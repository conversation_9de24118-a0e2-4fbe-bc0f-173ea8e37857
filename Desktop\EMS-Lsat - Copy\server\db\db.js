import mongoose from "mongoose";

const connectToDatabase = async () => {
    try {
        console.log("🔄 Tentative de connexion à MongoDB...");
        console.log("📍 URL MongoDB:", process.env.MONGODB_URL);

        await mongoose.connect(process.env.MONGODB_URL, {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });

        console.log("✅ Connexion à MongoDB réussie!");
        console.log("🗄️  Base de données:", mongoose.connection.name);

        // Événements de connexion
        mongoose.connection.on('error', (err) => {
            console.error("❌ Erreur MongoDB:", err);
        });

        mongoose.connection.on('disconnected', () => {
            console.log("⚠️  MongoDB déconnecté");
        });

        mongoose.connection.on('reconnected', () => {
            console.log("🔄 MongoDB reconnecté");
        });

    } catch (error) {
        console.error("❌ Erreur de connexion à MongoDB:", error.message);
        console.error("🔧 Vérifiez que MongoDB est démarré et que l'URL est correcte");
        process.exit(1);
    }
}

export default connectToDatabase;