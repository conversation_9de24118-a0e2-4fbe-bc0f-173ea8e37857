import mongoose from "mongoose";

const departementSchema = new mongoose.Schema({
  nom_departement: {
    type: String,
    required: true,
    unique: true,
    index: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  responsable: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Employe",
    validate: {
      validator: async function(value) {
        if (!value) return true; // Responsable optionnel

        // Vérifier que le responsable appartient à ce département
        const employe = await mongoose.model('Employe').findById(value);
        return !employe || employe.departement.toString() === this._id.toString();
      },
      message: 'Le responsable doit appartenir à ce département'
    }
  },
  is_active: {
    type: Boolean,
    default: true,
    index: true
  },

  // Informations supplémentaires
  code_departement: {
    type: String,
    unique: true,
    sparse: true,
    trim: true,
    uppercase: true
  },
  budget_annuel: {
    type: Number,
    min: 0,
    default: 0
  },
  localisation: {
    type: String,
    trim: true
  },

  // Métadonnées
  date_creation: {
    type: Date,
    default: Date.now
  },
  cree_par: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Utilisateur"
  }
}, {
  timestamps: true
});

// Index pour optimiser les recherches
departementSchema.index({ nom_departement: 'text', description: 'text' });
departementSchema.index({ is_active: 1, nom_departement: 1 });

// Méthode virtuelle pour obtenir le nombre d'employés
departementSchema.virtual('nombre_employes', {
  ref: 'Employe',
  localField: '_id',
  foreignField: 'departement',
  count: true
});

// Méthode pour obtenir tous les employés du département
departementSchema.methods.getEmployes = function() {
  return mongoose.model('Employe').find({
    departement: this._id,
    is_active: true
  }).populate('utilisateur', 'email role');
};

// Méthode pour obtenir les superviseurs potentiels (employés du département)
departementSchema.methods.getSuperviseursPotentiels = function() {
  return mongoose.model('Employe').find({
    departement: this._id,
    is_active: true,
    statut_compte: 'actif'
  }).select('_id nom prenom fonction email');
};

// Méthode statique pour obtenir les statistiques des départements
departementSchema.statics.getStatistiques = async function() {
  const stats = await this.aggregate([
    {
      $lookup: {
        from: 'employes',
        localField: '_id',
        foreignField: 'departement',
        as: 'employes'
      }
    },
    {
      $project: {
        nom_departement: 1,
        is_active: 1,
        nombre_employes: { $size: '$employes' },
        employes_actifs: {
          $size: {
            $filter: {
              input: '$employes',
              cond: { $eq: ['$$this.is_active', true] }
            }
          }
        }
      }
    }
  ]);

  return stats;
};

const Departement = mongoose.model("Departement", departementSchema);
export default Departement;