import React, { useState, useEffect } from 'react';

const FormationTest = () => {
  const [formations, setFormations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [token, setToken] = useState('');

  useEffect(() => {
    const storedToken = localStorage.getItem('token');
    setToken(storedToken || 'Aucun token');
    testAPI();
  }, []);

  const testAPI = async () => {
    try {
      setLoading(true);
      setError('');
      
      const storedToken = localStorage.getItem('token');
      console.log('🔑 Token utilisé:', storedToken);

      const response = await fetch('http://localhost:4000/api/formations', {
        headers: {
          'Authorization': `Bearer ${storedToken}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Statut de la réponse:', response.status);
      console.log('📡 Headers de la réponse:', response.headers);

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Données reçues:', data);
        setFormations(data.formations || []);
      } else {
        const errorData = await response.json();
        console.error('❌ Erreur API:', errorData);
        setError(`Erreur ${response.status}: ${errorData.message || 'Erreur inconnue'}`);
      }
    } catch (error) {
      console.error('❌ Erreur réseau:', error);
      setError(`Erreur de connexion: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '2rem', backgroundColor: 'white', borderRadius: '8px' }}>
      <h2>🧪 Test API Formations</h2>
      
      <div style={{ marginBottom: '2rem', padding: '1rem', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
        <h3>Informations de Debug</h3>
        <p><strong>Token:</strong> {token.substring(0, 50)}...</p>
        <p><strong>URL API:</strong> http://localhost:4000/api/formations</p>
        <p><strong>Statut:</strong> {loading ? 'Chargement...' : 'Terminé'}</p>
      </div>

      {error && (
        <div style={{
          backgroundColor: '#fee',
          color: '#c00',
          padding: '1rem',
          borderRadius: '8px',
          marginBottom: '1rem',
          border: '1px solid #fcc'
        }}>
          <strong>Erreur:</strong> {error}
        </div>
      )}

      <div style={{ marginBottom: '1rem' }}>
        <button 
          onClick={testAPI}
          style={{
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            padding: '0.5rem 1rem',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          🔄 Retester l'API
        </button>
      </div>

      <h3>Résultats ({formations.length} formations)</h3>
      
      {loading ? (
        <p>Chargement...</p>
      ) : formations.length === 0 ? (
        <p style={{ color: '#666' }}>Aucune formation trouvée</p>
      ) : (
        <div style={{ display: 'grid', gap: '1rem' }}>
          {formations.map((formation, index) => (
            <div 
              key={formation._id || index}
              style={{
                border: '1px solid #ddd',
                padding: '1rem',
                borderRadius: '8px',
                backgroundColor: '#f9f9f9'
              }}
            >
              <h4 style={{ margin: '0 0 0.5rem 0', color: '#333' }}>
                {formation.nom_formation || 'Nom non défini'}
              </h4>
              <p style={{ margin: '0 0 0.5rem 0', color: '#666' }}>
                <strong>Organisme:</strong> {formation.organisme || 'Non défini'}
              </p>
              <p style={{ margin: '0 0 0.5rem 0', color: '#666' }}>
                <strong>Type:</strong> {formation.type || 'Non défini'}
              </p>
              <p style={{ margin: '0 0 0.5rem 0', color: '#666' }}>
                <strong>Statut:</strong> {formation.statut || 'Non défini'}
              </p>
              <p style={{ margin: '0', color: '#666' }}>
                <strong>Description:</strong> {formation.description || 'Non définie'}
              </p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default FormationTest;
