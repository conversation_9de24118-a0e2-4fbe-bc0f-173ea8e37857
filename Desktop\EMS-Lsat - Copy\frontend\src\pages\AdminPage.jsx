import { Outlet } from "react-router-dom";
import AdminSidebar from "../components/dashboard/AdminSidebar.jsx";
import ModernNavbar from "../components/dashboard/ModernNavbar.jsx";
import { useAuth } from "../context/authContext.jsx";

const AdminPage = () => {
  const { utilisateur, loading } = useAuth();

  // Vérification de sécurité - s'assurer que l'utilisateur est bien admin
  if (!loading && utilisateur?.role !== 'admin') {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        backgroundColor: '#f7fafc',
        flexDirection: 'column',
        gap: '1rem'
      }}>
        <div style={{
          backgroundColor: '#fed7d7',
          color: '#c53030',
          padding: '1rem 2rem',
          borderRadius: '8px',
          border: '1px solid #feb2b2',
          textAlign: 'center'
        }}>
          <h2 style={{ margin: '0 0 0.5rem 0', fontSize: '1.5rem' }}>
            🚫 Accès Refusé
          </h2>
          <p style={{ margin: 0 }}>
            Vous n'avez pas les privilèges administrateur requis pour accéder à cette page.
          </p>
        </div>
        <button
          onClick={() => window.location.href = '/'}
          style={{
            backgroundColor: '#3182ce',
            color: 'white',
            padding: '0.75rem 1.5rem',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '1rem'
          }}
        >
          Retour à l'accueil
        </button>
      </div>
    );
  }

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        backgroundColor: '#f7fafc'
      }}>
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '1rem'
        }}>
          <div style={{
            width: '50px',
            height: '50px',
            border: '4px solid #e2e8f0',
            borderTop: '4px solid #3182ce',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }}></div>
          <p style={{ color: '#4a5568', fontSize: '1.1rem' }}>
            Chargement du panneau d'administration...
          </p>
        </div>
        <style>
          {`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `}
        </style>
      </div>
    );
  }

  return (
    <div style={{ display: "flex", flexDirection: "row" }}>
      {/* Admin Sidebar */}
      <AdminSidebar />

      {/* Main content area */}
      <div style={{
        flex: 1,
        marginLeft: "280px", // Largeur par défaut du sidebar ouvert
        transition: "margin-left 0.3s ease"
      }}>
        {/* Navbar */}
        <ModernNavbar />

        {/* Page content */}
        <div
          style={{
            padding: "2rem",
            marginTop: "4rem", // Matches the navbar height
            backgroundColor: "#f7fafc", // Light gray background for contrast
            minHeight: "calc(100vh - 4rem)", // Full height minus navbar
          }}
        >
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default AdminPage;
