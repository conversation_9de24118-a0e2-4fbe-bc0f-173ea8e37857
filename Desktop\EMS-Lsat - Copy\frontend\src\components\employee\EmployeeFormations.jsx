import React, { useState, useEffect } from 'react';
import {
  FaGraduationCap,
  FaCalendarAlt,
  FaMapMarkerAlt,
  FaUsers,
  FaClock,
  FaEye,
  FaHandPaper,
  FaCheckCircle,
  FaTimesCircle,
  FaHourglassHalf,
  FaSearch,
  FaFilter,
  FaPaperPlane,
  FaTrash
} from 'react-icons/fa';
import { useNotifications } from '../../contexts/NotificationContext';

const EmployeeFormations = ({ employeeData }) => {
  const [formations, setFormations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedFormation, setSelectedFormation] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showApplyModal, setShowApplyModal] = useState(false);
  const [applyMessage, setApplyMessage] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('');
  const [employeeCandidatures, setEmployeeCandidatures] = useState([]);

  // Hook pour les notifications toast
  const { showSuccess, showError, showWarning, showInfo } = useNotifications();

  // Récupérer les candidatures de l'employé
  const fetchEmployeeCandidatures = async () => {
    if (!employeeData?._id) return;

    try {
      // Utiliser la nouvelle route spécifique pour récupérer toutes les candidatures de l'employé
      const response = await fetch('http://localhost:4000/api/formations/mes-candidatures', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('📋 Mes candidatures:', data);
        setEmployeeCandidatures(data.candidatures || []);
      } else {
        console.error('Erreur lors du chargement des candidatures:', response.status);
      }
    } catch (error) {
      console.error('Erreur récupération candidatures employé:', error);
    }
  };

  // Vérifier si l'employé a déjà postulé à une formation
  const hasAlreadyApplied = (formationId) => {
    return employeeCandidatures.some(candidature =>
      candidature.formation_id._id === formationId
    );
  };

  // Obtenir le statut de la candidature pour une formation
  const getCandidatureStatus = (formationId) => {
    const candidature = employeeCandidatures.find(candidature =>
      candidature.formation_id._id === formationId
    );
    return candidature ? candidature.statut : null;
  };

  // Obtenir les détails complets de la candidature pour une formation
  const getCandidatureDetails = (formationId) => {
    const candidature = employeeCandidatures.find(candidature =>
      candidature.formation_id._id === formationId
    );
    return candidature || null;
  };

  // Supprimer une candidature
  const supprimerCandidature = async (candidatureId, formationNom) => {
    if (!window.confirm(`Êtes-vous sûr de vouloir supprimer votre candidature pour "${formationNom}" ?`)) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:4000/api/formations/mes-candidatures/${candidatureId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Candidature supprimée:', data);

        // Recharger les candidatures et formations
        await fetchEmployeeCandidatures();
        await fetchFormations();

        alert('Candidature supprimée avec succès');
      } else {
        const errorData = await response.json();
        alert(errorData.message || 'Erreur lors de la suppression');
      }
    } catch (error) {
      console.error('❌ Erreur suppression candidature:', error);
      alert('Erreur de connexion');
    }
  };

  // Charger les formations et candidatures
  useEffect(() => {
    fetchFormations();
    fetchEmployeeCandidatures();
  }, [employeeData]);

  const fetchFormations = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:4000/api/formations', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setFormations(data.formations || []);
      } else {
        setError('Erreur lors du chargement des formations');
      }
    } catch (error) {
      console.error('Erreur:', error);
      setError('Erreur de connexion');
    } finally {
      setLoading(false);
    }
  };

  // Postuler à une formation
  const handleApply = async () => {
    if (!employeeData?._id) {
      showError('Erreur', 'Informations employé manquantes');
      return;
    }

    try {
      // Afficher une notification d'information pendant l'envoi
      showInfo('Envoi en cours...', `Envoi de votre candidature pour "${selectedFormation.nom_formation}"`);

      const response = await fetch(`http://localhost:4000/api/formations/${selectedFormation._id}/candidature`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          employe_id: employeeData._id,
          message_employe: applyMessage
        })
      });

      const data = await response.json();

      if (response.ok) {
        // Notification de succès avec détails
        showSuccess(
          '🎉 Candidature envoyée !',
          `Votre candidature pour "${selectedFormation.nom_formation}" a été envoyée avec succès. RH sera notifié et vous recevrez une réponse prochainement.`,
          { duration: 6000 }
        );

        // Fermer la modal et réinitialiser
        setShowApplyModal(false);
        setApplyMessage('');
        setSelectedFormation(null);

        // Recharger les candidatures pour mettre à jour l'affichage
        fetchEmployeeCandidatures();
      } else {
        // Gestion spécifique des erreurs selon le type
        if (response.status === 400 && data.message?.includes('déjà postulé')) {
          showWarning(
            'Candidature déjà envoyée',
            `Vous avez déjà postulé à la formation "${selectedFormation.nom_formation}". Vous ne pouvez postuler qu'une seule fois par formation.`,
            { duration: 7000 }
          );
        } else {
          // Autres erreurs
          showError(
            'Erreur lors de l\'envoi',
            data.message || 'Une erreur est survenue lors de l\'envoi de votre candidature'
          );
        }
      }
    } catch (error) {
      console.error('Erreur:', error);
      showError(
        'Erreur de connexion',
        'Impossible de se connecter au serveur. Vérifiez votre connexion internet.'
      );
    }
  };

  // Filtrer les formations
  const filteredFormations = formations.filter(formation => {
    const matchesSearch = formation.nom_formation.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         formation.organisme.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === '' || formation.type === filterType;
    return matchesSearch && matchesType;
  });

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '50vh',
        fontSize: '1.2rem',
        color: '#6b7280'
      }}>
        Chargement des formations...
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        backgroundColor: '#fef2f2',
        color: '#dc2626',
        padding: '1rem',
        margin: '1rem',
        borderRadius: '8px',
        border: '1px solid #fecaca'
      }}>
        {error}
      </div>
    );
  }

  return (
    <div style={{ padding: '2rem', backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '2rem'
      }}>
        <div>
          <h1 style={{ margin: 0, fontSize: '2rem', fontWeight: '700', color: '#1f2937' }}>
            Formations Disponibles
          </h1>
          <p style={{ margin: '0.5rem 0 0 0', color: '#6b7280' }}>
            Découvrez et postulez aux formations proposées par votre entreprise
          </p>
        </div>
      </div>

      {/* Filtres */}
      <div style={{
        display: 'flex',
        gap: '1rem',
        marginBottom: '2rem',
        flexWrap: 'wrap'
      }}>
        {/* Recherche */}
        <div style={{ position: 'relative', flex: '1', minWidth: '300px' }}>
          <FaSearch style={{
            position: 'absolute',
            left: '1rem',
            top: '50%',
            transform: 'translateY(-50%)',
            color: '#6b7280'
          }} />
          <input
            type="text"
            placeholder="Rechercher une formation..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              width: '100%',
              padding: '0.75rem 1rem 0.75rem 2.5rem',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              fontSize: '1rem'
            }}
          />
        </div>

        {/* Filtre par type */}
        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value)}
          style={{
            padding: '0.75rem 1rem',
            border: '1px solid #d1d5db',
            borderRadius: '8px',
            fontSize: '1rem',
            backgroundColor: 'white'
          }}
        >
          <option value="">Tous les types</option>
          <option value="interne">Interne</option>
          <option value="externe">Externe</option>
        </select>
      </div>

      {/* Liste des formations */}
      {filteredFormations.length === 0 ? (
        <div style={{
          textAlign: 'center',
          padding: '3rem',
          backgroundColor: 'white',
          borderRadius: '12px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <FaGraduationCap style={{ fontSize: '3rem', color: '#d1d5db', marginBottom: '1rem' }} />
          <h3 style={{ color: '#6b7280', margin: '0 0 0.5rem 0' }}>Aucune formation trouvée</h3>
          <p style={{ color: '#9ca3af', margin: 0 }}>
            {searchTerm || filterType ? 'Essayez de modifier vos critères de recherche' : 'Aucune formation disponible pour le moment'}
          </p>
        </div>
      ) : (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(400px, 1fr))',
          gap: '1.5rem'
        }}>
          {filteredFormations.map((formation) => (
            <FormationCard
              key={formation._id}
              formation={formation}
              hasAlreadyApplied={hasAlreadyApplied(formation._id)}
              candidatureStatus={getCandidatureStatus(formation._id)}
              candidatureDetails={getCandidatureDetails(formation._id)}
              onViewDetails={() => {
                setSelectedFormation(formation);
                setShowDetailsModal(true);
              }}
              onApply={() => {
                setSelectedFormation(formation);
                setShowApplyModal(true);
              }}
              onDeleteCandidature={supprimerCandidature}
            />
          ))}
        </div>
      )}

      {/* Modal détails formation */}
      {showDetailsModal && selectedFormation && (
        <FormationDetailsModal
          formation={selectedFormation}
          onClose={() => {
            setShowDetailsModal(false);
            setSelectedFormation(null);
          }}
          onApply={() => {
            setShowDetailsModal(false);
            setShowApplyModal(true);
          }}
        />
      )}

      {/* Modal candidature */}
      {showApplyModal && selectedFormation && (
        <ApplyModal
          formation={selectedFormation}
          message={applyMessage}
          setMessage={setApplyMessage}
          onApply={handleApply}
          onClose={() => {
            setShowApplyModal(false);
            setSelectedFormation(null);
            setApplyMessage('');
          }}
        />
      )}
    </div>
  );
};

// Composant carte formation
const FormationCard = ({ formation, hasAlreadyApplied, candidatureStatus, candidatureDetails, onViewDetails, onApply, onDeleteCandidature }) => {
  const getStatusColor = (statut) => {
    switch (statut) {
      case 'planifiee': return '#3b82f6';
      case 'en_cours': return '#f59e0b';
      case 'terminee': return '#10b981';
      case 'annulee': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getStatusLabel = (statut) => {
    switch (statut) {
      case 'planifiee': return 'Planifiée';
      case 'en_cours': return 'En cours';
      case 'terminee': return 'Terminée';
      case 'annulee': return 'Annulée';
      default: return statut;
    }
  };

  // Fonctions pour gérer le statut de candidature
  const getCandidatureStatusColor = (status) => {
    switch (status) {
      case 'en_attente': return '#f59e0b';
      case 'approuvee': return '#10b981';
      case 'refusee': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getCandidatureStatusLabel = (status) => {
    switch (status) {
      case 'en_attente': return 'En attente';
      case 'approuvee': return 'Approuvée';
      case 'refusee': return 'Refusée';
      default: return status;
    }
  };

  const getCandidatureStatusIcon = (status) => {
    switch (status) {
      case 'en_attente': return <FaHourglassHalf />;
      case 'approuvee': return <FaCheckCircle />;
      case 'refusee': return <FaTimesCircle />;
      default: return null;
    }
  };

  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      padding: '1.5rem',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
      border: '1px solid #e5e7eb',
      transition: 'transform 0.2s, box-shadow 0.2s',
      cursor: 'pointer'
    }}
    onMouseEnter={(e) => {
      e.currentTarget.style.transform = 'translateY(-2px)';
      e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
    }}
    onMouseLeave={(e) => {
      e.currentTarget.style.transform = 'translateY(0)';
      e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
    }}>
      {/* Header */}
      <div style={{ marginBottom: '1rem' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '0.5rem' }}>
          <h3 style={{ margin: 0, fontSize: '1.25rem', fontWeight: '600', color: '#1f2937' }}>
            {formation.nom_formation}
          </h3>
          <div style={{ display: 'flex', gap: '0.5rem', flexDirection: 'column', alignItems: 'flex-end' }}>
            <span style={{
              backgroundColor: getStatusColor(formation.statut),
              color: 'white',
              padding: '0.25rem 0.75rem',
              borderRadius: '12px',
              fontSize: '0.75rem',
              fontWeight: '500'
            }}>
              {getStatusLabel(formation.statut)}
            </span>
            {candidatureStatus && (
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem', alignItems: 'flex-end' }}>
                <span style={{
                  backgroundColor: getCandidatureStatusColor(candidatureStatus),
                  color: 'white',
                  padding: '0.25rem 0.75rem',
                  borderRadius: '12px',
                  fontSize: '0.75rem',
                  fontWeight: '500',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.25rem'
                }}>
                  {getCandidatureStatusIcon(candidatureStatus)}
                  {getCandidatureStatusLabel(candidatureStatus)}
                </span>
                {candidatureDetails?.date_candidature && (
                  <span style={{
                    fontSize: '0.625rem',
                    color: '#6b7280',
                    fontStyle: 'italic'
                  }}>
                    Postulé le {new Date(candidatureDetails.date_candidature).toLocaleDateString('fr-FR')}
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
        <p style={{ margin: 0, color: '#6b7280', fontSize: '0.875rem' }}>
          {formation.organisme}
        </p>
      </div>

      {/* Informations */}
      <div style={{ marginBottom: '1rem' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
          <FaCalendarAlt style={{ color: '#6b7280', fontSize: '0.875rem' }} />
          <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>
            {new Date(formation.date_debut).toLocaleDateString()} - {new Date(formation.date_fin).toLocaleDateString()}
          </span>
        </div>
        {formation.lieu && (
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
            <FaMapMarkerAlt style={{ color: '#6b7280', fontSize: '0.875rem' }} />
            <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>
              {formation.lieu}
            </span>
          </div>
        )}
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <FaUsers style={{ color: '#6b7280', fontSize: '0.875rem' }} />
          <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>
            Capacité: {formation.capacite_max} participants
          </span>
        </div>
      </div>

      {/* Description */}
      <p style={{
        margin: '0 0 1rem 0',
        color: '#4b5563',
        fontSize: '0.875rem',
        lineHeight: '1.5',
        display: '-webkit-box',
        WebkitLineClamp: 3,
        WebkitBoxOrient: 'vertical',
        overflow: 'hidden'
      }}>
        {formation.description}
      </p>

      {/* Message RH si candidature traitée */}
      {candidatureDetails?.message_rh && (candidatureStatus === 'approuvee' || candidatureStatus === 'refusee') && (
        <div style={{
          backgroundColor: candidatureStatus === 'approuvee' ? '#f0fdf4' : '#fef2f2',
          border: `1px solid ${candidatureStatus === 'approuvee' ? '#bbf7d0' : '#fecaca'}`,
          borderRadius: '8px',
          padding: '1rem',
          margin: '1rem 0'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            marginBottom: '0.5rem'
          }}>
            {candidatureStatus === 'approuvee' ? (
              <FaCheckCircle style={{ color: '#16a34a' }} />
            ) : (
              <FaTimesCircle style={{ color: '#dc2626' }} />
            )}
            <span style={{
              fontWeight: '600',
              fontSize: '0.875rem',
              color: candidatureStatus === 'approuvee' ? '#16a34a' : '#dc2626'
            }}>
              Réponse RH:
            </span>
          </div>
          <p style={{
            margin: 0,
            fontSize: '0.875rem',
            color: '#374151',
            fontStyle: 'italic'
          }}>
            "{candidatureDetails.message_rh}"
          </p>
          {candidatureDetails.date_reponse && (
            <p style={{
              margin: '0.5rem 0 0 0',
              fontSize: '0.75rem',
              color: '#6b7280'
            }}>
              Répondu le {new Date(candidatureDetails.date_reponse).toLocaleDateString('fr-FR', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </p>
          )}
        </div>
      )}

      {/* Actions */}
      <div style={{ display: 'flex', gap: '0.5rem' }}>
        <button
          onClick={(e) => {
            e.stopPropagation();
            onViewDetails();
          }}
          style={{
            flex: 1,
            padding: '0.5rem 1rem',
            border: '1px solid #3b82f6',
            backgroundColor: 'transparent',
            color: '#3b82f6',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '0.875rem',
            fontWeight: '500',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '0.5rem'
          }}
        >
          <FaEye />
          Détails
        </button>

        {/* Bouton conditionnel selon le statut de candidature */}
        {!hasAlreadyApplied ? (
          <button
            onClick={(e) => {
              e.stopPropagation();
              onApply();
            }}
            style={{
              flex: 1,
              padding: '0.5rem 1rem',
              border: 'none',
              backgroundColor: '#10b981',
              color: 'white',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '0.875rem',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '0.5rem'
            }}
          >
            <FaHandPaper />
            Postuler
          </button>
        ) : candidatureStatus === 'en_attente' ? (
          // Candidature en attente - afficher bouton statut + bouton supprimer
          <>
            <button
              disabled
              style={{
                flex: 1,
                padding: '0.5rem 1rem',
                border: 'none',
                backgroundColor: '#f59e0b',
                color: 'white',
                borderRadius: '6px',
                cursor: 'not-allowed',
                fontSize: '0.875rem',
                fontWeight: '500',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '0.5rem'
              }}
            >
              {getCandidatureStatusIcon(candidatureStatus)}
              En attente
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDeleteCandidature(candidatureDetails._id, formation.nom_formation);
              }}
              style={{
                padding: '0.5rem 1rem',
                border: 'none',
                backgroundColor: '#ef4444',
                color: 'white',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '0.875rem',
                fontWeight: '500',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '0.5rem'
              }}
            >
              <FaTrash />
              Annuler
            </button>
          </>
        ) : (
          // Candidature approuvée ou refusée - afficher seulement le statut
          <button
            disabled
            style={{
              flex: 1,
              padding: '0.5rem 1rem',
              border: 'none',
              backgroundColor: candidatureStatus === 'approuvee' ? '#10b981' : '#ef4444',
              color: 'white',
              borderRadius: '6px',
              cursor: 'not-allowed',
              fontSize: '0.875rem',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '0.5rem'
            }}
          >
            {getCandidatureStatusIcon(candidatureStatus)}
            {candidatureStatus === 'approuvee' ? 'Approuvée' : 'Refusée'}
          </button>
        )}
      </div>
    </div>
  );
};

// Modal détails formation
const FormationDetailsModal = ({ formation, onClose, onApply }) => {
  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '1rem'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '2rem',
        width: '100%',
        maxWidth: '600px',
        maxHeight: '90vh',
        overflowY: 'auto'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '1.5rem' }}>
          <h2 style={{ margin: 0, fontSize: '1.5rem', fontWeight: '700', color: '#1f2937' }}>
            {formation.nom_formation}
          </h2>
          <button
            onClick={onClose}
            style={{
              backgroundColor: 'transparent',
              border: 'none',
              fontSize: '1.5rem',
              cursor: 'pointer',
              color: '#6b7280'
            }}
          >
            ×
          </button>
        </div>

        <div style={{ marginBottom: '1.5rem' }}>
          <h3 style={{ margin: '0 0 0.5rem 0', color: '#374151' }}>Organisme</h3>
          <p style={{ margin: 0, color: '#6b7280' }}>{formation.organisme}</p>
        </div>

        <div style={{ marginBottom: '1.5rem' }}>
          <h3 style={{ margin: '0 0 0.5rem 0', color: '#374151' }}>Description</h3>
          <p style={{ margin: 0, color: '#6b7280', lineHeight: '1.6' }}>{formation.description}</p>
        </div>

        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem', marginBottom: '1.5rem' }}>
          <div>
            <h3 style={{ margin: '0 0 0.5rem 0', color: '#374151' }}>Date de début</h3>
            <p style={{ margin: 0, color: '#6b7280' }}>{new Date(formation.date_debut).toLocaleDateString()}</p>
          </div>
          <div>
            <h3 style={{ margin: '0 0 0.5rem 0', color: '#374151' }}>Date de fin</h3>
            <p style={{ margin: 0, color: '#6b7280' }}>{new Date(formation.date_fin).toLocaleDateString()}</p>
          </div>
        </div>

        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem', marginBottom: '1.5rem' }}>
          <div>
            <h3 style={{ margin: '0 0 0.5rem 0', color: '#374151' }}>Type</h3>
            <p style={{ margin: 0, color: '#6b7280' }}>{formation.type === 'interne' ? 'Interne' : 'Externe'}</p>
          </div>
          <div>
            <h3 style={{ margin: '0 0 0.5rem 0', color: '#374151' }}>Modalité</h3>
            <p style={{ margin: 0, color: '#6b7280' }}>
              {formation.modalite === 'presentielle' ? 'Présentielle' :
               formation.modalite === 'en_ligne' ? 'En ligne' : 'Mixte'}
            </p>
          </div>
        </div>

        {formation.lieu && (
          <div style={{ marginBottom: '1.5rem' }}>
            <h3 style={{ margin: '0 0 0.5rem 0', color: '#374151' }}>Lieu</h3>
            <p style={{ margin: 0, color: '#6b7280' }}>{formation.lieu}</p>
          </div>
        )}

        <div style={{ marginBottom: '1.5rem' }}>
          <h3 style={{ margin: '0 0 0.5rem 0', color: '#374151' }}>Capacité</h3>
          <p style={{ margin: 0, color: '#6b7280' }}>{formation.capacite_max} participants maximum</p>
        </div>

        {formation.certifiante && (
          <div style={{ marginBottom: '1.5rem' }}>
            <span style={{
              backgroundColor: '#10b981',
              color: 'white',
              padding: '0.5rem 1rem',
              borderRadius: '20px',
              fontSize: '0.875rem',
              fontWeight: '500'
            }}>
              ✓ Formation certifiante
            </span>
          </div>
        )}

        <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end' }}>
          <button
            onClick={onClose}
            style={{
              padding: '0.75rem 1.5rem',
              border: '1px solid #d1d5db',
              backgroundColor: 'white',
              color: '#374151',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '1rem',
              fontWeight: '500'
            }}
          >
            Fermer
          </button>
          <button
            onClick={onApply}
            style={{
              padding: '0.75rem 1.5rem',
              border: 'none',
              backgroundColor: '#10b981',
              color: 'white',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '1rem',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
          >
            <FaHandPaper />
            Postuler
          </button>
        </div>
      </div>
    </div>
  );
};

// Modal candidature
const ApplyModal = ({ formation, message, setMessage, onApply, onClose }) => {
  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '1rem'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '2rem',
        width: '100%',
        maxWidth: '500px'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '1.5rem' }}>
          <h2 style={{ margin: 0, fontSize: '1.5rem', fontWeight: '700', color: '#1f2937' }}>
            Postuler à la formation
          </h2>
          <button
            onClick={onClose}
            style={{
              backgroundColor: 'transparent',
              border: 'none',
              fontSize: '1.5rem',
              cursor: 'pointer',
              color: '#6b7280'
            }}
          >
            ×
          </button>
        </div>

        <div style={{ marginBottom: '1.5rem' }}>
          <h3 style={{ margin: '0 0 0.5rem 0', color: '#374151' }}>Formation</h3>
          <p style={{ margin: 0, color: '#6b7280', fontWeight: '500' }}>{formation.nom_formation}</p>
        </div>

        <div style={{ marginBottom: '1.5rem' }}>
          <label style={{ display: 'block', marginBottom: '0.5rem', color: '#374151', fontWeight: '500' }}>
            Message de motivation (optionnel)
          </label>
          <textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Expliquez pourquoi vous souhaitez suivre cette formation..."
            style={{
              width: '100%',
              height: '120px',
              padding: '0.75rem',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              fontSize: '1rem',
              resize: 'vertical'
            }}
          />
        </div>

        <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end' }}>
          <button
            onClick={onClose}
            style={{
              padding: '0.75rem 1.5rem',
              border: '1px solid #d1d5db',
              backgroundColor: 'white',
              color: '#374151',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '1rem',
              fontWeight: '500'
            }}
          >
            Annuler
          </button>
          <button
            onClick={onApply}
            style={{
              padding: '0.75rem 1.5rem',
              border: 'none',
              backgroundColor: '#10b981',
              color: 'white',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '1rem',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
          >
            <FaHandPaper />
            Envoyer ma candidature
          </button>
        </div>
      </div>
    </div>
  );
};

export default EmployeeFormations;
