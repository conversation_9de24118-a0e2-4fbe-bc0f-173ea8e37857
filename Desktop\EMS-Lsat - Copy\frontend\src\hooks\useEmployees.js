/**
 * Hook personnalisé pour la gestion des employés
 */
import { useState, useEffect, useCallback } from 'react';
import { employeeService } from '../services/employeeService';

export const useEmployees = () => {
    const [employees, setEmployees] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchEmployees = useCallback(async () => {
        setLoading(true);
        setError(null);
        
        try {
            const response = await employeeService.getAllEmployees();
            if (response.success) {
                setEmployees(response.data.employees || []);
            } else {
                setError(response.message || 'Erreur lors du chargement des employés');
            }
        } catch (error) {
            setError(error.message || 'Erreur lors du chargement des employés');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchEmployees();
    }, [fetchEmployees]);

    const createEmployee = async (employeeData) => {
        try {
            const response = await employeeService.createEmployee(employeeData);
            if (response.success) {
                await fetchEmployees(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de la création');
            }
        } catch (error) {
            throw error;
        }
    };

    const updateEmployee = async (id, employeeData) => {
        try {
            const response = await employeeService.updateEmployee(id, employeeData);
            if (response.success) {
                await fetchEmployees(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de la mise à jour');
            }
        } catch (error) {
            throw error;
        }
    };

    const deleteEmployee = async (id) => {
        try {
            const response = await employeeService.deleteEmployee(id);
            if (response.success) {
                await fetchEmployees(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de la suppression');
            }
        } catch (error) {
            throw error;
        }
    };

    return {
        employees,
        loading,
        error,
        fetchEmployees,
        createEmployee,
        updateEmployee,
        deleteEmployee
    };
};

/**
 * Hook pour obtenir un employé spécifique
 */
export const useEmployee = (id) => {
    const [employee, setEmployee] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchEmployee = useCallback(async () => {
        if (!id) return;
        
        setLoading(true);
        setError(null);
        
        try {
            const response = await employeeService.getEmployeeById(id);
            if (response.success) {
                setEmployee(response.data.employee);
            } else {
                setError(response.message || 'Employé non trouvé');
            }
        } catch (error) {
            setError(error.message || 'Erreur lors du chargement de l\'employé');
        } finally {
            setLoading(false);
        }
    }, [id]);

    useEffect(() => {
        fetchEmployee();
    }, [fetchEmployee]);

    return { employee, loading, error, refetch: fetchEmployee };
};

/**
 * Hook pour obtenir les informations de l'employé connecté
 */
export const useMyInfo = () => {
    const [myInfo, setMyInfo] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchMyInfo = useCallback(async () => {
        setLoading(true);
        setError(null);
        
        try {
            const response = await employeeService.getMyInfo();
            if (response.success) {
                setMyInfo(response.data.employee);
            } else {
                setError(response.message || 'Erreur lors du chargement de vos informations');
            }
        } catch (error) {
            setError(error.message || 'Erreur lors du chargement de vos informations');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchMyInfo();
    }, [fetchMyInfo]);

    return { myInfo, loading, error, refetch: fetchMyInfo };
};
