import mongoose from "mongoose";

const tacheSchema = new mongoose.Schema({
  nom_tache: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200,
    index: true
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000
  },
  date_debut: {
    type: Date,
    required: true,
    index: true
  },
  date_fin_prevue: {
    type: Date,
    required: true,
    validate: {
      validator: function(value) {
        return value > this.date_debut;
      },
      message: 'La date de fin prévue doit être postérieure à la date de début'
    }
  },
  date_fin_reelle: {
    type: Date,
    validate: {
      validator: function(value) {
        return !value || value >= this.date_debut;
      },
      message: 'La date de fin réelle doit être postérieure à la date de début'
    }
  },
  statut: {
    type: String,
    enum: ['Non commencée', 'En cours', 'En pause', 'Terminée', 'Annulée'],
    default: 'Non commencée',
    required: true,
    index: true
  },
  priorite: {
    type: String,
    enum: ['Faible', 'Moyenne', 'Haute', 'Urgente'],
    default: 'Moyenne',
    required: true,
    index: true
  },
  progression: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  temps_estime: {
    type: Number, // en heures
    min: 0,
    default: 0
  },
  temps_passe: {
    type: Number, // en heures
    min: 0,
    default: 0
  },
  commentaires: [{
    auteur: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Employe",
      required: true
    },
    contenu: {
      type: String,
      required: true,
      maxlength: 500
    },
    date_creation: {
      type: Date,
      default: Date.now
    }
  }],
  projet: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Projet",
    required: true,
    index: true
  },
  employe_assigne: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Employe",
    required: true,
    index: true
  },
  dependances: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: "Tache"
  }],
  tags: [{
    type: String,
    trim: true
  }],
  fichiers_attaches: [{
    nom: String,
    url: String,
    taille: Number,
    date_upload: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true
});

// Index composés
tacheSchema.index({ projet: 1, statut: 1 });
tacheSchema.index({ employe_assigne: 1, statut: 1 });
tacheSchema.index({ date_debut: 1, date_fin_prevue: 1 });

// Méthodes d'instance
tacheSchema.methods.estEnRetard = function() {
  if (this.statut === 'Terminée' || this.statut === 'Annulée') return false;
  return new Date() > this.date_fin_prevue;
};

tacheSchema.methods.joursRestants = function() {
  if (this.statut === 'Terminée' || this.statut === 'Annulée') return 0;

  const maintenant = new Date();
  const dateFin = this.date_fin_reelle || this.date_fin_prevue;
  const diffTime = dateFin - maintenant;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return Math.max(0, diffDays);
};

tacheSchema.methods.calculerProgressionTemps = function() {
  if (this.temps_estime === 0) return 0;
  return Math.min(100, Math.round((this.temps_passe / this.temps_estime) * 100));
};

// Middleware pre-save
tacheSchema.pre('save', function(next) {
  // Mettre à jour la date de fin réelle si terminée
  if (this.statut === 'Terminée' && !this.date_fin_reelle) {
    this.date_fin_reelle = new Date();
    this.progression = 100;
  }

  // Réinitialiser la date de fin réelle si plus terminée
  if (this.statut !== 'Terminée' && this.date_fin_reelle) {
    this.date_fin_reelle = undefined;
  }

  // Ajuster la progression selon le statut
  if (this.statut === 'Non commencée' && this.progression > 0) {
    this.progression = 0;
  }
  if (this.statut === 'Terminée' && this.progression < 100) {
    this.progression = 100;
  }

  next();
});

const Tache = mongoose.model("Tache", tacheSchema);
export default Tache;
