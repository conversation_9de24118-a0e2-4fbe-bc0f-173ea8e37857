/**
 * Service pour la gestion des contrats
 */
import api from './api';

export const contratService = {
    /**
     * Obtenir tous les contrats
     * @param {Object} params - Paramètres de pagination et filtres
     * @returns {Promise} Liste des contrats
     */
    getAllContrats: async (params = {}) => {
        try {
            const { page = 1, limit = 10, type_contrat, statut, employe } = params;
            let url = `/contrats?page=${page}&limit=${limit}`;
            if (type_contrat) url += `&type_contrat=${type_contrat}`;
            if (statut) url += `&statut=${statut}`;
            if (employe) url += `&employe=${employe}`;
            
            const response = await api.get(url);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la récupération des contrats' };
        }
    },

    /**
     * Obtenir un contrat par ID
     * @param {string} id - ID du contrat
     * @returns {Promise} Données du contrat
     */
    getContratById: async (id) => {
        try {
            const response = await api.get(`/contrats/${id}`);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la récupération du contrat' };
        }
    },

    /**
     * Créer un nouveau contrat
     * @param {Object} contratData - Données du contrat
     * @returns {Promise} Contrat créé
     */
    createContrat: async (contratData) => {
        try {
            const response = await api.post('/contrats', contratData);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la création du contrat' };
        }
    },

    /**
     * Mettre à jour un contrat
     * @param {string} id - ID du contrat
     * @param {Object} contratData - Nouvelles données du contrat
     * @returns {Promise} Contrat mis à jour
     */
    updateContrat: async (id, contratData) => {
        try {
            const response = await api.put(`/contrats/${id}`, contratData);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la mise à jour du contrat' };
        }
    },

    /**
     * Supprimer un contrat
     * @param {string} id - ID du contrat
     * @returns {Promise} Confirmation de suppression
     */
    deleteContrat: async (id) => {
        try {
            const response = await api.delete(`/contrats/${id}`);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la suppression du contrat' };
        }
    },

    /**
     * Obtenir les statistiques des contrats
     * @returns {Promise} Statistiques
     */
    getContratStats: async () => {
        try {
            const response = await api.get('/contrats/stats');
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la récupération des statistiques' };
        }
    },

    /**
     * Obtenir les contrats d'un employé spécifique
     * @param {string} employeId - ID de l'employé
     * @returns {Promise} Contrats de l'employé
     */
    getContratsByEmploye: async (employeId) => {
        try {
            const response = await api.get(`/contrats/employe/${employeId}`);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la récupération des contrats' };
        }
    },

    /**
     * Obtenir les types de contrats disponibles
     * @returns {Array} Liste des types de contrats
     */
    getTypesContrat: () => {
        return [
            { value: 'CDI', label: 'CDI - Contrat à Durée Indéterminée' },
            { value: 'CDD', label: 'CDD - Contrat à Durée Déterminée' },
            { value: 'Stage', label: 'Stage' },
            { value: 'Freelance', label: 'Freelance' },
            { value: 'Apprentissage', label: 'Apprentissage' },
            { value: 'Interim', label: 'Intérim' }
        ];
    },

    /**
     * Obtenir les statuts de contrats disponibles
     * @returns {Array} Liste des statuts
     */
    getStatutsContrat: () => {
        return [
            { value: 'Actif', label: 'Actif', color: '#10b981' },
            { value: 'Terminé', label: 'Terminé', color: '#6b7280' },
            { value: 'Suspendu', label: 'Suspendu', color: '#f59e0b' },
            { value: 'Annulé', label: 'Annulé', color: '#ef4444' }
        ];
    },

    /**
     * Formater une date pour l'affichage
     * @param {string|Date} date - Date à formater
     * @returns {string} Date formatée
     */
    formatDate: (date) => {
        if (!date) return 'Non définie';
        return new Date(date).toLocaleDateString('fr-FR', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    },

    /**
     * Formater un salaire pour l'affichage
     * @param {number} salaire - Salaire à formater
     * @returns {string} Salaire formaté
     */
    formatSalaire: (salaire) => {
        if (!salaire) return '0 €';
        return new Intl.NumberFormat('fr-FR', {
            style: 'currency',
            currency: 'EUR'
        }).format(salaire);
    },

    /**
     * Calculer la durée d'un contrat
     * @param {string|Date} dateDebut - Date de début
     * @param {string|Date} dateFin - Date de fin
     * @returns {string} Durée formatée
     */
    calculateDuree: (dateDebut, dateFin) => {
        if (!dateFin) return 'Indéterminée (CDI)';
        
        const debut = new Date(dateDebut);
        const fin = new Date(dateFin);
        const diffTime = Math.abs(fin - debut);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays < 30) {
            return `${diffDays} jour${diffDays > 1 ? 's' : ''}`;
        } else if (diffDays < 365) {
            const months = Math.floor(diffDays / 30);
            return `${months} mois`;
        } else {
            const years = Math.floor(diffDays / 365);
            const remainingMonths = Math.floor((diffDays % 365) / 30);
            return `${years} an${years > 1 ? 's' : ''}${remainingMonths > 0 ? ` et ${remainingMonths} mois` : ''}`;
        }
    },

    /**
     * Vérifier si un contrat est actif
     * @param {Object} contrat - Contrat à vérifier
     * @returns {boolean} True si le contrat est actif
     */
    isContratActif: (contrat) => {
        if (contrat.statut !== 'Actif') return false;
        
        const now = new Date();
        const dateDebut = new Date(contrat.date_debut);
        const dateFin = contrat.date_fin ? new Date(contrat.date_fin) : null;
        
        return dateDebut <= now && (!dateFin || dateFin >= now);
    },

    /**
     * Vérifier si un contrat expire bientôt (dans les 30 jours)
     * @param {Object} contrat - Contrat à vérifier
     * @returns {boolean} True si le contrat expire bientôt
     */
    isContratExpirantBientot: (contrat) => {
        if (!contrat.date_fin || contrat.statut !== 'Actif') return false;
        
        const now = new Date();
        const dateFin = new Date(contrat.date_fin);
        const diffTime = dateFin - now;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        return diffDays > 0 && diffDays <= 30;
    }
};
