import { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { useAuth } from '../../context/authContext';
import {
  FaHome,
  FaUsers,
  FaCalendarAlt,
  FaFileAlt,
  FaTasks,
  FaGraduationCap,
  FaClock,
  FaEnvelope,
  FaFolder,
  FaBuilding,
  FaMoneyBillWave,
  FaProjectDiagram,
  FaFileContract,
  FaAward,
  FaChevronRight,
  FaChevronDown,
  FaUserTie,
  FaBars,
  FaTimes,
  FaChartBar,
  FaClipboardList
} from 'react-icons/fa';

const RhSidebar = () => {
  const { utilisateur } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [expandedCategories, setExpandedCategories] = useState({
    personnel: true,
    paie: false,
    formation: false,
    projets: false,
    documents: false
  });

  // Fonction pour basculer l'expansion d'une catégorie
  const toggleCategory = (category) => {
    setExpandedCategories(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  // Structure organisée par catégories
  const menuCategories = [
    {
      id: 'dashboard',
      label: 'Vue d\'ensemble',
      icon: FaHome,
      type: 'single',
      path: '/RHpage',
      exact: true
    },
    {
      id: 'personnel',
      label: 'Gestion du Personnel',
      icon: FaUsers,
      type: 'category',
      items: [
        {
          path: '/RHpage/employes',
          icon: FaUsers,
          label: 'Employés'
        },
        {
          path: '/RHpage/departements',
          icon: FaBuilding,
          label: 'Départements'
        },
        {
          path: '/RHpage/conges',
          icon: FaCalendarAlt,
          label: 'Congés'
        },
        {
          path: '/RHpage/pointage',
          icon: FaClock,
          label: 'Pointage'
        }
      ]
    },
    {
      id: 'paie',
      label: 'Paie & Rémunération',
      icon: FaMoneyBillWave,
      type: 'category',
      items: [
        {
          path: '/RHpage/salaires',
          icon: FaMoneyBillWave,
          label: 'Salaires'
        },
        {
          path: '/RHpage/primes',
          icon: FaAward,
          label: 'Primes'
        },
        {
          path: '/RHpage/contrats',
          icon: FaFileContract,
          label: 'Contrats'
        }
      ]
    },
    {
      id: 'formation',
      label: 'Formation & Développement',
      icon: FaGraduationCap,
      type: 'category',
      items: [
        {
          path: '/RHpage/formations',
          icon: FaGraduationCap,
          label: 'Formations'
        },
        {
          path: '/RHpage/evaluations',
          icon: FaClipboardList,
          label: 'Évaluations'
        }
      ]
    },
    {
      id: 'projets',
      label: 'Projets & Tâches',
      icon: FaProjectDiagram,
      type: 'category',
      items: [
        {
          path: '/RHpage/projets',
          icon: FaProjectDiagram,
          label: 'Projets'
        },
        {
          path: '/RHpage/taches',
          icon: FaTasks,
          label: 'Tâches'
        }
      ]
    },
    {
      id: 'documents',
      label: 'Documents & Communication',
      icon: FaFolder,
      type: 'category',
      items: [
        {
          path: '/RHpage/documents',
          icon: FaFolder,
          label: 'Documents'
        },
        {
          path: '/RHpage/attestations',
          icon: FaFileAlt,
          label: 'Attestations'
        },
        {
          path: '/RHpage/messages',
          icon: FaEnvelope,
          label: 'Messages'
        }
      ]
    },
    {
      id: 'analytics',
      label: 'Analyses & Rapports',
      icon: FaChartBar,
      type: 'single',
      path: '/RHpage/analytics'
    }
  ];

  return (
    <div
      style={{
        width: sidebarOpen ? '280px' : '80px',
        backgroundColor: 'white',
        boxShadow: '2px 0 10px rgba(0,0,0,0.1)',
        transition: 'width 0.3s ease',
        position: 'fixed',
        height: '100vh',
        zIndex: 1000,
        overflow: 'hidden',
        fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {/* Header du sidebar */}
      <div style={{
        padding: '1.5rem',
        borderBottom: '1px solid #e5e7eb',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        {sidebarOpen && (
          <div>
            <h2 style={{
              margin: '0 0 0.25rem 0',
              fontSize: '1.2rem',
              color: '#374151',
              fontWeight: '700',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              <FaUserTie style={{ color: '#3b82f6', fontSize: '1.1rem' }} />
              RH Dashboard
            </h2>
            <p style={{
              margin: 0,
              fontSize: '0.875rem',
              color: '#6b7280'
            }}>
              Ressources Humaines
            </p>
          </div>
        )}
        <button
          onClick={() => setSidebarOpen(!sidebarOpen)}
          style={{
            backgroundColor: 'transparent',
            border: 'none',
            color: '#6b7280',
            cursor: 'pointer',
            padding: '0.5rem',
            borderRadius: '4px',
            fontSize: '1.2rem'
          }}
        >
          {sidebarOpen ? <FaTimes /> : <FaBars />}
        </button>
      </div>

      {/* Navigation */}
      <nav style={{
        padding: '1rem 0',
        flex: 1,
        overflowY: 'auto',
        overflowX: 'hidden'
      }}>
        {menuCategories.map((category) => {
          if (category.type === 'single') {
            // Élément de menu simple
            const Icon = category.icon;
            return (
              <NavLink
                key={category.id}
                to={category.path}
                end={category.exact}
                style={({ isActive }) => ({
                  width: '100%',
                  padding: sidebarOpen ? '0.75rem 1.5rem' : '0.75rem',
                  border: 'none',
                  backgroundColor: isActive ? '#3b82f6' : 'transparent',
                  color: isActive ? 'white' : '#6b7280',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: sidebarOpen ? '0.75rem' : '0',
                  justifyContent: sidebarOpen ? 'flex-start' : 'center',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  transition: 'all 0.2s ease',
                  borderLeft: isActive ? '4px solid #3b82f6' : '4px solid transparent',
                  textDecoration: 'none',
                  marginBottom: '0.25rem'
                })}
                onMouseEnter={(e) => {
                  const isActive = e.currentTarget.style.backgroundColor === 'rgb(59, 130, 246)';
                  if (!isActive) {
                    e.currentTarget.style.backgroundColor = '#f3f4f6';
                    e.currentTarget.style.color = '#374151';
                  }
                }}
                onMouseLeave={(e) => {
                  const isActive = e.currentTarget.style.backgroundColor === 'rgb(59, 130, 246)';
                  if (!isActive) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = '#6b7280';
                  }
                }}
              >
                <Icon style={{ fontSize: '1.1rem' }} />
                {sidebarOpen && category.label}
              </NavLink>
            );
          } else {
            // Catégorie avec sous-éléments
            const Icon = category.icon;
            const isExpanded = expandedCategories[category.id];

            return (
              <div key={category.id} style={{ marginBottom: '0.5rem' }}>
                {/* En-tête de catégorie */}
                <button
                  onClick={() => sidebarOpen && toggleCategory(category.id)}
                  style={{
                    width: '100%',
                    padding: sidebarOpen ? '0.75rem 1.5rem' : '0.75rem',
                    border: 'none',
                    backgroundColor: 'transparent',
                    color: '#374151',
                    cursor: sidebarOpen ? 'pointer' : 'default',
                    display: 'flex',
                    alignItems: 'center',
                    gap: sidebarOpen ? '0.75rem' : '0',
                    justifyContent: sidebarOpen ? 'space-between' : 'center',
                    fontSize: '0.875rem',
                    fontWeight: '600',
                    transition: 'all 0.2s ease',
                    textAlign: 'left'
                  }}
                  onMouseEnter={(e) => {
                    if (sidebarOpen) {
                      e.target.style.backgroundColor = '#f9fafb';
                    }
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = 'transparent';
                  }}
                >
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: sidebarOpen ? '0.75rem' : '0'
                  }}>
                    <Icon style={{ fontSize: '1.1rem' }} />
                    {sidebarOpen && category.label}
                  </div>
                  {sidebarOpen && (
                    <div style={{
                      transform: isExpanded ? 'rotate(90deg)' : 'rotate(0deg)',
                      transition: 'transform 0.2s ease'
                    }}>
                      <FaChevronRight style={{ fontSize: '0.75rem' }} />
                    </div>
                  )}
                </button>

                {/* Sous-éléments */}
                {sidebarOpen && isExpanded && (
                  <div style={{
                    paddingLeft: '1rem',
                    borderLeft: '2px solid #f3f4f6',
                    marginLeft: '1.5rem',
                    marginTop: '0.25rem'
                  }}>
                    {category.items.map((item, index) => {
                      const ItemIcon = item.icon;
                      return (
                        <NavLink
                          key={index}
                          to={item.path}
                          style={({ isActive }) => ({
                            width: '100%',
                            padding: '0.5rem 1rem',
                            border: 'none',
                            backgroundColor: isActive ? '#eff6ff' : 'transparent',
                            color: isActive ? '#3b82f6' : '#6b7280',
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.75rem',
                            fontSize: '0.8rem',
                            fontWeight: isActive ? '600' : '500',
                            transition: 'all 0.2s ease',
                            textDecoration: 'none',
                            borderRadius: '6px',
                            marginBottom: '0.125rem'
                          })}
                          onMouseEnter={(e) => {
                            const isActive = e.currentTarget.style.backgroundColor === 'rgb(239, 246, 255)';
                            if (!isActive) {
                              e.currentTarget.style.backgroundColor = '#f8fafc';
                              e.currentTarget.style.color = '#374151';
                            }
                          }}
                          onMouseLeave={(e) => {
                            const isActive = e.currentTarget.style.backgroundColor === 'rgb(239, 246, 255)';
                            if (!isActive) {
                              e.currentTarget.style.backgroundColor = 'transparent';
                              e.currentTarget.style.color = '#6b7280';
                            }
                          }}
                        >
                          <ItemIcon style={{ fontSize: '0.9rem' }} />
                          {item.label}
                        </NavLink>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          }
        })}
      </nav>

      {/* Footer avec informations utilisateur */}
      {sidebarOpen && (
        <div style={{
          padding: '1rem 1.5rem',
          borderTop: '1px solid #e5e7eb',
          backgroundColor: '#f9fafb'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.75rem'
          }}>
            <div style={{
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              backgroundColor: '#3b82f6',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: '600',
              fontSize: '0.875rem'
            }}>
              {utilisateur?.email?.charAt(0).toUpperCase() || 'U'}
            </div>
            <div style={{ flex: 1, minWidth: 0 }}>
              <div style={{
                fontSize: '0.875rem',
                fontWeight: '600',
                color: '#374151',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}>
                {utilisateur?.email || 'Utilisateur'}
              </div>
              <div style={{
                fontSize: '0.75rem',
                color: '#6b7280',
                textTransform: 'uppercase',
                letterSpacing: '0.5px',
                fontWeight: '500'
              }}>
                👔 Ressources Humaines
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RhSidebar;