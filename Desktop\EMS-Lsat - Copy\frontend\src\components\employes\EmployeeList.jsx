import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { FaPlus, FaSearch, FaEye, FaEdit, FaTrash, FaUser, FaIdCard, FaUserSlash, FaUserCheck } from 'react-icons/fa';
import AddEmployeeModal from './AddEmployeeModal';
import EditEmployeeModal from './EditEmployeeModal';
import ViewEmployeeModal from './ViewEmployeeModal';
import MyInfoModal from './MyInfoModal';

const EmployeeList = () => {
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showMyInfoModal, setShowMyInfoModal] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [currentUser, setCurrentUser] = useState(null);
  const [currentEmployeeData, setCurrentEmployeeData] = useState(null);
  const [error, setError] = useState('');

  // Charger les employés
  const fetchEmployees = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await axios.get('http://localhost:4000/api/employees', {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        setEmployees(response.data.data.employees);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des employés:', error);
      setError('Erreur lors du chargement des employés');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEmployees();
    getCurrentUser();
  }, []);

  // Récupérer les informations de l'utilisateur connecté
  const getCurrentUser = () => {
    const token = localStorage.getItem('token');
    if (token) {
      try {
        // Décoder le token pour récupérer les informations utilisateur
        const payload = JSON.parse(atob(token.split('.')[1]));
        setCurrentUser(payload);
      } catch (error) {
        console.error('Erreur lors du décodage du token:', error);
      }
    }
  };

  // Filtrer les employés selon le terme de recherche
  const filteredEmployees = employees.filter(employee =>
    employee.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.prenom.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.poste?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.departement?.nom_departement?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Basculer le statut d'un employé (activer/désactiver)
  const handleToggleStatus = async (employee) => {
    const newStatus = employee.statut_compte === 'actif' ? 'inactif' : 'actif';
    const action = newStatus === 'actif' ? 'activer' : 'désactiver';

    if (window.confirm(`Êtes-vous sûr de vouloir ${action} le compte de ${employee.prenom} ${employee.nom} ?`)) {
      try {
        const token = localStorage.getItem('token');
        await axios.patch(`http://localhost:4000/api/employees/${employee._id}/status`, {
          statut_compte: newStatus,
          is_active: newStatus === 'actif'
        }, {
          headers: { Authorization: `Bearer ${token}` }
        });

        // Recharger la liste
        fetchEmployees();
        setError(''); // Effacer les erreurs précédentes
      } catch (error) {
        console.error('Erreur lors du changement de statut:', error);
        setError(`Erreur lors de la ${action === 'activer' ? 'réactivation' : 'désactivation'} du compte`);
      }
    }
  };

  // Supprimer définitivement un employé (action plus restrictive)
  const handleDelete = async (employee) => {
    if (employee.statut_compte === 'actif') {
      setError('Impossible de supprimer un employé actif. Veuillez d\'abord désactiver son compte.');
      return;
    }

    if (window.confirm(`⚠️ ATTENTION: Cette action est irréversible!\n\nÊtes-vous sûr de vouloir supprimer définitivement ${employee.prenom} ${employee.nom} ?\n\nToutes ses données (contrats, projets, historique) seront perdues.`)) {
      try {
        const token = localStorage.getItem('token');
        await axios.delete(`http://localhost:4000/api/employees/${employee._id}`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        // Recharger la liste
        fetchEmployees();
        setError(''); // Effacer les erreurs précédentes
      } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        setError('Erreur lors de la suppression de l\'employé');
      }
    }
  };

  // Ouvrir le modal de modification
  const handleEdit = (employee) => {
    setSelectedEmployee(employee);
    setShowEditModal(true);
  };

  // Ouvrir le modal de visualisation
  const handleView = (employee) => {
    setSelectedEmployee(employee);
    setShowViewModal(true);
  };

  // Ouvrir le modal "Mes Informations"
  const handleMyInfo = async () => {
    if (!currentUser) {
      setError('Utilisateur non connecté');
      return;
    }

    try {
      const token = localStorage.getItem('token');
      // Utiliser la nouvelle API pour récupérer les informations de l'employé connecté
      const response = await axios.get('http://localhost:4000/api/employees/my-info', {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        setCurrentEmployeeData(response.data.data);
        setShowMyInfoModal(true);
      } else {
        setError(response.data.message || 'Aucune information d\'employé trouvée');
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des informations:', error);
      if (error.response?.status === 404) {
        setError('Aucune information d\'employé trouvée pour votre compte');
      } else {
        setError('Erreur lors de la récupération de vos informations');
      }
    }
  };

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '50px',
            height: '50px',
            border: '4px solid #e2e8f0',
            borderTop: '4px solid #3182ce',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 1rem'
          }}></div>
          <p>Chargement des employés...</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{ padding: '1rem' }}>
      {/* Header avec recherche et bouton d'ajout */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '2rem',
        flexWrap: 'wrap',
        gap: '1rem'
      }}>
        {/* Barre de recherche */}
        <div style={{ position: 'relative', flex: '1', maxWidth: '400px' }}>
          <FaSearch style={{
            position: 'absolute',
            left: '12px',
            top: '50%',
            transform: 'translateY(-50%)',
            color: '#9ca3af',
            fontSize: '1rem'
          }} />
          <input
            type="text"
            placeholder="Rechercher un employé..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              width: '100%',
              padding: '0.75rem 0.75rem 0.75rem 2.5rem',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              fontSize: '1rem',
              outline: 'none',
              transition: 'border-color 0.2s'
            }}
            onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
            onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
          />
        </div>

        {/* Boutons d'action */}
        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
          {/* Bouton Mes Informations - visible pour tous les utilisateurs connectés */}
          {currentUser && (
            <button
              onClick={handleMyInfo}
              style={{
                backgroundColor: '#10b981',
                color: 'white',
                padding: '0.75rem 1.5rem',
                border: 'none',
                borderRadius: '8px',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                transition: 'background-color 0.2s'
              }}
              onMouseEnter={(e) => e.target.style.backgroundColor = '#059669'}
              onMouseLeave={(e) => e.target.style.backgroundColor = '#10b981'}
            >
              <FaIdCard />
              Mes Informations
            </button>
          )}

          {/* Bouton Ajouter - visible seulement pour admin/RH */}
          {currentUser && (currentUser.role === 'admin' || currentUser.role === 'rh') && (
            <button
              onClick={() => setShowAddModal(true)}
              style={{
                backgroundColor: '#3b82f6',
                color: 'white',
                padding: '0.75rem 1.5rem',
                border: 'none',
                borderRadius: '8px',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                transition: 'background-color 0.2s'
              }}
              onMouseEnter={(e) => e.target.style.backgroundColor = '#2563eb'}
              onMouseLeave={(e) => e.target.style.backgroundColor = '#3b82f6'}
            >
              <FaPlus />
              Ajouter un employé
            </button>
          )}
        </div>
      </div>

      {/* Message d'erreur */}
      {error && (
        <div style={{
          backgroundColor: '#fef2f2',
          color: '#dc2626',
          padding: '1rem',
          borderRadius: '8px',
          marginBottom: '1rem',
          border: '1px solid #fecaca'
        }}>
          {error}
        </div>
      )}

      {/* Grille des employés */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',
        gap: '1.5rem'
      }}>
        {filteredEmployees.map((employee) => (
          <EmployeeCard
            key={employee._id}
            employee={employee}
            onView={() => handleView(employee)}
            onEdit={() => handleEdit(employee)}
            onToggleStatus={() => handleToggleStatus(employee)}
            onDelete={() => handleDelete(employee)}
          />
        ))}
      </div>

      {/* Message si aucun employé */}
      {filteredEmployees.length === 0 && !loading && (
        <div style={{
          textAlign: 'center',
          padding: '3rem',
          color: '#6b7280'
        }}>
          <FaUser style={{ fontSize: '3rem', marginBottom: '1rem', opacity: 0.5 }} />
          <h3 style={{ margin: '0 0 0.5rem 0' }}>Aucun employé trouvé</h3>
          <p style={{ margin: 0 }}>
            {searchTerm ? 'Aucun employé ne correspond à votre recherche.' : 'Commencez par ajouter votre premier employé.'}
          </p>
        </div>
      )}

      {/* Modals */}
      {showAddModal && (
        <AddEmployeeModal
          onClose={() => setShowAddModal(false)}
          onSuccess={() => {
            setShowAddModal(false);
            fetchEmployees();
          }}
        />
      )}

      {showEditModal && selectedEmployee && (
        <EditEmployeeModal
          employee={selectedEmployee}
          onClose={() => {
            setShowEditModal(false);
            setSelectedEmployee(null);
          }}
          onSuccess={() => {
            setShowEditModal(false);
            setSelectedEmployee(null);
            fetchEmployees();
          }}
        />
      )}

      {showViewModal && selectedEmployee && (
        <ViewEmployeeModal
          employee={selectedEmployee}
          onClose={() => {
            setShowViewModal(false);
            setSelectedEmployee(null);
          }}
        />
      )}

      {showMyInfoModal && currentEmployeeData && (
        <MyInfoModal
          employee={currentEmployeeData}
          onClose={() => {
            setShowMyInfoModal(false);
            setCurrentEmployeeData(null);
          }}
        />
      )}

      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );
};

// Composant pour une carte d'employé
const EmployeeCard = ({ employee, onView, onEdit, onToggleStatus, onDelete }) => {
  const getStatusColor = (status) => {
    return status === 'actif' ? '#10b981' : '#ef4444';
  };

  const getStatusBgColor = (status) => {
    return status === 'actif' ? '#d1fae5' : '#fee2e2';
  };

  const isInactive = employee.statut_compte === 'inactif';

  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      padding: '1.5rem',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
      border: `1px solid ${isInactive ? '#fecaca' : '#e5e7eb'}`,
      transition: 'transform 0.2s, box-shadow 0.2s',
      opacity: isInactive ? 0.75 : 1,
      position: 'relative'
    }}
    onMouseEnter={(e) => {
      e.currentTarget.style.transform = 'translateY(-2px)';
      e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
    }}
    onMouseLeave={(e) => {
      e.currentTarget.style.transform = 'translateY(0)';
      e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
    }}
    >
      {/* Badge pour compte inactif */}
      {isInactive && (
        <div style={{
          position: 'absolute',
          top: '0.75rem',
          right: '0.75rem',
          backgroundColor: '#ef4444',
          color: 'white',
          padding: '0.25rem 0.5rem',
          borderRadius: '12px',
          fontSize: '0.75rem',
          fontWeight: '500'
        }}>
          INACTIF
        </div>
      )}

      {/* Header avec photo et nom */}
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>
        <div style={{
          width: '60px',
          height: '60px',
          borderRadius: '50%',
          backgroundColor: '#f3f4f6',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginRight: '1rem',
          overflow: 'hidden'
        }}>
          {employee.photo ? (
            <img
              src={`http://localhost:4000${employee.photo}`}
              alt={`${employee.prenom} ${employee.nom}`}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover'
              }}
            />
          ) : (
            <FaUser style={{ fontSize: '1.5rem', color: '#9ca3af' }} />
          )}
        </div>
        <div>
          <h3 style={{
            margin: '0 0 0.25rem 0',
            fontSize: '1.25rem',
            fontWeight: '600',
            color: '#1f2937'
          }}>
            {employee.prenom} {employee.nom}
          </h3>
          <p style={{
            margin: 0,
            color: '#6b7280',
            fontSize: '0.9rem'
          }}>
            {employee.poste || 'Poste non défini'}
          </p>
        </div>
      </div>

      {/* Informations */}
      <div style={{ marginBottom: '1rem' }}>
        <div style={{ marginBottom: '0.5rem' }}>
          <span style={{ color: '#6b7280', fontSize: '0.9rem' }}>Département: </span>
          <span style={{ color: '#3b82f6', fontWeight: '500' }}>
            {employee.departement?.nom_departement || 'Non assigné'}
          </span>
        </div>
        <div>
          <span style={{ color: '#6b7280', fontSize: '0.9rem' }}>Statut: </span>
          <span style={{
            backgroundColor: getStatusBgColor(employee.statut_compte),
            color: getStatusColor(employee.statut_compte),
            padding: '0.25rem 0.5rem',
            borderRadius: '4px',
            fontSize: '0.8rem',
            fontWeight: '500',
            textTransform: 'capitalize'
          }}>
            {employee.statut_compte || 'Actif'}
          </span>
        </div>
      </div>

      {/* Boutons d'action */}
      <div style={{
        display: 'flex',
        gap: '0.5rem',
        flexWrap: 'wrap'
      }}>
        <button
          onClick={onView}
          style={{
            flex: '1 1 calc(50% - 0.25rem)',
            padding: '0.5rem',
            border: '1px solid #d1d5db',
            borderRadius: '6px',
            backgroundColor: 'white',
            color: '#374151',
            cursor: 'pointer',
            fontSize: '0.85rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '0.25rem',
            transition: 'background-color 0.2s'
          }}
          onMouseEnter={(e) => e.target.style.backgroundColor = '#f9fafb'}
          onMouseLeave={(e) => e.target.style.backgroundColor = 'white'}
        >
          <FaEye />
          Afficher
        </button>

        <button
          onClick={onEdit}
          style={{
            flex: '1 1 calc(50% - 0.25rem)',
            padding: '0.5rem',
            border: '1px solid #d1d5db',
            borderRadius: '6px',
            backgroundColor: 'white',
            color: '#374151',
            cursor: 'pointer',
            fontSize: '0.85rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '0.25rem',
            transition: 'background-color 0.2s'
          }}
          onMouseEnter={(e) => e.target.style.backgroundColor = '#f9fafb'}
          onMouseLeave={(e) => e.target.style.backgroundColor = 'white'}
        >
          <FaEdit />
          Modifier
        </button>

        <button
          onClick={onToggleStatus}
          style={{
            flex: '1 1 calc(50% - 0.25rem)',
            padding: '0.5rem',
            border: 'none',
            borderRadius: '6px',
            backgroundColor: isInactive ? '#10b981' : '#f59e0b',
            color: 'white',
            cursor: 'pointer',
            fontSize: '0.85rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '0.25rem',
            transition: 'background-color 0.2s'
          }}
          onMouseEnter={(e) => e.target.style.backgroundColor = isInactive ? '#059669' : '#d97706'}
          onMouseLeave={(e) => e.target.style.backgroundColor = isInactive ? '#10b981' : '#f59e0b'}
          title={isInactive ? 'Réactiver le compte' : 'Désactiver le compte'}
        >
          {isInactive ? <FaUserCheck /> : <FaUserSlash />}
          {isInactive ? 'Activer' : 'Désactiver'}
        </button>

        <button
          onClick={onDelete}
          style={{
            flex: '1 1 calc(50% - 0.25rem)',
            padding: '0.5rem',
            border: 'none',
            borderRadius: '6px',
            backgroundColor: '#ef4444',
            color: 'white',
            cursor: 'pointer',
            fontSize: '0.85rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '0.25rem',
            transition: 'background-color 0.2s',
            opacity: employee.statut_compte === 'actif' ? 0.5 : 1
          }}
          onMouseEnter={(e) => e.target.style.backgroundColor = '#dc2626'}
          onMouseLeave={(e) => e.target.style.backgroundColor = '#ef4444'}
          title={employee.statut_compte === 'actif' ? 'Désactivez d\'abord le compte pour pouvoir supprimer' : 'Supprimer définitivement'}
        >
          <FaTrash />
          Supprimer
        </button>
      </div>
    </div>
  );
};

export default EmployeeList;