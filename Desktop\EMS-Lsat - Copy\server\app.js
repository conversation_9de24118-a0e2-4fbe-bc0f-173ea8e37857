import express from 'express';
import cors from 'cors';
import path from 'path';
import { fileURLToPath } from 'url';

// Import routes
import authRouter from './routes/auth.js';
import adminRouter from './routes/admin.js';
import employeesRouter from './routes/employees.js';
import departmentsRouter from './routes/departments.js';
import servicesRouter from './routes/services.js';
import contratsRouter from './routes/contrats.js';
import projetsRouter from './routes/projets.js';
import tachesRouter from './routes/taches.js';
import profileRouter from './routes/profile.js';
import archivesRouter from './routes/archives.js';
import formationsRouter from './routes/formations.js';
import congesRouter from './routes/conges.js';
import testRouter from './routes/test.js';


// Configuration pour ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Créer l'application Express
const app = express();

// Middlewares globaux
app.use(cors({
    origin: ["http://localhost:3000", "http://localhost:5173", "http://localhost:5174"],
    credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Servir les fichiers statiques (uploads)
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Route de test
app.get('/api/test', (req, res) => {
    res.json({
        success: true,
        message: "API EMS fonctionne correctement!",
        timestamp: new Date().toISOString(),
        endpoints: {
            auth: "/api/auth",
            admin: "/api/admin",
            employees: "/api/employees",
            departments: "/api/departments",
            services: "/api/services",
            contrats: "/api/contrats",
            projets: "/api/projets",
            taches: "/api/taches",
            profile: "/api/profile",
            archives: "/api/archives",
            formations: "/api/formations",
            conges: "/api/conges"
        }
    });
});

// Routes API
app.use('/api/test', testRouter); // Route de test SANS authentification
app.use('/api/auth', authRouter);
app.use('/api/admin', adminRouter);
app.use('/api/employees', employeesRouter);
app.use('/api/departments', departmentsRouter);
app.use('/api/services', servicesRouter);
app.use('/api/contrats', contratsRouter);
app.use('/api/projets', projetsRouter);
app.use('/api/taches', tachesRouter);
app.use('/api/profile', profileRouter);
app.use('/api/archives', archivesRouter);
app.use('/api/formations', formationsRouter);
app.use('/api/conges', congesRouter);



// Route par défaut
app.get('/', (req, res) => {
    res.json({
        message: "🏢 EMS API Server",
        version: "1.0.0",
        status: "running",
        documentation: "/api/test"
    });
});

// Middleware de gestion des erreurs 404
app.use((req, res) => {
    res.status(404).json({
        success: false,
        message: `Route ${req.originalUrl} non trouvée`
    });
});

// Middleware de gestion des erreurs globales
app.use((error, req, res, next) => {
    console.error('Erreur globale:', error);
    res.status(500).json({
        success: false,
        message: "Erreur interne du serveur",
        ...(process.env.NODE_ENV === 'development' && { error: error.message })
    });
});

export default app;
