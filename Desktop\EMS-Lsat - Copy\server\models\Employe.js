import mongoose from "mongoose";

const employeSchema = new mongoose.Schema({
  // 🧍 Informations personnelles
  prenom: {
    type: String,
    required: true,
    trim: true
  },
  nom: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  sexe: {
    type: String,
    enum: ["Masculin", "Féminin"],
    required: true
  },
  genre: {
    type: String,
    enum: ["homme", "femme", "autre"],
    default: function() {
      return this.sexe === "Masculin" ? "homme" : "femme";
    }
  },
  cin: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  cnss: {
    type: String,
    unique: true,
    sparse: true,
    trim: true
  },
  statut_matrimonial: {
    type: String,
    enum: ["Célibataire", "Mari<PERSON>(e)", "<PERSON>vor<PERSON>(e)", "Veuf/Veuve"],
    required: true
  },
  situation_familiale: {
    type: String,
    enum: ["celibataire", "marie", "divorce", "veuf"],
    default: function() {
      const mapping = {
        "Célibataire": "celibataire",
        "<PERSON><PERSON>(e)": "marie",
        "<PERSON><PERSON><PERSON>(e)": "divorce",
        "Veuf/Veuve": "veuf"
      };
      return mapping[this.statut_matrimonial] || "celibataire";
    }
  },
  telephone: {
    type: String,
    trim: true
  },
  mobile: {
    type: String,
    required: true,
    trim: true
  },
  adresse: {
    type: String,
    required: true,
    trim: true
  },
  ville: {
    type: String,
    required: true,
    trim: true
  },
  pays: {
    type: String,
    required: true,
    default: "Maroc",
    trim: true
  },
  nationalite: {
    type: String,
    default: "Marocaine",
    trim: true
  },
  code_postal: {
    type: String,
    trim: true
  },
  date_naissance: {
    type: Date,
    required: true
  },
  photo: {
    type: String,
    default: null
  },

  // 💼 Informations professionnelles
  departement: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Departement",
    required: true
  },
  service: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Service"
  },
  fonction: {
    type: String,
    required: true,
    trim: true
  },
  type_contrat: {
    type: String,
    enum: ['CDI', 'CDD', 'Stage', 'Freelance', 'Apprentissage', 'Interim'],
    required: true
  },
  date_entree: {
    type: Date,
    required: true,
    default: Date.now
  },
  salaire_base: {
    type: Number,
    required: true,
    min: 0,
    comment: "Salaire de base en Dirhams"
  },
  salaire: {
    type: Number,
    default: function() { return this.salaire_base; },
    min: 0
  },
  email_professionnel: {
    type: String,
    trim: true,
    lowercase: true
  },
  cout_heures_supplementaires: {
    type: Number,
    default: 0,
    min: 0,
    comment: "Coût par heure supplémentaire en Dirhams"
  },
  superviseur: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Employe",
    validate: {
      validator: async function(value) {
        if (!value) return true; // Superviseur optionnel

        // Vérifier que le superviseur est dans le même département
        const superviseur = await mongoose.model('Employe').findById(value);
        return superviseur && superviseur.departement.toString() === this.departement.toString();
      },
      message: 'Le superviseur doit être du même département'
    }
  },
  conges_consommes: {
    type: Number,
    default: 0,
    min: 0,
    comment: "Nombre de jours de congés consommés"
  },

  // 🏦 Informations bancaires
  banque: {
    nom_banque: {
      type: String,
      trim: true
    },
    rib: {
      type: String,
      trim: true,
      validate: {
        validator: function(v) {
          // Validation RIB marocain (24 chiffres)
          return !v || /^\d{24}$/.test(v.replace(/\s/g, ''));
        },
        message: 'Le RIB doit contenir 24 chiffres'
      }
    },
    adresse_banque: {
      type: String,
      trim: true
    },
    ville_banque: {
      type: String,
      trim: true
    }
  },
  // Champs bancaires directs pour compatibilité
  nom_banque: {
    type: String,
    trim: true,
    default: function() { return this.banque?.nom_banque; }
  },
  rib: {
    type: String,
    trim: true,
    default: function() { return this.banque?.rib; }
  },
  adresse_banque: {
    type: String,
    trim: true,
    default: function() { return this.banque?.adresse_banque; }
  },
  ville_banque: {
    type: String,
    trim: true,
    default: function() { return this.banque?.ville_banque; }
  },

  // 🎓 Informations éducatives et professionnelles
  niveau_education: {
    type: String,
    trim: true
  },
  diplomes: {
    type: String,
    trim: true
  },
  experience_professionnelle: {
    type: String,
    trim: true
  },
  competences: {
    type: String,
    trim: true
  },

  // Champs existants conservés
  date_embauche: {
    type: Date,
    default: function() { return this.date_entree; }
  },
  poste: {
    type: String,
    default: function() { return this.fonction; }
  },
  statut_compte: {
    type: String,
    enum: ["actif", "inactif"],
    default: "actif"
  },
  is_active: {
    type: Boolean,
    default: true
  },
  utilisateur: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Utilisateur",
    required: true
  },
  contacts_urgence: [{
    nom: String,
    telephone: String,
    relation: String
  }],
  personnes_a_charge: [{
    nom: String,
    relation: String,
    date_naissance: Date
  }],
  infos_personnelles: mongoose.Schema.Types.Mixed
}, {
  timestamps: true
});

// Index pour optimiser les recherches
employeSchema.index({ email: 1 });
employeSchema.index({ cin: 1 });
employeSchema.index({ cnss: 1 });
employeSchema.index({ departement: 1 });
employeSchema.index({ superviseur: 1 });
employeSchema.index({ statut_compte: 1 });

// Méthode pour calculer l'âge
employeSchema.methods.getAge = function() {
  const today = new Date();
  const birthDate = new Date(this.date_naissance);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }

  return age;
};

// Méthode pour calculer l'ancienneté
employeSchema.methods.getAnciennete = function() {
  const today = new Date();
  const entryDate = new Date(this.date_entree);
  const diffTime = Math.abs(today - entryDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  const years = Math.floor(diffDays / 365);
  const months = Math.floor((diffDays % 365) / 30);

  return { years, months, days: diffDays };
};

const Employe = mongoose.model("Employe", employeSchema);
export default Employe;
