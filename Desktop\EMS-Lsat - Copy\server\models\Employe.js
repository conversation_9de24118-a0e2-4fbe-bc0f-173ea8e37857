import mongoose from "mongoose";

const employeSchema = new mongoose.Schema({
  nom: String,
  prenom: String,
  cin: String,
  date_naissance: Date,
  poste: String,
  telephone: String,
  adresse: String,
  date_embauche: Date,
  photo: String,
  statut_compte: { type: String, enum: ["actif", "inactif"] },
  is_active: Boolean,
  utilisateur: { type: mongoose.Schema.Types.ObjectId, ref: "Utilisateur" },
  departement: { type: mongoose.Schema.Types.ObjectId, ref: "Departement" },
  service: { type: mongoose.Schema.Types.ObjectId, ref: "Service" },
  contacts_urgence: [{ nom: String, telephone: String, relation: String }],
  personnes_a_charge: [{ nom: String, relation: String, date_naissance: Date }],
  infos_personnelles: mongoose.Schema.Types.Mixed
}, { timestamps: true });

const Employe = mongoose.model("Employe", employeSchema);
export default Employe;
