import React, { useState, useEffect } from 'react';
import { FaEye, FaCheck, FaTimes, FaClock, FaUser, FaCalendarAlt, FaBuilding } from 'react-icons/fa';

const CandidatureManagement = () => {
  const [candidatures, setCandidatures] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedCandidature, setSelectedCandidature] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [actionType, setActionType] = useState(''); // 'approuver' ou 'refuser'
  const [messageRH, setMessageRH] = useState('');
  const [processing, setProcessing] = useState(false);

  // Filtres
  const [filterStatut, setFilterStatut] = useState('');

  useEffect(() => {
    fetchCandidatures();
  }, [filterStatut]);

  const fetchCandidatures = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (filterStatut) params.append('statut', filterStatut);

      const response = await fetch(`http://localhost:4000/api/formations/candidatures?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setCandidatures(data.candidatures || []);
      } else {
        setError('Erreur lors du chargement des candidatures');
      }
    } catch (error) {
      console.error('Erreur:', error);
      setError('Erreur de connexion');
    } finally {
      setLoading(false);
    }
  };

  const handleAction = async () => {
    if (!selectedCandidature || !actionType) return;

    try {
      setProcessing(true);
      const endpoint = actionType === 'approuver' ? 'approuver' : 'refuser';
      
      const response = await fetch(`http://localhost:4000/api/formations/candidatures/${selectedCandidature._id}/${endpoint}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          message_rh: messageRH
        })
      });

      if (response.ok) {
        // Recharger les candidatures
        await fetchCandidatures();
        
        // Fermer la modal
        setShowModal(false);
        setSelectedCandidature(null);
        setMessageRH('');
        setActionType('');
        
        // Notification de succès (vous pouvez ajouter une notification toast ici)
        alert(`Candidature ${actionType === 'approuver' ? 'approuvée' : 'refusée'} avec succès`);
      } else {
        const data = await response.json();
        alert(data.message || 'Erreur lors du traitement');
      }
    } catch (error) {
      console.error('Erreur:', error);
      alert('Erreur de connexion');
    } finally {
      setProcessing(false);
    }
  };

  const openActionModal = (candidature, action) => {
    setSelectedCandidature(candidature);
    setActionType(action);
    setShowModal(true);
  };

  const getStatutColor = (statut) => {
    switch (statut) {
      case 'en_attente': return '#f59e0b';
      case 'approuvee': return '#10b981';
      case 'refusee': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getStatutLabel = (statut) => {
    switch (statut) {
      case 'en_attente': return 'En attente';
      case 'approuvee': return 'Approuvée';
      case 'refusee': return 'Refusée';
      default: return statut;
    }
  };

  if (loading) {
    return (
      <div style={{ padding: '2rem', textAlign: 'center' }}>
        <div>Chargement des candidatures...</div>
      </div>
    );
  }

  return (
    <div style={{ padding: '2rem' }}>
      {/* Header */}
      <div style={{ marginBottom: '2rem' }}>
        <h1 style={{ margin: '0 0 0.5rem 0', fontSize: '2rem', fontWeight: '700' }}>
          Gestion des Candidatures
        </h1>
        <p style={{ margin: 0, color: '#6b7280' }}>
          Gérez les candidatures des employés aux formations
        </p>
      </div>

      {/* Filtres */}
      <div style={{ marginBottom: '2rem', display: 'flex', gap: '1rem', alignItems: 'center' }}>
        <label style={{ fontWeight: '600' }}>Filtrer par statut:</label>
        <select
          value={filterStatut}
          onChange={(e) => setFilterStatut(e.target.value)}
          style={{
            padding: '0.5rem',
            border: '1px solid #d1d5db',
            borderRadius: '6px',
            backgroundColor: 'white'
          }}
        >
          <option value="">Tous les statuts</option>
          <option value="en_attente">En attente</option>
          <option value="approuvee">Approuvées</option>
          <option value="refusee">Refusées</option>
        </select>
      </div>

      {/* Liste des candidatures */}
      {error && (
        <div style={{
          backgroundColor: '#fef2f2',
          color: '#dc2626',
          padding: '1rem',
          borderRadius: '8px',
          marginBottom: '1rem'
        }}>
          {error}
        </div>
      )}

      {candidatures.length === 0 ? (
        <div style={{
          textAlign: 'center',
          padding: '3rem',
          backgroundColor: 'white',
          borderRadius: '12px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <FaClock style={{ fontSize: '3rem', color: '#d1d5db', marginBottom: '1rem' }} />
          <h3 style={{ color: '#6b7280', margin: '0 0 0.5rem 0' }}>Aucune candidature</h3>
          <p style={{ color: '#9ca3af', margin: 0 }}>
            {filterStatut ? 'Aucune candidature avec ce statut' : 'Aucune candidature reçue pour le moment'}
          </p>
        </div>
      ) : (
        <div style={{ display: 'grid', gap: '1rem' }}>
          {candidatures.map((candidature) => (
            <CandidatureCard
              key={candidature._id}
              candidature={candidature}
              onApprouver={() => openActionModal(candidature, 'approuver')}
              onRefuser={() => openActionModal(candidature, 'refuser')}
            />
          ))}
        </div>
      )}

      {/* Modal d'action */}
      {showModal && selectedCandidature && (
        <ActionModal
          candidature={selectedCandidature}
          actionType={actionType}
          message={messageRH}
          setMessage={setMessageRH}
          onConfirm={handleAction}
          onCancel={() => {
            setShowModal(false);
            setSelectedCandidature(null);
            setMessageRH('');
            setActionType('');
          }}
          processing={processing}
        />
      )}
    </div>
  );
};

// Composant carte candidature
const CandidatureCard = ({ candidature, onApprouver, onRefuser }) => {
  const getStatutColor = (statut) => {
    switch (statut) {
      case 'en_attente': return '#f59e0b';
      case 'approuvee': return '#10b981';
      case 'refusee': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getStatutLabel = (statut) => {
    switch (statut) {
      case 'en_attente': return 'En attente';
      case 'approuvee': return 'Approuvée';
      case 'refusee': return 'Refusée';
      default: return statut;
    }
  };

  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      padding: '1.5rem',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
      border: '1px solid #e5e7eb'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '1rem' }}>
        <div style={{ flex: 1 }}>
          <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '1.25rem', fontWeight: '600' }}>
            {candidature.formation_id?.nom_formation}
          </h3>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '0.5rem' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <FaUser style={{ color: '#6b7280', fontSize: '0.875rem' }} />
              <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                {candidature.employe_id?.prenom} {candidature.employe_id?.nom}
              </span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <FaBuilding style={{ color: '#6b7280', fontSize: '0.875rem' }} />
              <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                {candidature.formation_id?.organisme}
              </span>
            </div>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            <FaCalendarAlt style={{ color: '#6b7280', fontSize: '0.875rem' }} />
            <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>
              Candidature du {new Date(candidature.date_candidature).toLocaleDateString()}
            </span>
          </div>
        </div>
        
        <span style={{
          backgroundColor: getStatutColor(candidature.statut),
          color: 'white',
          padding: '0.25rem 0.75rem',
          borderRadius: '12px',
          fontSize: '0.75rem',
          fontWeight: '500'
        }}>
          {getStatutLabel(candidature.statut)}
        </span>
      </div>

      {candidature.message_employe && (
        <div style={{
          backgroundColor: '#f8fafc',
          padding: '1rem',
          borderRadius: '8px',
          marginBottom: '1rem'
        }}>
          <p style={{ margin: 0, fontSize: '0.875rem', fontStyle: 'italic' }}>
            "{candidature.message_employe}"
          </p>
        </div>
      )}

      {candidature.statut === 'en_attente' && (
        <div style={{ display: 'flex', gap: '0.5rem' }}>
          <button
            onClick={onApprouver}
            style={{
              flex: 1,
              padding: '0.5rem 1rem',
              border: 'none',
              backgroundColor: '#10b981',
              color: 'white',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '0.875rem',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '0.5rem'
            }}
          >
            <FaCheck />
            Approuver
          </button>
          <button
            onClick={onRefuser}
            style={{
              flex: 1,
              padding: '0.5rem 1rem',
              border: 'none',
              backgroundColor: '#ef4444',
              color: 'white',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '0.875rem',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '0.5rem'
            }}
          >
            <FaTimes />
            Refuser
          </button>
        </div>
      )}

      {candidature.message_rh && (
        <div style={{
          backgroundColor: '#eff6ff',
          padding: '1rem',
          borderRadius: '8px',
          marginTop: '1rem',
          borderLeft: '4px solid #3b82f6'
        }}>
          <p style={{ margin: '0 0 0.5rem 0', fontSize: '0.75rem', fontWeight: '600', color: '#3b82f6' }}>
            Réponse RH:
          </p>
          <p style={{ margin: 0, fontSize: '0.875rem' }}>
            {candidature.message_rh}
          </p>
        </div>
      )}
    </div>
  );
};

// Modal d'action
const ActionModal = ({ candidature, actionType, message, setMessage, onConfirm, onCancel, processing }) => {
  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '2rem',
        width: '90%',
        maxWidth: '500px'
      }}>
        <h3 style={{ margin: '0 0 1rem 0', fontSize: '1.25rem', fontWeight: '600' }}>
          {actionType === 'approuver' ? 'Approuver la candidature' : 'Refuser la candidature'}
        </h3>
        
        <p style={{ margin: '0 0 1rem 0', color: '#6b7280' }}>
          <strong>{candidature.employe_id?.prenom} {candidature.employe_id?.nom}</strong> pour la formation 
          <strong> {candidature.formation_id?.nom_formation}</strong>
        </p>

        <div style={{ marginBottom: '1.5rem' }}>
          <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
            Message (optionnel):
          </label>
          <textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder={`Ajoutez un message pour expliquer votre ${actionType === 'approuver' ? 'approbation' : 'refus'}...`}
            rows={3}
            style={{
              width: '100%',
              padding: '0.75rem',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              resize: 'vertical'
            }}
          />
        </div>

        <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end' }}>
          <button
            onClick={onCancel}
            disabled={processing}
            style={{
              padding: '0.75rem 1.5rem',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              backgroundColor: 'white',
              cursor: processing ? 'not-allowed' : 'pointer',
              opacity: processing ? 0.7 : 1
            }}
          >
            Annuler
          </button>
          <button
            onClick={onConfirm}
            disabled={processing}
            style={{
              padding: '0.75rem 1.5rem',
              border: 'none',
              borderRadius: '8px',
              backgroundColor: actionType === 'approuver' ? '#10b981' : '#ef4444',
              color: 'white',
              cursor: processing ? 'not-allowed' : 'pointer',
              opacity: processing ? 0.7 : 1
            }}
          >
            {processing ? 'Traitement...' : (actionType === 'approuver' ? 'Approuver' : 'Refuser')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CandidatureManagement;
