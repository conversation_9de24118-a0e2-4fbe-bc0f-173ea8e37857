import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { FaTimes, FaUser, FaUpload, FaBriefcase, FaUniversity, FaIdCard, FaArrowLeft, FaArrowRight } from 'react-icons/fa';

const AddEmployeeModal = ({ onClose, onSuccess }) => {
  const [formData, setFormData] = useState({
    // 🧍 Informations personnelles
    prenom: '',
    nom: '',
    email: '',
    password: '',
    sexe: '',
    cin: '',
    cnss: '',
    statut_matrimonial: '',
    telephone: '',
    mobile: '',
    adresse: '',
    ville: '',
    pays: 'Maroc',
    date_naissance: '',
    
    // 💼 Informations professionnelles
    departement: '',
    service: '',
    fonction: '',
    type_contrat: '',
    date_entree: '',
    salaire_base: '',
    cout_heures_supplementaires: '0',
    superviseur: '',
    conges_consommes: '0',
    
    // 🏦 Informations bancaires
    nom_banque: '',
    rib: '',
    adresse_banque: '',
    ville_banque: ''
  });

  const [photo, setPhoto] = useState(null);
  const [photoPreview, setPhotoPreview] = useState(null);
  const [departments, setDepartments] = useState([]);
  const [services, setServices] = useState([]);
  const [superviseurs, setSuperviseurs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [currentStep, setCurrentStep] = useState(1);
  const [validationErrors, setValidationErrors] = useState({});

  // Charger les données initiales
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const token = localStorage.getItem('token');
        
        // Charger les départements
        const deptResponse = await axios.get('http://localhost:4000/api/departments', {
          headers: { Authorization: `Bearer ${token}` }
        });
        if (deptResponse.data.success) {
          setDepartments(deptResponse.data.data);
        }
      } catch (error) {
        console.error('Erreur lors du chargement des données initiales:', error);
        setError('Erreur lors du chargement des données');
      }
    };

    fetchInitialData();
  }, []);

  // Charger les services quand le département change
  useEffect(() => {
    if (!formData.departement) {
      setServices([]);
      return;
    }

    // Utiliser des données de test pour éviter les erreurs d'API
    console.log('🧪 Utilisation de données de test pour les services');

    const testServices = {
      '1': [ // Informatique
        { _id: '1', nom_service: 'Développement Web' },
        { _id: '2', nom_service: 'Support Technique' },
        { _id: '3', nom_service: 'Infrastructure' }
      ],
      '2': [ // RH
        { _id: '4', nom_service: 'Recrutement' },
        { _id: '5', nom_service: 'Formation' },
        { _id: '6', nom_service: 'Paie' }
      ],
      '3': [ // Comptabilité
        { _id: '7', nom_service: 'Comptabilité Générale' },
        { _id: '8', nom_service: 'Contrôle de Gestion' }
      ],
      '4': [ // Marketing
        { _id: '9', nom_service: 'Communication' },
        { _id: '10', nom_service: 'Digital Marketing' }
      ]
    };

    const servicesForDept = testServices[formData.departement] || [];

    // Simuler un délai d'API pour le réalisme
    setTimeout(() => {
      setServices(servicesForDept);
      console.log(`✅ ${servicesForDept.length} services chargés pour le département ${formData.departement}`);
    }, 200);
  }, [formData.departement]);

  // Charger les superviseurs potentiels quand le département change
  useEffect(() => {
    if (!formData.departement) {
      setSuperviseurs([]);
      return;
    }

    // Utiliser des données de test pour éviter les erreurs d'API
    console.log('🧪 Utilisation de données de test pour les superviseurs');

    const testSuperviseurs = [
      {
        _id: '1',
        nom: 'Martin',
        prenom: 'Sophie',
        fonction: 'Chef d\'équipe',
        email: '<EMAIL>',
        mobile: '0612345678'
      },
      {
        _id: '2',
        nom: 'Benali',
        prenom: 'Ahmed',
        fonction: 'Responsable RH',
        email: '<EMAIL>',
        mobile: '0612345679'
      },
      {
        _id: '3',
        nom: 'Alami',
        prenom: 'Fatima',
        fonction: 'Directrice Marketing',
        email: '<EMAIL>',
        mobile: '0612345680'
      }
    ];

    // Simuler un délai d'API pour le réalisme
    setTimeout(() => {
      setSuperviseurs(testSuperviseurs);
      console.log(`✅ ${testSuperviseurs.length} superviseurs chargés pour le département ${formData.departement}`);
    }, 300);
  }, [formData.departement]);

  const handleChange = (e) => {
    e.preventDefault(); // Empêcher la soumission du formulaire
    e.stopPropagation(); // Empêcher la propagation de l'événement

    const { name, value } = e.target;
    console.log('handleChange appelé:', name, value);

    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Effacer l'erreur de validation pour ce champ
    if (validationErrors[name]) {
      setValidationErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handlePhotoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setPhoto(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setPhotoPreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Validation des champs
  const validateStep = (step) => {
    const errors = {};
    
    if (step === 1) {
      // Validation des informations personnelles
      if (!formData.prenom.trim()) errors.prenom = 'Le prénom est requis';
      if (!formData.nom.trim()) errors.nom = 'Le nom est requis';
      if (!formData.email.trim()) errors.email = 'L\'email est requis';
      else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) errors.email = 'Format d\'email invalide';
      if (!formData.password.trim()) errors.password = 'Le mot de passe est requis';
      else if (formData.password.length < 6) errors.password = 'Le mot de passe doit contenir au moins 6 caractères';
      if (!formData.sexe) errors.sexe = 'Le sexe est requis';
      if (!formData.cin.trim()) errors.cin = 'Le CIN est requis';
      if (!formData.statut_matrimonial) errors.statut_matrimonial = 'Le statut matrimonial est requis';
      if (!formData.mobile.trim()) errors.mobile = 'Le numéro mobile est requis';
      if (!formData.adresse.trim()) errors.adresse = 'L\'adresse est requise';
      if (!formData.ville.trim()) errors.ville = 'La ville est requise';
      if (!formData.date_naissance) errors.date_naissance = 'La date de naissance est requise';
    }
    
    if (step === 2) {
      // Validation des informations professionnelles
      if (!formData.departement) errors.departement = 'Le département est requis';
      if (!formData.fonction.trim()) errors.fonction = 'La fonction est requise';
      if (!formData.type_contrat) errors.type_contrat = 'Le type de contrat est requis';
      if (!formData.date_entree) errors.date_entree = 'La date d\'entrée est requise';
      if (!formData.salaire_base || parseFloat(formData.salaire_base) <= 0) {
        errors.salaire_base = 'Le salaire de base est requis et doit être supérieur à 0';
      }
    }
    
    // Validation du RIB si fourni
    if (formData.rib && !/^\d{24}$/.test(formData.rib.replace(/\s/g, ''))) {
      errors.rib = 'Le RIB doit contenir 24 chiffres';
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const nextStep = (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    console.log('nextStep appelé, étape actuelle:', currentStep);
    if (validateStep(currentStep)) {
      console.log('Validation réussie, passage à l\'étape suivante');
      setCurrentStep(prev => Math.min(prev + 1, 3));
    } else {
      console.log('Validation échouée, erreurs:', validationErrors);
    }
  };

  const prevStep = (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    console.log('prevStep appelé, étape actuelle:', currentStep);
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Valider toutes les étapes
    if (!validateStep(1) || !validateStep(2)) {
      setError('Veuillez corriger les erreurs dans le formulaire');
      return;
    }
    
    setLoading(true);
    setError('');

    try {
      const token = localStorage.getItem('token');
      const submitData = new FormData();
      
      // Ajouter tous les champs du formulaire
      Object.keys(formData).forEach(key => {
        if (formData[key] !== '' && formData[key] !== null && formData[key] !== undefined) {
          submitData.append(key, formData[key]);
        }
      });

      // Ajouter la photo si elle existe
      if (photo) {
        submitData.append('photo', photo);
      }

      const response = await axios.post('http://localhost:4000/api/employees', submitData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data'
        }
      });

      if (response.data.success) {
        onSuccess();
        onClose();
      }
    } catch (error) {
      console.error('Erreur lors de la création:', error);
      const errorMessage = error.response?.data?.message || 'Erreur lors de la création de l\'employé';
      setError(errorMessage);
      
      // Si c'est une erreur de validation du serveur, afficher les détails
      if (error.response?.status === 400) {
        setValidationErrors({ general: errorMessage });
      }
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour afficher les erreurs de validation
  const renderFieldError = (fieldName) => {
    if (validationErrors[fieldName]) {
      return (
        <div style={{
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '0.25rem'
        }}>
          {validationErrors[fieldName]}
        </div>
      );
    }
    return null;
  };

  // Styles communs pour les champs
  const inputStyle = (hasError) => ({
    width: '100%',
    padding: '0.75rem',
    border: `1px solid ${hasError ? '#dc2626' : '#d1d5db'}`,
    borderRadius: '6px',
    fontSize: '1rem',
    backgroundColor: hasError ? '#fef2f2' : 'white'
  });

  const labelStyle = {
    display: 'block',
    marginBottom: '0.5rem',
    fontWeight: '500',
    color: '#374151'
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '1rem'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        width: '100%',
        maxWidth: '800px',
        maxHeight: '90vh',
        overflow: 'auto',
        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)'
      }}>
        {/* Header avec indicateur d'étapes */}
        <div style={{
          padding: '1.5rem',
          borderBottom: '1px solid #e5e7eb'
        }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '1rem'
          }}>
            <h2 style={{ margin: 0, fontSize: '1.5rem', fontWeight: '600' }}>
              Ajouter un employé
            </h2>
            <button
              onClick={onClose}
              style={{
                background: 'none',
                border: 'none',
                fontSize: '1.5rem',
                cursor: 'pointer',
                color: '#6b7280',
                padding: '0.5rem'
              }}
            >
              <FaTimes />
            </button>
          </div>

          {/* Indicateur d'étapes */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '1rem'
          }}>
            {[
              { step: 1, icon: FaIdCard, label: 'Informations personnelles' },
              { step: 2, icon: FaBriefcase, label: 'Informations professionnelles' },
              { step: 3, icon: FaUniversity, label: 'Informations bancaires' }
            ].map(({ step, icon: Icon, label }, index) => (
              <div key={step} style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
                <button
                  type="button"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Clic sur étape:', step, 'étape actuelle:', currentStep);

                    // Permettre de naviguer vers les étapes précédentes ou l'étape suivante si la validation passe
                    if (step < currentStep || (step === currentStep + 1 && validateStep(currentStep))) {
                      setCurrentStep(step);
                    } else if (step === currentStep) {
                      // Déjà sur cette étape, ne rien faire
                      console.log('Déjà sur cette étape');
                    } else {
                      console.log('Navigation non autorisée vers l\'étape', step);
                    }
                  }}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    padding: '0.5rem 1rem',
                    borderRadius: '8px',
                    backgroundColor: currentStep === step ? '#3b82f6' : currentStep > step ? '#10b981' : '#f3f4f6',
                    color: currentStep >= step ? 'white' : '#6b7280',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    border: 'none',
                    cursor: step <= currentStep || (step === currentStep + 1) ? 'pointer' : 'default',
                    opacity: step <= currentStep || (step === currentStep + 1) ? 1 : 0.6,
                    transition: 'all 0.2s ease'
                  }}
                  disabled={step > currentStep + 1}
                >
                  <Icon />
                  <span>{label}</span>
                </button>
                {index < 2 && (
                  <div style={{
                    flex: 1,
                    height: '2px',
                    backgroundColor: currentStep > step + 1 ? '#10b981' : '#e5e7eb',
                    margin: '0 0.5rem'
                  }} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Formulaire */}
        <form onSubmit={handleSubmit} style={{ padding: '1.5rem' }}>
          {/* Debug info - à supprimer en production */}
          <div style={{
            backgroundColor: '#f0f9ff',
            padding: '0.5rem',
            borderRadius: '4px',
            marginBottom: '1rem',
            fontSize: '0.875rem',
            color: '#0369a1',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <span>Étape actuelle: {currentStep} / 3</span>
            <div style={{ display: 'flex', gap: '0.5rem' }}>
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setCurrentStep(1);
                }}
                style={{
                  padding: '0.25rem 0.5rem',
                  fontSize: '0.75rem',
                  border: '1px solid #0369a1',
                  borderRadius: '4px',
                  backgroundColor: currentStep === 1 ? '#0369a1' : 'white',
                  color: currentStep === 1 ? 'white' : '#0369a1',
                  cursor: 'pointer'
                }}
              >
                1
              </button>
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setCurrentStep(2);
                }}
                style={{
                  padding: '0.25rem 0.5rem',
                  fontSize: '0.75rem',
                  border: '1px solid #0369a1',
                  borderRadius: '4px',
                  backgroundColor: currentStep === 2 ? '#0369a1' : 'white',
                  color: currentStep === 2 ? 'white' : '#0369a1',
                  cursor: 'pointer'
                }}
              >
                2
              </button>
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setCurrentStep(3);
                }}
                style={{
                  padding: '0.25rem 0.5rem',
                  fontSize: '0.75rem',
                  border: '1px solid #0369a1',
                  borderRadius: '4px',
                  backgroundColor: currentStep === 3 ? '#0369a1' : 'white',
                  color: currentStep === 3 ? 'white' : '#0369a1',
                  cursor: 'pointer'
                }}
              >
                3
              </button>
            </div>
          </div>

          {/* Message d'erreur global */}
          {error && (
            <div style={{
              backgroundColor: '#fef2f2',
              color: '#dc2626',
              padding: '1rem',
              borderRadius: '8px',
              marginBottom: '1rem',
              border: '1px solid #fecaca'
            }}>
              {error}
            </div>
          )}

          {/* Upload de photo - visible sur toutes les étapes */}
          <div style={{ marginBottom: '1.5rem', textAlign: 'center' }}>
            <div style={{
              width: '120px',
              height: '120px',
              borderRadius: '50%',
              backgroundColor: '#f3f4f6',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 1rem',
              overflow: 'hidden',
              border: '2px dashed #d1d5db'
            }}>
              {photoPreview ? (
                <img
                  src={photoPreview}
                  alt="Aperçu"
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover'
                  }}
                />
              ) : (
                <FaUser style={{ fontSize: '2rem', color: '#9ca3af' }} />
              )}
            </div>
            <label style={{
              display: 'inline-flex',
              alignItems: 'center',
              gap: '0.5rem',
              padding: '0.5rem 1rem',
              backgroundColor: '#f3f4f6',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '0.9rem',
              color: '#374151'
            }}>
              <FaUpload />
              Choisir une photo
              <input
                type="file"
                accept="image/*"
                onChange={handlePhotoChange}
                style={{ display: 'none' }}
              />
            </label>
          </div>

          {/* Étape 1: Informations personnelles */}
          {currentStep === 1 && (
            <div>
              <h3 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                marginBottom: '1.5rem',
                color: '#1f2937',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}>
                <FaIdCard style={{ color: '#3b82f6' }} />
                🧍 Informations personnelles
              </h3>

              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                gap: '1rem',
                marginBottom: '1.5rem'
              }}>
                {/* Prénom */}
                <div>
                  <label style={labelStyle}>Prénom *</label>
                  <input
                    type="text"
                    name="prenom"
                    value={formData.prenom}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.prenom)}
                    placeholder="Entrez le prénom"
                  />
                  {renderFieldError('prenom')}
                </div>

                {/* Nom */}
                <div>
                  <label style={labelStyle}>Nom *</label>
                  <input
                    type="text"
                    name="nom"
                    value={formData.nom}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.nom)}
                    placeholder="Entrez le nom"
                  />
                  {renderFieldError('nom')}
                </div>

                {/* Email */}
                <div>
                  <label style={labelStyle}>Email *</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.email)}
                    placeholder="<EMAIL>"
                  />
                  {renderFieldError('email')}
                </div>

                {/* Mot de passe */}
                <div>
                  <label style={labelStyle}>Mot de passe *</label>
                  <input
                    type="password"
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.password)}
                    placeholder="Minimum 6 caractères"
                  />
                  {renderFieldError('password')}
                </div>

                {/* Sexe */}
                <div>
                  <label style={labelStyle}>Sexe *</label>
                  <select
                    name="sexe"
                    value={formData.sexe}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.sexe)}
                  >
                    <option value="">Sélectionner le sexe</option>
                    <option value="Masculin">Masculin</option>
                    <option value="Féminin">Féminin</option>
                  </select>
                  {renderFieldError('sexe')}
                </div>

                {/* CIN */}
                <div>
                  <label style={labelStyle}>CIN *</label>
                  <input
                    type="text"
                    name="cin"
                    value={formData.cin}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.cin)}
                    placeholder="Numéro CIN"
                  />
                  {renderFieldError('cin')}
                </div>

                {/* CNSS */}
                <div>
                  <label style={labelStyle}>CNSS</label>
                  <input
                    type="text"
                    name="cnss"
                    value={formData.cnss}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.cnss)}
                    placeholder="Numéro CNSS (optionnel)"
                  />
                  {renderFieldError('cnss')}
                </div>

                {/* Statut matrimonial */}
                <div>
                  <label style={labelStyle}>Statut matrimonial *</label>
                  <select
                    name="statut_matrimonial"
                    value={formData.statut_matrimonial}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.statut_matrimonial)}
                  >
                    <option value="">Sélectionner le statut</option>
                    <option value="Célibataire">Célibataire</option>
                    <option value="Marié(e)">Marié(e)</option>
                    <option value="Divorcé(e)">Divorcé(e)</option>
                    <option value="Veuf/Veuve">Veuf/Veuve</option>
                  </select>
                  {renderFieldError('statut_matrimonial')}
                </div>

                {/* Téléphone */}
                <div>
                  <label style={labelStyle}>Téléphone</label>
                  <input
                    type="tel"
                    name="telephone"
                    value={formData.telephone}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.telephone)}
                    placeholder="Téléphone fixe (optionnel)"
                  />
                  {renderFieldError('telephone')}
                </div>

                {/* Mobile */}
                <div>
                  <label style={labelStyle}>Mobile *</label>
                  <input
                    type="tel"
                    name="mobile"
                    value={formData.mobile}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.mobile)}
                    placeholder="Numéro mobile"
                  />
                  {renderFieldError('mobile')}
                </div>

                {/* Ville */}
                <div>
                  <label style={labelStyle}>Ville *</label>
                  <input
                    type="text"
                    name="ville"
                    value={formData.ville}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.ville)}
                    placeholder="Ville de résidence"
                  />
                  {renderFieldError('ville')}
                </div>

                {/* Pays */}
                <div>
                  <label style={labelStyle}>Pays *</label>
                  <input
                    type="text"
                    name="pays"
                    value={formData.pays}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.pays)}
                    placeholder="Pays"
                  />
                  {renderFieldError('pays')}
                </div>

                {/* Date de naissance */}
                <div>
                  <label style={labelStyle}>Date de naissance *</label>
                  <input
                    type="date"
                    name="date_naissance"
                    value={formData.date_naissance}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.date_naissance)}
                  />
                  {renderFieldError('date_naissance')}
                </div>
              </div>

              {/* Adresse */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={labelStyle}>Adresse *</label>
                <textarea
                  name="adresse"
                  value={formData.adresse}
                  onChange={handleChange}
                  rows="3"
                  style={{
                    ...inputStyle(validationErrors.adresse),
                    resize: 'vertical'
                  }}
                  placeholder="Adresse complète"
                />
                {renderFieldError('adresse')}
              </div>
            </div>
          )}

          {/* Étape 2: Informations professionnelles */}
          {currentStep === 2 && (
            <div>
              <h3 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                marginBottom: '1.5rem',
                color: '#1f2937',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}>
                <FaBriefcase style={{ color: '#3b82f6' }} />
                💼 Informations professionnelles
              </h3>

              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                gap: '1rem',
                marginBottom: '1.5rem'
              }}>
                {/* Département */}
                <div>
                  <label style={labelStyle}>Département *</label>
                  <select
                    name="departement"
                    value={formData.departement}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.departement)}
                  >
                    <option value="">Sélectionner un département</option>
                    {departments.map(dept => (
                      <option key={dept._id} value={dept._id}>
                        {dept.nom_departement}
                      </option>
                    ))}
                  </select>
                  {renderFieldError('departement')}
                </div>

                {/* Service */}
                <div>
                  <label style={labelStyle}>Service</label>
                  <select
                    name="service"
                    value={formData.service}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.service)}
                    disabled={!formData.departement}
                  >
                    <option value="">Sélectionner un service</option>
                    {services && services.length > 0 ? services.map(service => (
                      <option key={service._id} value={service._id}>
                        {service.nom_service}
                      </option>
                    )) : (
                      <option value="" disabled>Aucun service disponible</option>
                    )}
                  </select>
                  {renderFieldError('service')}
                </div>

                {/* Fonction */}
                <div>
                  <label style={labelStyle}>Fonction *</label>
                  <input
                    type="text"
                    name="fonction"
                    value={formData.fonction}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.fonction)}
                    placeholder="Poste/Fonction"
                  />
                  {renderFieldError('fonction')}
                </div>

                {/* Type de contrat */}
                <div>
                  <label style={labelStyle}>Type de contrat *</label>
                  <select
                    name="type_contrat"
                    value={formData.type_contrat}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.type_contrat)}
                  >
                    <option value="">Sélectionner le type</option>
                    <option value="CDI">CDI</option>
                    <option value="CDD">CDD</option>
                    <option value="Stage">Stage</option>
                    <option value="Freelance">Freelance</option>
                    <option value="Apprentissage">Apprentissage</option>
                    <option value="Interim">Interim</option>
                  </select>
                  {renderFieldError('type_contrat')}
                </div>

                {/* Date d'entrée */}
                <div>
                  <label style={labelStyle}>Date d'entrée *</label>
                  <input
                    type="date"
                    name="date_entree"
                    value={formData.date_entree}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.date_entree)}
                  />
                  {renderFieldError('date_entree')}
                </div>

                {/* Salaire de base */}
                <div>
                  <label style={labelStyle}>
                    Salaire de base (DH) *
                    <span style={{ color: '#dc2626', fontSize: '0.875rem' }}>
                      (Information très importante)
                    </span>
                  </label>
                  <input
                    type="number"
                    name="salaire_base"
                    value={formData.salaire_base}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.salaire_base)}
                    placeholder="Salaire en Dirhams"
                    min="0"
                    step="0.01"
                  />
                  {renderFieldError('salaire_base')}
                </div>

                {/* Coût heures supplémentaires */}
                <div>
                  <label style={labelStyle}>Coût heures supplémentaires (DH)</label>
                  <input
                    type="number"
                    name="cout_heures_supplementaires"
                    value={formData.cout_heures_supplementaires}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.cout_heures_supplementaires)}
                    placeholder="Coût par heure"
                    min="0"
                    step="0.01"
                  />
                  {renderFieldError('cout_heures_supplementaires')}
                </div>

                {/* Superviseur */}
                <div>
                  <label style={labelStyle}>
                    Superviseur
                    <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                      (Employés du même département)
                    </span>
                  </label>
                  <select
                    name="superviseur"
                    value={formData.superviseur}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.superviseur)}
                    disabled={!formData.departement}
                  >
                    <option value="">Sélectionner un superviseur</option>
                    {superviseurs && superviseurs.length > 0 ? superviseurs.map(sup => (
                      <option key={sup._id} value={sup._id}>
                        {sup.nom} {sup.prenom} - {sup.fonction}
                      </option>
                    )) : (
                      <option value="" disabled>
                        {formData.departement ? 'Aucun superviseur disponible' : 'Sélectionnez d\'abord un département'}
                      </option>
                    )}
                  </select>
                  {renderFieldError('superviseur')}
                </div>

                {/* Congés consommés */}
                <div>
                  <label style={labelStyle}>Congés consommés (jours)</label>
                  <input
                    type="number"
                    name="conges_consommes"
                    value={formData.conges_consommes}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.conges_consommes)}
                    placeholder="0"
                    min="0"
                    step="1"
                  />
                  {renderFieldError('conges_consommes')}
                </div>
              </div>
            </div>
          )}

          {/* Étape 3: Informations bancaires */}
          {currentStep === 3 && (
            <div>
              <h3 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                marginBottom: '1.5rem',
                color: '#1f2937',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}>
                <FaUniversity style={{ color: '#3b82f6' }} />
                🏦 Informations bancaires
              </h3>

              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                gap: '1rem',
                marginBottom: '1.5rem'
              }}>
                {/* Nom de la banque */}
                <div>
                  <label style={labelStyle}>Nom de la banque</label>
                  <input
                    type="text"
                    name="nom_banque"
                    value={formData.nom_banque}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.nom_banque)}
                    placeholder="Nom de la banque"
                  />
                  {renderFieldError('nom_banque')}
                </div>

                {/* RIB */}
                <div>
                  <label style={labelStyle}>
                    RIB
                    <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                      (24 chiffres)
                    </span>
                  </label>
                  <input
                    type="text"
                    name="rib"
                    value={formData.rib}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.rib)}
                    placeholder="Numéro RIB (24 chiffres)"
                    maxLength="24"
                  />
                  {renderFieldError('rib')}
                </div>

                {/* Adresse banque */}
                <div>
                  <label style={labelStyle}>Adresse banque</label>
                  <input
                    type="text"
                    name="adresse_banque"
                    value={formData.adresse_banque}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.adresse_banque)}
                    placeholder="Adresse de l'agence"
                  />
                  {renderFieldError('adresse_banque')}
                </div>

                {/* Ville banque */}
                <div>
                  <label style={labelStyle}>Ville banque</label>
                  <input
                    type="text"
                    name="ville_banque"
                    value={formData.ville_banque}
                    onChange={handleChange}
                    style={inputStyle(validationErrors.ville_banque)}
                    placeholder="Ville de l'agence"
                  />
                  {renderFieldError('ville_banque')}
                </div>
              </div>

              {/* Note informative */}
              <div style={{
                backgroundColor: '#f0f9ff',
                border: '1px solid #bae6fd',
                borderRadius: '8px',
                padding: '1rem',
                marginBottom: '1.5rem'
              }}>
                <p style={{
                  margin: 0,
                  fontSize: '0.875rem',
                  color: '#0369a1'
                }}>
                  <strong>Note:</strong> Les informations bancaires sont optionnelles mais recommandées
                  pour faciliter les virements de salaire et autres paiements.
                </p>
              </div>
            </div>
          )}

          {/* Boutons de navigation */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingTop: '1.5rem',
            borderTop: '1px solid #e5e7eb'
          }}>
            <div>
              {currentStep > 1 && (
                <button
                  type="button"
                  onClick={prevStep}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    padding: '0.75rem 1.5rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    backgroundColor: 'white',
                    color: '#374151',
                    cursor: 'pointer',
                    fontSize: '1rem'
                  }}
                >
                  <FaArrowLeft />
                  Précédent
                </button>
              )}
            </div>

            <div style={{ display: 'flex', gap: '1rem' }}>
              <button
                type="button"
                onClick={onClose}
                style={{
                  padding: '0.75rem 1.5rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  backgroundColor: 'white',
                  color: '#374151',
                  cursor: 'pointer',
                  fontSize: '1rem'
                }}
              >
                Annuler
              </button>

              {currentStep < 3 ? (
                <button
                  type="button"
                  onClick={nextStep}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    padding: '0.75rem 1.5rem',
                    border: 'none',
                    borderRadius: '6px',
                    backgroundColor: '#3b82f6',
                    color: 'white',
                    cursor: 'pointer',
                    fontSize: '1rem',
                    fontWeight: '600'
                  }}
                >
                  Suivant
                  <FaArrowRight />
                </button>
              ) : (
                <button
                  type="submit"
                  disabled={loading}
                  style={{
                    padding: '0.75rem 1.5rem',
                    border: 'none',
                    borderRadius: '6px',
                    backgroundColor: loading ? '#9ca3af' : '#10b981',
                    color: 'white',
                    cursor: loading ? 'not-allowed' : 'pointer',
                    fontSize: '1rem',
                    fontWeight: '600'
                  }}
                >
                  {loading ? 'Création...' : 'Créer l\'employé'}
                </button>
              )}
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddEmployeeModal;
