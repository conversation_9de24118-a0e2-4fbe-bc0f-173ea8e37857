import React, { useState, useEffect } from 'react';
import { FaCalendarAlt, FaPlus, FaClock, FaCheck, FaTimes, FaCalendarCheck } from 'react-icons/fa';

const EmployeeLeaves = ({ employeeData }) => {
  const [leaves, setLeaves] = useState([]);
  const [showRequestModal, setShowRequestModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [leaveBalance, setLeaveBalance] = useState(25); // Solde de congés

  useEffect(() => {
    loadLeaves();
  }, []);

  const loadLeaves = async () => {
    setLoading(true);
    try {
      // TODO: Implémenter l'API pour récupérer les congés de l'employé
      // const response = await leaveService.getMyLeaves();
      // setLeaves(response.data);
      
      // Données de test pour l'instant
      setLeaves([
        {
          _id: '1',
          type_conge: { nom: 'Congés payés' },
          date_debut: '2024-07-15',
          date_fin: '2024-07-25',
          statut: 'validé',
          commentaire: 'Vacances d\'été'
        },
        {
          _id: '2',
          type_conge: { nom: 'Congé maladie' },
          date_debut: '2024-06-10',
          date_fin: '2024-06-12',
          statut: 'en attente',
          commentaire: 'Consultation médicale'
        }
      ]);
    } catch (error) {
      console.error('Erreur lors du chargement des congés:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'validé': return { bg: '#dcfce7', color: '#166534' };
      case 'en attente': return { bg: '#fef3c7', color: '#92400e' };
      case 'refusé': return { bg: '#fef2f2', color: '#dc2626' };
      default: return { bg: '#f3f4f6', color: '#374151' };
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'validé': return <FaCheck />;
      case 'en attente': return <FaClock />;
      case 'refusé': return <FaTimes />;
      default: return <FaClock />;
    }
  };

  const LeaveRequestModal = () => {
    const [formData, setFormData] = useState({
      type_conge: '',
      date_debut: '',
      date_fin: '',
      commentaire: ''
    });

    const handleSubmit = async (e) => {
      e.preventDefault();
      try {
        // TODO: Implémenter l'API pour créer une demande de congé
        console.log('Demande de congé:', formData);
        setShowRequestModal(false);
        loadLeaves(); // Recharger la liste
      } catch (error) {
        console.error('Erreur lors de la création de la demande:', error);
      }
    };

    return (
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000
      }}>
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '2rem',
          width: '90%',
          maxWidth: '500px',
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
        }}>
          <h2 style={{
            margin: '0 0 1.5rem 0',
            fontSize: '1.5rem',
            color: '#374151',
            fontWeight: '600'
          }}>
            Nouvelle demande de congé
          </h2>

          <form onSubmit={handleSubmit}>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: '#374151'
              }}>
                Type de congé
              </label>
              <select
                value={formData.type_conge}
                onChange={(e) => setFormData({ ...formData, type_conge: e.target.value })}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '1rem'
                }}
              >
                <option value="">Sélectionner un type</option>
                <option value="conges_payes">Congés payés</option>
                <option value="conge_maladie">Congé maladie</option>
                <option value="conge_maternite">Congé maternité</option>
                <option value="conge_sans_solde">Congé sans solde</option>
              </select>
            </div>

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem', marginBottom: '1rem' }}>
              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151'
                }}>
                  Date de début
                </label>
                <input
                  type="date"
                  value={formData.date_debut}
                  onChange={(e) => setFormData({ ...formData, date_debut: e.target.value })}
                  required
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '1rem'
                  }}
                />
              </div>
              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151'
                }}>
                  Date de fin
                </label>
                <input
                  type="date"
                  value={formData.date_fin}
                  onChange={(e) => setFormData({ ...formData, date_fin: e.target.value })}
                  required
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '1rem'
                  }}
                />
              </div>
            </div>

            <div style={{ marginBottom: '1.5rem' }}>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: '#374151'
              }}>
                Commentaire (optionnel)
              </label>
              <textarea
                value={formData.commentaire}
                onChange={(e) => setFormData({ ...formData, commentaire: e.target.value })}
                rows={3}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  resize: 'vertical'
                }}
                placeholder="Motif de la demande..."
              />
            </div>

            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end' }}>
              <button
                type="button"
                onClick={() => setShowRequestModal(false)}
                style={{
                  padding: '0.75rem 1.5rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  backgroundColor: 'white',
                  color: '#374151',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}
              >
                Annuler
              </button>
              <button
                type="submit"
                style={{
                  padding: '0.75rem 1.5rem',
                  border: 'none',
                  borderRadius: '8px',
                  backgroundColor: '#3b82f6',
                  color: 'white',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}
              >
                Soumettre la demande
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  return (
    <div style={{ padding: '1rem' }}>
      {/* En-tête avec solde de congés */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '1.5rem',
        marginBottom: '2rem',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        border: '1px solid #e5e7eb'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
          <h1 style={{
            margin: 0,
            fontSize: '1.5rem',
            color: '#374151',
            fontWeight: '600',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            <FaCalendarAlt style={{ color: '#3b82f6' }} />
            Gestion des congés
          </h1>
          <button
            onClick={() => setShowRequestModal(true)}
            style={{
              backgroundColor: '#3b82f6',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '0.75rem 1.5rem',
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
          >
            <FaPlus />
            Nouvelle demande
          </button>
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '1rem'
        }}>
          <div style={{
            backgroundColor: '#dbeafe',
            padding: '1rem',
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '2rem', fontWeight: '700', color: '#1e40af' }}>
              {leaveBalance}
            </div>
            <div style={{ fontSize: '0.875rem', color: '#1e40af', fontWeight: '500' }}>
              Jours disponibles
            </div>
          </div>
          <div style={{
            backgroundColor: '#fef3c7',
            padding: '1rem',
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '2rem', fontWeight: '700', color: '#92400e' }}>
              {leaves.filter(l => l.statut === 'en attente').length}
            </div>
            <div style={{ fontSize: '0.875rem', color: '#92400e', fontWeight: '500' }}>
              En attente
            </div>
          </div>
          <div style={{
            backgroundColor: '#dcfce7',
            padding: '1rem',
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '2rem', fontWeight: '700', color: '#166534' }}>
              {leaves.filter(l => l.statut === 'validé').length}
            </div>
            <div style={{ fontSize: '0.875rem', color: '#166534', fontWeight: '500' }}>
              Validés
            </div>
          </div>
        </div>
      </div>

      {/* Liste des demandes */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '1.5rem',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        border: '1px solid #e5e7eb'
      }}>
        <h2 style={{
          margin: '0 0 1.5rem 0',
          fontSize: '1.3rem',
          color: '#374151',
          fontWeight: '600'
        }}>
          Historique des demandes
        </h2>

        {loading ? (
          <p>Chargement des congés...</p>
        ) : leaves.length > 0 ? (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            {leaves.map((leave) => {
              const statusStyle = getStatusColor(leave.statut);
              return (
                <div
                  key={leave._id}
                  style={{
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    padding: '1rem',
                    backgroundColor: '#f9fafb'
                  }}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start', marginBottom: '0.5rem' }}>
                    <div>
                      <h3 style={{
                        margin: '0 0 0.25rem 0',
                        fontSize: '1.1rem',
                        color: '#374151',
                        fontWeight: '600'
                      }}>
                        {leave.type_conge?.nom || 'Type non défini'}
                      </h3>
                      <p style={{
                        margin: 0,
                        fontSize: '0.875rem',
                        color: '#6b7280'
                      }}>
                        Du {formatDate(leave.date_debut)} au {formatDate(leave.date_fin)}
                      </p>
                    </div>
                    <span style={{
                      backgroundColor: statusStyle.bg,
                      color: statusStyle.color,
                      padding: '0.25rem 0.75rem',
                      borderRadius: '20px',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.25rem'
                    }}>
                      {getStatusIcon(leave.statut)}
                      {leave.statut}
                    </span>
                  </div>
                  {leave.commentaire && (
                    <p style={{
                      margin: '0.5rem 0 0 0',
                      fontSize: '0.875rem',
                      color: '#6b7280',
                      fontStyle: 'italic'
                    }}>
                      "{leave.commentaire}"
                    </p>
                  )}
                </div>
              );
            })}
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '2rem', color: '#6b7280' }}>
            <FaCalendarCheck style={{ fontSize: '3rem', marginBottom: '1rem', opacity: 0.5 }} />
            <p>Aucune demande de congé trouvée</p>
            <p style={{ fontSize: '0.875rem' }}>Cliquez sur "Nouvelle demande" pour commencer</p>
          </div>
        )}
      </div>

      {/* Modal de demande */}
      {showRequestModal && <LeaveRequestModal />}
    </div>
  );
};

export default EmployeeLeaves;
