/**
 * Service pour la gestion des projets
 */
import api from './api';

export const projetService = {
    /**
     * Obtenir tous les projets
     * @param {Object} params - Paramètres de pagination et filtres
     * @returns {Promise} Liste des projets
     */
    getAllProjets: async (params = {}) => {
        try {
            const {
                page = 1,
                limit = 10,
                statut,
                priorite,
                chef_projet,
                departement,
                archive = false,
                search
            } = params;

            let url = `/projets?page=${page}&limit=${limit}&archive=${archive}`;
            if (statut) url += `&statut=${statut}`;
            if (priorite) url += `&priorite=${priorite}`;
            if (chef_projet) url += `&chef_projet=${chef_projet}`;
            if (departement) url += `&departement=${departement}`;
            if (search) url += `&search=${encodeURIComponent(search)}`;

            const response = await api.get(url);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la récupération des projets' };
        }
    },

    /**
     * Obtenir un projet par ID
     * @param {string} id - ID du projet
     * @returns {Promise} Données du projet
     */
    getProjetById: async (id) => {
        try {
            const response = await api.get(`/projets/${id}`);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la récupération du projet' };
        }
    },

    /**
     * Créer un nouveau projet
     * @param {Object} projetData - Données du projet
     * @returns {Promise} Projet créé
     */
    createProjet: async (projetData) => {
        try {
            const response = await api.post('/projets', projetData);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la création du projet' };
        }
    },

    /**
     * Mettre à jour un projet
     * @param {string} id - ID du projet
     * @param {Object} projetData - Nouvelles données du projet
     * @returns {Promise} Projet mis à jour
     */
    updateProjet: async (id, projetData) => {
        try {
            const response = await api.put(`/projets/${id}`, projetData);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la mise à jour du projet' };
        }
    },

    /**
     * Supprimer un projet
     * @param {string} id - ID du projet
     * @returns {Promise} Confirmation de suppression
     */
    deleteProjet: async (id) => {
        try {
            const response = await api.delete(`/projets/${id}`);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la suppression du projet' };
        }
    },

    /**
     * Archiver un projet
     * @param {string} id - ID du projet
     * @param {Object} archiveData - Données d'archivage
     * @returns {Promise} Confirmation d'archivage
     */
    archiverProjet: async (id, archiveData) => {
        try {
            const response = await api.post(`/projets/${id}/archiver`, archiveData);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de l\'archivage du projet' };
        }
    },

    /**
     * Obtenir les statistiques des projets
     * @returns {Promise} Statistiques
     */
    getProjetStats: async () => {
        try {
            const response = await api.get('/projets/stats');
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la récupération des statistiques' };
        }
    },

    /**
     * Obtenir les priorités disponibles
     * @returns {Array} Liste des priorités
     */
    getPriorites: () => {
        return [
            { value: 'Faible', label: 'Faible', color: '#10b981' },
            { value: 'Moyenne', label: 'Moyenne', color: '#f59e0b' },
            { value: 'Haute', label: 'Haute', color: '#ef4444' },
            { value: 'Urgente', label: 'Urgente', color: '#dc2626' }
        ];
    },

    /**
     * Obtenir les statuts disponibles
     * @returns {Array} Liste des statuts
     */
    getStatuts: () => {
        return [
            { value: 'En attente', label: 'En attente', color: '#6b7280' },
            { value: 'En cours', label: 'En cours', color: '#3b82f6' },
            { value: 'En pause', label: 'En pause', color: '#f59e0b' },
            { value: 'Terminé', label: 'Terminé', color: '#10b981' },
            { value: 'Annulé', label: 'Annulé', color: '#ef4444' }
        ];
    },

    /**
     * Obtenir les raisons d'archivage
     * @returns {Array} Liste des raisons
     */
    getRaisonsArchivage: () => {
        return [
            { value: 'Projet terminé', label: 'Projet terminé' },
            { value: 'Projet annulé', label: 'Projet annulé' },
            { value: 'Obsolète', label: 'Obsolète' },
            { value: 'Demande utilisateur', label: 'Demande utilisateur' },
            { value: 'Maintenance', label: 'Maintenance' },
            { value: 'Autre', label: 'Autre' }
        ];
    },

    /**
     * Formater une date pour l'affichage
     * @param {string|Date} date - Date à formater
     * @returns {string} Date formatée
     */
    formatDate: (date) => {
        if (!date) return 'Non définie';
        return new Date(date).toLocaleDateString('fr-FR', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    },

    /**
     * Formater un budget pour l'affichage
     * @param {number} budget - Budget à formater
     * @returns {string} Budget formaté
     */
    formatBudget: (budget) => {
        if (!budget) return '0 €';
        return new Intl.NumberFormat('fr-FR', {
            style: 'currency',
            currency: 'EUR'
        }).format(budget);
    },

    /**
     * Calculer la durée d'un projet
     * @param {string|Date} dateDebut - Date de début
     * @param {string|Date} dateFin - Date de fin
     * @returns {string} Durée formatée
     */
    calculateDuree: (dateDebut, dateFin) => {
        if (!dateDebut || !dateFin) return 'Non définie';

        const debut = new Date(dateDebut);
        const fin = new Date(dateFin);
        const diffTime = Math.abs(fin - debut);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays < 30) {
            return `${diffDays} jour${diffDays > 1 ? 's' : ''}`;
        } else if (diffDays < 365) {
            const months = Math.floor(diffDays / 30);
            return `${months} mois`;
        } else {
            const years = Math.floor(diffDays / 365);
            const remainingMonths = Math.floor((diffDays % 365) / 30);
            return `${years} an${years > 1 ? 's' : ''}${remainingMonths > 0 ? ` et ${remainingMonths} mois` : ''}`;
        }
    },

    /**
     * Vérifier si un projet est en retard
     * @param {Object} projet - Projet à vérifier
     * @returns {boolean} True si le projet est en retard
     */
    isProjetEnRetard: (projet) => {
        if (projet.statut === 'Terminé' || projet.statut === 'Annulé') return false;

        const now = new Date();
        const dateFin = new Date(projet.date_fin_prevue);

        return now > dateFin;
    },

    /**
     * Calculer les jours restants
     * @param {Object} projet - Projet
     * @returns {number} Nombre de jours restants
     */
    getJoursRestants: (projet) => {
        if (projet.statut === 'Terminé' || projet.statut === 'Annulé') return 0;

        const now = new Date();
        const dateFin = projet.date_fin_reelle ? new Date(projet.date_fin_reelle) : new Date(projet.date_fin_prevue);
        const diffTime = dateFin - now;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        return Math.max(0, diffDays);
    },

    /**
     * Calculer le pourcentage de budget utilisé
     * @param {Object} projet - Projet
     * @returns {number} Pourcentage
     */
    getPourcentageBudgetUtilise: (projet) => {
        if (!projet.budget_alloue || projet.budget_alloue === 0) return 0;
        return Math.round((projet.budget_utilise / projet.budget_alloue) * 100);
    },

    /**
     * Obtenir la couleur selon la priorité
     * @param {string} priorite - Priorité du projet
     * @returns {string} Couleur
     */
    getPrioriteColor: (priorite) => {
        const prioriteObj = projetService.getPriorites().find(p => p.value === priorite);
        return prioriteObj?.color || '#6b7280';
    },

    /**
     * Obtenir la couleur selon le statut
     * @param {string} statut - Statut du projet
     * @returns {string} Couleur
     */
    getStatutColor: (statut) => {
        const statutObj = projetService.getStatuts().find(s => s.value === statut);
        return statutObj?.color || '#6b7280';
    },

    /**
     * Obtenir l'icône selon la priorité
     * @param {string} priorite - Priorité du projet
     * @returns {string} Emoji d'icône
     */
    getPrioriteIcon: (priorite) => {
        switch (priorite) {
            case 'Faible': return '🟢';
            case 'Moyenne': return '🟡';
            case 'Haute': return '🟠';
            case 'Urgente': return '🔴';
            default: return '⚪';
        }
    },

    /**
     * Obtenir l'icône selon le statut
     * @param {string} statut - Statut du projet
     * @returns {string} Emoji d'icône
     */
    getStatutIcon: (statut) => {
        switch (statut) {
            case 'En attente': return '⏳';
            case 'En cours': return '🚀';
            case 'En pause': return '⏸️';
            case 'Terminé': return '✅';
            case 'Annulé': return '❌';
            default: return '📋';
        }
    },

    /**
     * Obtenir les archives
     * @returns {Promise} Liste des archives
     */
    getArchives: async () => {
        try {
            const response = await api.get('/projets/archives');
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la récupération des archives' };
        }
    },

    /**
     * Restaurer un projet archivé
     * @param {string} archiveId - ID de l'archive
     * @returns {Promise} Projet restauré
     */
    restaurerProjet: async (archiveId) => {
        try {
            const response = await api.post(`/projets/archives/${archiveId}/restaurer`);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la restauration du projet' };
        }
    },

    /**
     * Supprimer définitivement une archive
     * @param {string} archiveId - ID de l'archive
     * @returns {Promise} Confirmation de suppression
     */
    supprimerArchiveDefinitivement: async (archiveId) => {
        try {
            const response = await api.delete(`/projets/archives/${archiveId}`);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la suppression définitive' };
        }
    },

    /**
     * Obtenir les raisons d'archivage disponibles
     * @returns {Array} Liste des raisons d'archivage
     */
    getRaisonsArchivage: () => {
        return [
            { value: 'Projet terminé', label: 'Projet terminé' },
            { value: 'Projet annulé', label: 'Projet annulé' },
            { value: 'Projet suspendu', label: 'Projet suspendu' },
            { value: 'Fin de contrat', label: 'Fin de contrat' },
            { value: 'Changement de priorités', label: 'Changement de priorités' },
            { value: 'Budget épuisé', label: 'Budget épuisé' },
            { value: 'Autre', label: 'Autre raison' }
        ];
    },

    /**
     * Calculer le pourcentage de budget utilisé
     * @param {Object} projet - Projet
     * @returns {number} Pourcentage de budget utilisé
     */
    getPourcentageBudgetUtilise: (projet) => {
        if (!projet.budget_alloue || projet.budget_alloue === 0) return 0;
        return Math.round((projet.budget_utilise / projet.budget_alloue) * 100);
    }
};
