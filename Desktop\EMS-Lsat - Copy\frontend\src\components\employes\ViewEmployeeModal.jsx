import React from 'react';
import { FaTimes, FaUser, FaEnvelope, FaPhone, FaMapMarkerAlt, FaCalendar, FaBuilding, FaBriefcase, FaIdCard } from 'react-icons/fa';

const ViewEmployeeModal = ({ employee, onClose }) => {
  const formatDate = (dateString) => {
    if (!dateString) return 'Non défini';
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const getStatusColor = (status) => {
    return status === 'actif' ? '#10b981' : '#ef4444';
  };

  const getStatusBgColor = (status) => {
    return status === 'actif' ? '#d1fae5' : '#fee2e2';
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '1rem'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        width: '100%',
        maxWidth: '700px',
        maxHeight: '90vh',
        overflow: 'auto',
        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '1.5rem',
          borderBottom: '1px solid #e5e7eb'
        }}>
          <h2 style={{ margin: 0, fontSize: '1.5rem', fontWeight: '600' }}>
            Profil de l'employé
          </h2>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '1.5rem',
              cursor: 'pointer',
              color: '#6b7280',
              padding: '0.5rem'
            }}
          >
            <FaTimes />
          </button>
        </div>

        {/* Contenu */}
        <div style={{ padding: '1.5rem' }}>
          {/* Section profil principal */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            marginBottom: '2rem',
            padding: '1.5rem',
            backgroundColor: '#f9fafb',
            borderRadius: '12px'
          }}>
            {/* Photo */}
            <div style={{
              width: '120px',
              height: '120px',
              borderRadius: '50%',
              backgroundColor: '#f3f4f6',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '2rem',
              overflow: 'hidden',
              border: '4px solid white',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
            }}>
              {employee.photo ? (
                <img
                  src={`http://localhost:4000${employee.photo}`}
                  alt={`${employee.prenom} ${employee.nom}`}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover'
                  }}
                />
              ) : (
                <FaUser style={{ fontSize: '3rem', color: '#9ca3af' }} />
              )}
            </div>

            {/* Informations principales */}
            <div style={{ flex: 1 }}>
              <h3 style={{
                margin: '0 0 0.5rem 0',
                fontSize: '2rem',
                fontWeight: '700',
                color: '#1f2937'
              }}>
                {employee.prenom} {employee.nom}
              </h3>
              <p style={{
                margin: '0 0 1rem 0',
                fontSize: '1.2rem',
                color: '#6b7280',
                fontWeight: '500'
              }}>
                {employee.poste || 'Poste non défini'}
              </p>
              <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                <span style={{
                  backgroundColor: getStatusBgColor(employee.statut_compte),
                  color: getStatusColor(employee.statut_compte),
                  padding: '0.5rem 1rem',
                  borderRadius: '20px',
                  fontSize: '0.9rem',
                  fontWeight: '600',
                  textTransform: 'capitalize'
                }}>
                  {employee.statut_compte || 'Actif'}
                </span>
                <span style={{
                  backgroundColor: '#e0f2fe',
                  color: '#0277bd',
                  padding: '0.5rem 1rem',
                  borderRadius: '20px',
                  fontSize: '0.9rem',
                  fontWeight: '600'
                }}>
                  {employee.departement?.nom_departement || 'Aucun département'}
                </span>
              </div>
            </div>
          </div>

          {/* Grille d'informations */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '1.5rem'
          }}>
            {/* Informations personnelles */}
            <div style={{
              backgroundColor: 'white',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              padding: '1.5rem'
            }}>
              <h4 style={{
                margin: '0 0 1rem 0',
                fontSize: '1.1rem',
                fontWeight: '600',
                color: '#374151',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}>
                <FaUser style={{ color: '#3b82f6' }} />
                Informations personnelles
              </h4>
              
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
                <InfoItem
                  icon={<FaIdCard />}
                  label="CIN"
                  value={employee.cin || 'Non renseigné'}
                />
                <InfoItem
                  icon={<FaCalendar />}
                  label="Date de naissance"
                  value={formatDate(employee.date_naissance)}
                />
                <InfoItem
                  icon={<FaPhone />}
                  label="Téléphone"
                  value={employee.telephone || 'Non renseigné'}
                />
                <InfoItem
                  icon={<FaMapMarkerAlt />}
                  label="Adresse"
                  value={employee.adresse || 'Non renseignée'}
                />
              </div>
            </div>

            {/* Informations professionnelles */}
            <div style={{
              backgroundColor: 'white',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              padding: '1.5rem'
            }}>
              <h4 style={{
                margin: '0 0 1rem 0',
                fontSize: '1.1rem',
                fontWeight: '600',
                color: '#374151',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}>
                <FaBriefcase style={{ color: '#10b981' }} />
                Informations professionnelles
              </h4>
              
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
                <InfoItem
                  icon={<FaBuilding />}
                  label="Département"
                  value={employee.departement?.nom_departement || 'Non assigné'}
                />
                <InfoItem
                  icon={<FaBriefcase />}
                  label="Poste"
                  value={employee.poste || 'Non défini'}
                />
                <InfoItem
                  icon={<FaCalendar />}
                  label="Date d'embauche"
                  value={formatDate(employee.date_embauche)}
                />
                <InfoItem
                  icon={<FaEnvelope />}
                  label="Email professionnel"
                  value={employee.utilisateur?.email || 'Non renseigné'}
                />
              </div>
            </div>
          </div>

          {/* Contacts d'urgence */}
          {employee.contacts_urgence && employee.contacts_urgence.length > 0 && (
            <div style={{
              marginTop: '1.5rem',
              backgroundColor: 'white',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              padding: '1.5rem'
            }}>
              <h4 style={{
                margin: '0 0 1rem 0',
                fontSize: '1.1rem',
                fontWeight: '600',
                color: '#374151'
              }}>
                Contacts d'urgence
              </h4>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                {employee.contacts_urgence.map((contact, index) => (
                  <div key={index} style={{
                    padding: '0.75rem',
                    backgroundColor: '#f9fafb',
                    borderRadius: '6px',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}>
                    <div>
                      <strong>{contact.nom}</strong> - {contact.relation}
                    </div>
                    <div style={{ color: '#6b7280' }}>
                      {contact.telephone}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Bouton de fermeture */}
          <div style={{
            marginTop: '2rem',
            textAlign: 'center'
          }}>
            <button
              onClick={onClose}
              style={{
                padding: '0.75rem 2rem',
                border: 'none',
                borderRadius: '6px',
                backgroundColor: '#3b82f6',
                color: 'white',
                cursor: 'pointer',
                fontSize: '1rem',
                fontWeight: '600'
              }}
            >
              Fermer
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Composant pour afficher une information
const InfoItem = ({ icon, label, value }) => (
  <div style={{
    display: 'flex',
    alignItems: 'center',
    gap: '0.75rem',
    padding: '0.5rem 0'
  }}>
    <div style={{ color: '#6b7280', fontSize: '0.9rem', minWidth: '20px' }}>
      {icon}
    </div>
    <div style={{ flex: 1 }}>
      <span style={{ color: '#6b7280', fontSize: '0.9rem' }}>{label}: </span>
      <span style={{ color: '#374151', fontWeight: '500' }}>{value}</span>
    </div>
  </div>
);

export default ViewEmployeeModal;
