import React, { useState } from 'react';
import { FaPlus, FaEdit, FaTrash, FaArchive, FaProjectDiagram, FaUser, FaSearch, FaFilter, FaCalendarAlt, FaEuroSign, FaClock, FaChartLine, FaTasks } from 'react-icons/fa';
import { useProjets } from '../../hooks/useProjets';
import { useEmployees } from '../../hooks/useEmployees';
import { useDepartments } from '../../hooks/useDepartments';
import { projetService } from '../../services/projetService';
import AddProjetModal from './AddProjetModal';
import EditProjetModal from './EditProjetModal';
import DeleteProjetModal from './DeleteProjetModal';
import ArchiveProjetModal from './ArchiveProjetModal';
import ProjetTachesModal from './ProjetTachesModal';

const ProjetList = () => {
  const { projets, loading, error, fetchProjets } = useProjets();
  const { employees } = useEmployees();
  const { departments } = useDepartments();
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showArchiveModal, setShowArchiveModal] = useState(false);
  const [showTachesModal, setShowTachesModal] = useState(false);
  const [selectedProjet, setSelectedProjet] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatut, setSelectedStatut] = useState('');
  const [selectedPriorite, setSelectedPriorite] = useState('');
  const [selectedChefProjet, setSelectedChefProjet] = useState('');

  const statuts = projetService.getStatuts();
  const priorites = projetService.getPriorites();

  // Filtrer les projets selon les critères
  const filteredProjets = projets.filter(projet => {
    const matchesSearch = projet.nom_projet?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         projet.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         projet.client?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         projet.chef_projet?.nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         projet.chef_projet?.prenom?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatut = !selectedStatut || projet.statut === selectedStatut;
    const matchesPriorite = !selectedPriorite || projet.priorite === selectedPriorite;
    const matchesChefProjet = !selectedChefProjet || projet.chef_projet?._id === selectedChefProjet;

    return matchesSearch && matchesStatut && matchesPriorite && matchesChefProjet;
  });

  const handleEdit = (projet) => {
    setSelectedProjet(projet);
    setShowEditModal(true);
  };

  const handleDelete = (projet) => {
    setSelectedProjet(projet);
    setShowDeleteModal(true);
  };

  const handleArchive = (projet) => {
    setSelectedProjet(projet);
    setShowArchiveModal(true);
  };

  const handleViewTaches = (projet) => {
    setSelectedProjet(projet);
    setShowTachesModal(true);
  };

  const handleModalClose = () => {
    setShowAddModal(false);
    setShowEditModal(false);
    setShowDeleteModal(false);
    setShowArchiveModal(false);
    setShowTachesModal(false);
    setSelectedProjet(null);
    fetchProjets(); // Recharger la liste
  };

  const getProgressionColor = (progression) => {
    if (progression >= 80) return '#10b981';
    if (progression >= 50) return '#f59e0b';
    if (progression >= 25) return '#3b82f6';
    return '#ef4444';
  };

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px',
        fontSize: '1.2rem',
        color: '#6b7280'
      }}>
        Chargement des projets...
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        backgroundColor: '#fef2f2',
        border: '1px solid #fecaca',
        borderRadius: '8px',
        padding: '1rem',
        color: '#dc2626'
      }}>
        <strong>Erreur:</strong> {error}
      </div>
    );
  }

  return (
    <div style={{ padding: '1rem' }}>
      {/* En-tête */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        padding: '1.5rem',
        marginBottom: '1.5rem',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '1rem'
        }}>
          <div>
            <h1 style={{
              fontSize: '2rem',
              fontWeight: 'bold',
              color: '#1a202c',
              margin: 0,
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              <FaProjectDiagram style={{ color: '#8b5cf6' }} />
              Gestion des Projets
            </h1>
            <p style={{ color: '#6b7280', margin: '0.5rem 0 0 0' }}>
              Gérez vos projets et suivez leur progression
            </p>
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            style={{
              backgroundColor: '#8b5cf6',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '0.75rem 1.5rem',
              fontSize: '1rem',
              fontWeight: '500',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              transition: 'background-color 0.2s'
            }}
            onMouseOver={(e) => e.target.style.backgroundColor = '#7c3aed'}
            onMouseOut={(e) => e.target.style.backgroundColor = '#8b5cf6'}
          >
            <FaPlus />
            Nouveau projet
          </button>
        </div>

        {/* Filtres */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: '2fr 1fr 1fr 1fr',
          gap: '1rem',
          alignItems: 'end'
        }}>
          {/* Barre de recherche */}
          <div style={{ position: 'relative' }}>
            <FaSearch style={{
              position: 'absolute',
              left: '12px',
              top: '50%',
              transform: 'translateY(-50%)',
              color: '#9ca3af',
              fontSize: '1rem'
            }} />
            <input
              type="text"
              placeholder="Rechercher un projet, client ou chef de projet..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem 0.75rem 0.75rem 2.5rem',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '1rem',
                outline: 'none',
                transition: 'border-color 0.2s'
              }}
              onFocus={(e) => e.target.style.borderColor = '#8b5cf6'}
              onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
            />
          </div>

          {/* Filtre par statut */}
          <div>
            <select
              value={selectedStatut}
              onChange={(e) => setSelectedStatut(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '1rem',
                outline: 'none',
                backgroundColor: 'white',
                cursor: 'pointer'
              }}
            >
              <option value="">Tous les statuts</option>
              {statuts.map((statut) => (
                <option key={statut.value} value={statut.value}>
                  {statut.label}
                </option>
              ))}
            </select>
          </div>

          {/* Filtre par priorité */}
          <div>
            <select
              value={selectedPriorite}
              onChange={(e) => setSelectedPriorite(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '1rem',
                outline: 'none',
                backgroundColor: 'white',
                cursor: 'pointer'
              }}
            >
              <option value="">Toutes les priorités</option>
              {priorites.map((priorite) => (
                <option key={priorite.value} value={priorite.value}>
                  {priorite.label}
                </option>
              ))}
            </select>
          </div>

          {/* Filtre par chef de projet */}
          <div>
            <select
              value={selectedChefProjet}
              onChange={(e) => setSelectedChefProjet(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '1rem',
                outline: 'none',
                backgroundColor: 'white',
                cursor: 'pointer'
              }}
            >
              <option value="">Tous les chefs de projet</option>
              {employees.map((emp) => (
                <option key={emp._id} value={emp._id}>
                  {emp.prenom} {emp.nom}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Liste des projets */}
      {filteredProjets.length === 0 ? (
        <div style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '3rem',
          textAlign: 'center',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <FaProjectDiagram style={{ fontSize: '3rem', color: '#d1d5db', marginBottom: '1rem' }} />
          <h3 style={{ color: '#6b7280', margin: '0 0 0.5rem 0' }}>
            {searchTerm || selectedStatut || selectedPriorite || selectedChefProjet ? 'Aucun projet trouvé' : 'Aucun projet'}
          </h3>
          <p style={{ color: '#9ca3af', margin: 0 }}>
            {searchTerm || selectedStatut || selectedPriorite || selectedChefProjet
              ? 'Essayez de modifier vos filtres'
              : 'Commencez par créer votre premier projet'
            }
          </p>
        </div>
      ) : (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(450px, 1fr))',
          gap: '1.5rem'
        }}>
          {filteredProjets.map((projet) => (
            <div
              key={projet._id}
              style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                padding: '1.5rem',
                boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                border: '1px solid #e5e7eb',
                transition: 'transform 0.2s, box-shadow 0.2s'
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 4px 20px rgba(0,0,0,0.15)';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
              }}
            >
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'flex-start',
                marginBottom: '1rem'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem'
                }}>
                  <div style={{
                    width: '50px',
                    height: '50px',
                    borderRadius: '12px',
                    backgroundColor: projetService.getPrioriteColor(projet.priorite),
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '1.5rem'
                  }}>
                    {projetService.getPrioriteIcon(projet.priorite)}
                  </div>
                  <div>
                    <h3 style={{
                      margin: 0,
                      fontSize: '1.25rem',
                      fontWeight: '600',
                      color: '#1f2937'
                    }}>
                      {projet.nom_projet}
                    </h3>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      marginTop: '0.25rem'
                    }}>
                      <FaUser style={{ color: '#6b7280', fontSize: '0.8rem' }} />
                      <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>
                        {projet.chef_projet?.prenom} {projet.chef_projet?.nom}
                      </span>
                    </div>
                  </div>
                </div>
                <div style={{ display: 'flex', gap: '0.5rem' }}>
                  <button
                    onClick={() => handleViewTaches(projet)}
                    style={{
                      backgroundColor: '#3b82f6',
                      color: 'white',
                      border: 'none',
                      borderRadius: '6px',
                      padding: '0.5rem',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      transition: 'background-color 0.2s'
                    }}
                    onMouseOver={(e) => e.target.style.backgroundColor = '#2563eb'}
                    onMouseOut={(e) => e.target.style.backgroundColor = '#3b82f6'}
                    title="Voir les tâches"
                  >
                    <FaTasks />
                  </button>
                  <button
                    onClick={() => handleEdit(projet)}
                    style={{
                      backgroundColor: '#f59e0b',
                      color: 'white',
                      border: 'none',
                      borderRadius: '6px',
                      padding: '0.5rem',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      transition: 'background-color 0.2s'
                    }}
                    onMouseOver={(e) => e.target.style.backgroundColor = '#d97706'}
                    onMouseOut={(e) => e.target.style.backgroundColor = '#f59e0b'}
                    title="Modifier"
                  >
                    <FaEdit />
                  </button>
                  <button
                    onClick={() => handleArchive(projet)}
                    style={{
                      backgroundColor: '#6b7280',
                      color: 'white',
                      border: 'none',
                      borderRadius: '6px',
                      padding: '0.5rem',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      transition: 'background-color 0.2s'
                    }}
                    onMouseOver={(e) => e.target.style.backgroundColor = '#4b5563'}
                    onMouseOut={(e) => e.target.style.backgroundColor = '#6b7280'}
                    title="Archiver"
                  >
                    <FaArchive />
                  </button>
                  <button
                    onClick={() => handleDelete(projet)}
                    style={{
                      backgroundColor: '#ef4444',
                      color: 'white',
                      border: 'none',
                      borderRadius: '6px',
                      padding: '0.5rem',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      transition: 'background-color 0.2s'
                    }}
                    onMouseOver={(e) => e.target.style.backgroundColor = '#dc2626'}
                    onMouseOut={(e) => e.target.style.backgroundColor = '#ef4444'}
                    title="Supprimer"
                  >
                    <FaTrash />
                  </button>
                </div>
              </div>

              {/* Description */}
              <p style={{
                margin: '0 0 1rem 0',
                color: '#6b7280',
                lineHeight: '1.5',
                fontSize: '0.875rem'
              }}>
                {projet.description}
              </p>

              {/* Informations du projet */}
              <div style={{ marginBottom: '1rem' }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  marginBottom: '0.5rem'
                }}>
                  <FaCalendarAlt style={{ color: '#6b7280', fontSize: '0.8rem' }} />
                  <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>
                    Du {projetService.formatDate(projet.date_debut)} au {projetService.formatDate(projet.date_fin_prevue)}
                  </span>
                </div>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  marginBottom: '0.5rem'
                }}>
                  <FaEuroSign style={{ color: '#6b7280', fontSize: '0.8rem' }} />
                  <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>
                    Budget: {projetService.formatBudget(projet.budget_alloue)}
                    {projet.budget_utilise > 0 && (
                      <span style={{ color: '#f59e0b' }}>
                        {' '}({projetService.getPourcentageBudgetUtilise(projet)}% utilisé)
                      </span>
                    )}
                  </span>
                </div>
                {projet.client && (
                  <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                    Client: {projet.client}
                  </div>
                )}
              </div>

              {/* Progression */}
              <div style={{ marginBottom: '1rem' }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '0.5rem'
                }}>
                  <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                    Progression
                  </span>
                  <span style={{ fontSize: '0.875rem', fontWeight: '600', color: '#1f2937' }}>
                    {projet.progressionCalculee || projet.progression || 0}%
                  </span>
                </div>
                <div style={{
                  width: '100%',
                  height: '8px',
                  backgroundColor: '#e5e7eb',
                  borderRadius: '4px',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    width: `${projet.progressionCalculee || projet.progression || 0}%`,
                    height: '100%',
                    backgroundColor: getProgressionColor(projet.progressionCalculee || projet.progression || 0),
                    transition: 'width 0.3s ease'
                  }} />
                </div>
              </div>

              {/* Statut et priorité */}
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <div style={{ display: 'flex', gap: '0.5rem' }}>
                  <span style={{
                    padding: '0.25rem 0.75rem',
                    borderRadius: '12px',
                    backgroundColor: `${projetService.getStatutColor(projet.statut)}20`,
                    color: projetService.getStatutColor(projet.statut),
                    fontSize: '0.75rem',
                    fontWeight: '500'
                  }}>
                    {projetService.getStatutIcon(projet.statut)} {projet.statut}
                  </span>
                  <span style={{
                    padding: '0.25rem 0.75rem',
                    borderRadius: '12px',
                    backgroundColor: `${projetService.getPrioriteColor(projet.priorite)}20`,
                    color: projetService.getPrioriteColor(projet.priorite),
                    fontSize: '0.75rem',
                    fontWeight: '500'
                  }}>
                    {projet.priorite}
                  </span>
                </div>

                {projetService.isProjetEnRetard(projet) && (
                  <span style={{
                    padding: '0.25rem 0.5rem',
                    borderRadius: '12px',
                    backgroundColor: '#fef3c7',
                    color: '#d97706',
                    fontSize: '0.75rem',
                    fontWeight: '500'
                  }}>
                    ⚠️ En retard
                  </span>
                )}
              </div>

              {/* Informations supplémentaires */}
              <div style={{
                marginTop: '1rem',
                paddingTop: '1rem',
                borderTop: '1px solid #e5e7eb',
                display: 'flex',
                justifyContent: 'space-between',
                fontSize: '0.75rem',
                color: '#6b7280'
              }}>
                <span>
                  {projet.taches?.length || 0} tâche{(projet.taches?.length || 0) > 1 ? 's' : ''}
                </span>
                <span>
                  {projetService.getJoursRestants(projet)} jour{projetService.getJoursRestants(projet) > 1 ? 's' : ''} restant{projetService.getJoursRestants(projet) > 1 ? 's' : ''}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Modals */}
      {showAddModal && (
        <AddProjetModal
          onClose={handleModalClose}
        />
      )}

      {showEditModal && selectedProjet && (
        <EditProjetModal
          projet={selectedProjet}
          onClose={handleModalClose}
        />
      )}

      {showDeleteModal && selectedProjet && (
        <DeleteProjetModal
          projet={selectedProjet}
          onClose={handleModalClose}
        />
      )}

      {showArchiveModal && selectedProjet && (
        <ArchiveProjetModal
          projet={selectedProjet}
          onClose={handleModalClose}
        />
      )}

      {showTachesModal && selectedProjet && (
        <ProjetTachesModal
          projet={selectedProjet}
          onClose={handleModalClose}
        />
      )}
    </div>
  );
};

export default ProjetList;
