import Formation from '../models/Formation.js';
import CandidatureFormation from '../models/CandidatureFormation.js';
import Employe from '../models/Employe.js';

// 📋 Obtenir toutes les formations
const getFormations = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      type,
      statut,
      certifiante,
      organisme,
      sortBy = 'date_debut',
      sortOrder = 'asc'
    } = req.query;

    // Construction du filtre
    const filter = {};
    if (type) filter.type = type;
    if (statut) filter.statut = statut;
    if (certifiante !== undefined) filter.certifiante = certifiante === 'true';
    if (organisme) filter.organisme = new RegExp(organisme, 'i');

    // Options de tri
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const formations = await Formation.find(filter)
      .populate('cree_par', 'email role')
      .sort(sortOptions)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .exec();

    const total = await Formation.countDocuments(filter);

    res.json({
      formations,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des formations:', error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};

// ➕ Créer une nouvelle formation
const createFormation = async (req, res) => {
  try {
    console.log('Création formation - Données reçues:', req.body);
    console.log('Utilisateur connecté:', req.utilisateur);

    const formationData = {
      ...req.body,
      cree_par: req.utilisateur._id,
      historique: [{
        action: 'creation',
        utilisateur: req.utilisateur._id,
        details: 'Formation créée',
        date: new Date()
      }]
    };

    const formation = new Formation(formationData);
    await formation.save();

    await formation.populate('cree_par', 'email role');

    res.status(201).json({
      success: true,
      message: 'Formation créée avec succès',
      formation
    });
  } catch (error) {
    console.error('Erreur lors de la création de la formation:', error);
    res.status(400).json({
      success: false,
      message: 'Erreur lors de la création',
      error: error.message
    });
  }
};

// 📝 Mettre à jour une formation
const updateFormation = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const formation = await Formation.findById(id);
    if (!formation) {
      return res.status(404).json({ message: 'Formation non trouvée' });
    }

    // Ajouter à l'historique
    formation.historique.push({
      action: 'modification',
      utilisateur: req.utilisateur._id,
      details: `Formation modifiée: ${Object.keys(updateData).join(', ')}`,
      date: new Date()
    });

    Object.assign(formation, updateData);
    await formation.save();

    await formation.populate('cree_par', 'email role');

    res.json({
      success: true,
      message: 'Formation mise à jour avec succès',
      formation
    });
  } catch (error) {
    console.error('Erreur lors de la mise à jour:', error);
    res.status(400).json({ message: 'Erreur lors de la mise à jour', error: error.message });
  }
};

// 🗑️ Supprimer une formation
const deleteFormation = async (req, res) => {
  try {
    const { id } = req.params;

    // Vérifier s'il y a des candidatures
    const candidatures = await CandidatureFormation.countDocuments({ formation_id: id });
    if (candidatures > 0) {
      return res.status(400).json({
        message: 'Impossible de supprimer une formation avec des candidatures en cours'
      });
    }

    await Formation.findByIdAndDelete(id);

    res.json({ message: 'Formation supprimée avec succès' });
  } catch (error) {
    console.error('Erreur lors de la suppression:', error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};

// 👥 Obtenir les candidatures d'une formation
const getParticipants = async (req, res) => {
  try {
    const { id } = req.params;

    const candidatures = await CandidatureFormation.find({ formation_id: id })
      .populate('employe_id', 'nom prenom email departement')
      .sort({ date_candidature: -1 });

    res.json({ candidatures });
  } catch (error) {
    console.error('Erreur lors de la récupération des candidatures:', error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};

// 📊 Statistiques des formations
const getStatistiques = async (req, res) => {
  try {
    const { annee = new Date().getFullYear() } = req.query;

    const startDate = new Date(annee, 0, 1);
    const endDate = new Date(annee, 11, 31);

    // Statistiques générales
    const totalFormations = await Formation.countDocuments({
      date_debut: { $gte: startDate, $lte: endDate }
    });

    const formationsParStatut = await Formation.aggregate([
      { $match: { date_debut: { $gte: startDate, $lte: endDate } } },
      { $group: { _id: '$statut', count: { $sum: 1 } } }
    ]);

    const formationsParType = await Formation.aggregate([
      { $match: { date_debut: { $gte: startDate, $lte: endDate } } },
      { $group: { _id: '$type', count: { $sum: 1 } } }
    ]);

    // Statistiques des candidatures
    const candidaturesParStatut = await CandidatureFormation.aggregate([
      { $group: {
        _id: '$statut',
        count: { $sum: 1 }
      }}
    ]);

    res.json({
      totalFormations,
      formationsParStatut,
      formationsParType,
      candidaturesParStatut
    });
  } catch (error) {
    console.error('Erreur lors du calcul des statistiques:', error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};

export default {
  getFormations,
  createFormation,
  updateFormation,
  deleteFormation,
  getParticipants,
  getStatistiques
};
