import jwt from 'jsonwebtoken';
import Utilisateur from '../models/utilisateur.js';

// Middleware pour vérifier si l'utilisateur est un administrateur
const adminMiddleware = async (req, res, next) => {
    try {
        const token = req.header('Authorization')?.replace('Bearer ', '');

        if (!token) {
            return res.status(401).json({
                success: false,
                message: "Accès refusé. Token manquant."
            });
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const utilisateur = await Utilisateur.findById(decoded._id);

        if (!utilisateur) {
            return res.status(401).json({
                success: false,
                message: "Utilisateur non trouvé."
            });
        }

        if (utilisateur.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: "Accès refusé. Privilèges administrateur requis."
            });
        }

        if (!utilisateur.actif) {
            return res.status(403).json({
                success: false,
                message: "Compte désactivé."
            });
        }

        req.utilisateur = utilisateur;
        next();
    } catch (error) {
        console.error('Erreur dans adminMiddleware:', error);
        return res.status(401).json({
            success: false,
            message: "Token invalide."
        });
    }
};

// Middleware pour vérifier des permissions spécifiques
const checkPermission = (requiredPermission) => {
    return async (req, res, next) => {
        try {
            if (!req.utilisateur) {
                return res.status(401).json({
                    success: false,
                    message: "Utilisateur non authentifié."
                });
            }

            // Les admins et RH ont toutes les permissions
            if (req.utilisateur.role === 'admin' || req.utilisateur.role === 'rh') {
                return next();
            }

            // Vérifier si l'utilisateur a la permission requise
            if (!req.utilisateur.permissions || !req.utilisateur.permissions.includes(requiredPermission)) {
                return res.status(403).json({
                    success: false,
                    message: `Permission refusée. Permission requise: ${requiredPermission}`
                });
            }

            next();
        } catch (error) {
            console.error('Erreur dans checkPermission:', error);
            return res.status(500).json({
                success: false,
                message: "Erreur serveur lors de la vérification des permissions."
            });
        }
    };
};

// Middleware pour vérifier si l'utilisateur est admin ou RH
const adminOrRHMiddleware = async (req, res, next) => {
    try {
        const token = req.header('Authorization')?.replace('Bearer ', '');

        if (!token) {
            return res.status(401).json({
                success: false,
                message: "Accès refusé. Token manquant."
            });
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const utilisateur = await Utilisateur.findById(decoded._id);

        if (!utilisateur) {
            return res.status(401).json({
                success: false,
                message: "Utilisateur non trouvé."
            });
        }

        if (utilisateur.role !== 'admin' && utilisateur.role !== 'rh') {
            return res.status(403).json({
                success: false,
                message: "Accès refusé. Privilèges administrateur ou RH requis."
            });
        }

        if (!utilisateur.actif) {
            return res.status(403).json({
                success: false,
                message: "Compte désactivé."
            });
        }

        req.utilisateur = utilisateur;
        req.user = utilisateur; // Alias pour compatibilité
        next();
    } catch (error) {
        console.error('Erreur dans adminOrRHMiddleware:', error);
        return res.status(401).json({
            success: false,
            message: "Token invalide."
        });
    }
};

export { adminMiddleware, checkPermission, adminOrRHMiddleware };
