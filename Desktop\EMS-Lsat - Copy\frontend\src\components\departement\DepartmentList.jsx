
import React, { useState } from 'react';
import { FaPlus, FaEdit, FaTrash, FaBuilding, FaUsers, FaSearch, FaEye, FaEuroSign, FaUser } from 'react-icons/fa';
import { useDepartments } from '../../hooks/useDepartments';
import AddDepartmentModal from './AddDepartmentModal';
import EditDepartmentModal from './EditDepartmentModal';
import DeleteDepartmentModal from './DeleteDepartmentModal';

const DepartmentList = () => {
  const { departments, loading, error, fetchDepartments } = useDepartments();
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Filtrer les départements selon le terme de recherche
  const filteredDepartments = departments.filter(dept =>
    dept.nom_departement?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    dept.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleEdit = (department) => {
    setSelectedDepartment(department);
    setShowEditModal(true);
  };

  const handleDelete = (department) => {
    setSelectedDepartment(department);
    setShowDeleteModal(true);
  };

  const handleModalClose = () => {
    setShowAddModal(false);
    setShowEditModal(false);
    setShowDeleteModal(false);
    setSelectedDepartment(null);
    fetchDepartments(); // Recharger la liste
  };

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px',
        fontSize: '1.2rem',
        color: '#6b7280'
      }}>
        Chargement des départements...
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        backgroundColor: '#fef2f2',
        border: '1px solid #fecaca',
        borderRadius: '8px',
        padding: '1rem',
        color: '#dc2626'
      }}>
        <strong>Erreur:</strong> {error}
      </div>
    );
  }

  return (
    <div style={{ padding: '0' }}>
      {/* En-tête avec recherche et bouton */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '1.5rem',
        marginBottom: '1.5rem',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '1rem'
        }}>
          <div>
            <h2 style={{
              fontSize: '1.5rem',
              fontWeight: '600',
              color: '#1f2937',
              margin: 0
            }}>
              Gestion des départements
            </h2>
            <p style={{
              color: '#6b7280',
              margin: '0.25rem 0 0 0',
              fontSize: '0.875rem'
            }}>
              Gérez les départements et services de votre organisation
            </p>
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            style={{
              backgroundColor: '#6366f1',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '0.75rem 1.25rem',
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              transition: 'background-color 0.2s'
            }}
            onMouseOver={(e) => e.target.style.backgroundColor = '#4f46e5'}
            onMouseOut={(e) => e.target.style.backgroundColor = '#6366f1'}
          >
            <FaPlus />
            Nouveau département
          </button>
        </div>

        {/* Barre de recherche et filtres */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          gap: '1rem'
        }}>
          <div style={{ position: 'relative', flex: 1, maxWidth: '400px' }}>
            <FaSearch style={{
              position: 'absolute',
              left: '12px',
              top: '50%',
              transform: 'translateY(-50%)',
              color: '#9ca3af',
              fontSize: '0.875rem'
            }} />
            <input
              type="text"
              placeholder="Rechercher un département"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem 0.75rem 0.75rem 2.5rem',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '0.875rem',
                outline: 'none',
                transition: 'border-color 0.2s',
                boxSizing: 'border-box'
              }}
              onFocus={(e) => e.target.style.borderColor = '#6366f1'}
              onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
            />
          </div>

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            <label style={{
              fontSize: '0.875rem',
              color: '#6b7280',
              fontWeight: '500'
            }}>
              Statut:
            </label>
            <select
              style={{
                padding: '0.5rem 0.75rem',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                fontSize: '0.875rem',
                outline: 'none',
                backgroundColor: 'white',
                cursor: 'pointer'
              }}
            >
              <option value="">Tous</option>
              <option value="actif">Actif</option>
              <option value="inactif">Inactif</option>
            </select>
          </div>
        </div>
      </div>

      {/* Tableau des départements */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        overflow: 'hidden'
      }}>
        {filteredDepartments.length === 0 ? (
          <div style={{
            padding: '3rem',
            textAlign: 'center'
          }}>
            <FaBuilding style={{ fontSize: '3rem', color: '#d1d5db', marginBottom: '1rem' }} />
            <h3 style={{ color: '#6b7280', margin: '0 0 0.5rem 0' }}>
              {searchTerm ? 'Aucun département trouvé' : 'Aucun département'}
            </h3>
            <p style={{ color: '#9ca3af', margin: 0 }}>
              {searchTerm
                ? 'Essayez de modifier votre recherche'
                : 'Commencez par ajouter votre premier département'
              }
            </p>
          </div>
        ) : (
          <div style={{ overflowX: 'auto' }}>
            <table style={{
              width: '100%',
              borderCollapse: 'collapse'
            }}>
              <thead>
                <tr style={{ backgroundColor: '#f8fafc' }}>
                  <th style={{
                    padding: '1rem',
                    textAlign: 'left',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    color: '#6b7280',
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em',
                    borderBottom: '1px solid #e5e7eb'
                  }}>
                    DÉPARTEMENT
                  </th>
                  <th style={{
                    padding: '1rem',
                    textAlign: 'left',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    color: '#6b7280',
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em',
                    borderBottom: '1px solid #e5e7eb'
                  }}>
                    RESPONSABLE
                  </th>
                  <th style={{
                    padding: '1rem',
                    textAlign: 'left',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    color: '#6b7280',
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em',
                    borderBottom: '1px solid #e5e7eb'
                  }}>
                    DESCRIPTION
                  </th>
                  <th style={{
                    padding: '1rem',
                    textAlign: 'left',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    color: '#6b7280',
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em',
                    borderBottom: '1px solid #e5e7eb'
                  }}>
                    STATUT
                  </th>
                  <th style={{
                    padding: '1rem',
                    textAlign: 'center',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    color: '#6b7280',
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em',
                    borderBottom: '1px solid #e5e7eb'
                  }}>
                    ACTIONS
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredDepartments.map((department, index) => (
                  <tr
                    key={department._id}
                    style={{
                      borderBottom: index < filteredDepartments.length - 1 ? '1px solid #f3f4f6' : 'none',
                      transition: 'background-color 0.2s'
                    }}
                    onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
                    onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                  >
                    <td style={{ padding: '1rem' }}>
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.75rem'
                      }}>
                        <div style={{
                          width: '40px',
                          height: '40px',
                          borderRadius: '8px',
                          backgroundColor: '#3b82f6',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: 'white',
                          fontSize: '1rem'
                        }}>
                          <FaBuilding />
                        </div>
                        <div>
                          <div style={{
                            fontWeight: '600',
                            color: '#1f2937',
                            fontSize: '0.875rem'
                          }}>
                            {department.nom_departement}
                          </div>
                          <div style={{
                            color: '#6b7280',
                            fontSize: '0.75rem',
                            marginTop: '0.125rem'
                          }}>
                            Créé le {new Date(department.createdAt).toLocaleDateString('fr-FR')}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td style={{ padding: '1rem' }}>
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem'
                      }}>
                        <FaUser style={{ color: '#6b7280', fontSize: '0.875rem' }} />
                        <div>
                          {department.responsable ? (
                            <>
                              <div style={{
                                fontWeight: '500',
                                color: '#1f2937',
                                fontSize: '0.875rem'
                              }}>
                                {department.responsable.prenom} {department.responsable.nom}
                              </div>
                              <div style={{
                                color: '#6b7280',
                                fontSize: '0.75rem'
                              }}>
                                {department.responsable.email}
                              </div>
                            </>
                          ) : (
                            <span style={{
                              fontStyle: 'italic',
                              color: '#9ca3af',
                              fontSize: '0.875rem'
                            }}>
                              Non assigné
                            </span>
                          )}
                        </div>
                      </div>
                    </td>
                    <td style={{ padding: '1rem' }}>
                      <div style={{
                        color: '#6b7280',
                        fontSize: '0.875rem',
                        lineHeight: '1.5'
                      }}>
                        {department.description || (
                          <span style={{ fontStyle: 'italic', color: '#9ca3af' }}>
                            Aucune description
                          </span>
                        )}
                      </div>
                    </td>
                    <td style={{ padding: '1rem' }}>
                      <span style={{
                        display: 'inline-flex',
                        alignItems: 'center',
                        padding: '0.25rem 0.75rem',
                        borderRadius: '9999px',
                        fontSize: '0.75rem',
                        fontWeight: '500',
                        backgroundColor: department.is_active ? '#dcfce7' : '#fee2e2',
                        color: department.is_active ? '#166534' : '#dc2626'
                      }}>
                        {department.is_active ? 'Actif' : 'Inactif'}
                      </span>
                    </td>
                    <td style={{ padding: '1rem', textAlign: 'center' }}>
                      <div style={{
                        display: 'flex',
                        justifyContent: 'center',
                        gap: '0.5rem'
                      }}>
                        <button
                          onClick={() => handleEdit(department)}
                          style={{
                            backgroundColor: 'transparent',
                            color: '#6b7280',
                            border: 'none',
                            borderRadius: '6px',
                            padding: '0.5rem',
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            transition: 'all 0.2s'
                          }}
                          onMouseOver={(e) => {
                            e.target.style.backgroundColor = '#f3f4f6';
                            e.target.style.color = '#3b82f6';
                          }}
                          onMouseOut={(e) => {
                            e.target.style.backgroundColor = 'transparent';
                            e.target.style.color = '#6b7280';
                          }}
                          title="Voir les détails"
                        >
                          <FaEye />
                        </button>
                        <button
                          onClick={() => handleEdit(department)}
                          style={{
                            backgroundColor: 'transparent',
                            color: '#6b7280',
                            border: 'none',
                            borderRadius: '6px',
                            padding: '0.5rem',
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            transition: 'all 0.2s'
                          }}
                          onMouseOver={(e) => {
                            e.target.style.backgroundColor = '#fef3c7';
                            e.target.style.color = '#d97706';
                          }}
                          onMouseOut={(e) => {
                            e.target.style.backgroundColor = 'transparent';
                            e.target.style.color = '#6b7280';
                          }}
                          title="Modifier"
                        >
                          <FaEdit />
                        </button>
                        <button
                          onClick={() => handleDelete(department)}
                          style={{
                            backgroundColor: 'transparent',
                            color: '#6b7280',
                            border: 'none',
                            borderRadius: '6px',
                            padding: '0.5rem',
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            transition: 'all 0.2s'
                          }}
                          onMouseOver={(e) => {
                            e.target.style.backgroundColor = '#fee2e2';
                            e.target.style.color = '#dc2626';
                          }}
                          onMouseOut={(e) => {
                            e.target.style.backgroundColor = 'transparent';
                            e.target.style.color = '#6b7280';
                          }}
                          title="Supprimer"
                        >
                          <FaTrash />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {filteredDepartments.length > 0 && (
          <div style={{
            padding: '1rem 1.5rem',
            borderTop: '1px solid #f3f4f6',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: '#f8fafc'
          }}>
            <div style={{
              fontSize: '0.875rem',
              color: '#6b7280'
            }}>
              Affichage de 1 à {filteredDepartments.length} sur {filteredDepartments.length} départements
            </div>
            <div style={{
              display: 'flex',
              gap: '0.25rem'
            }}>
              <button style={{
                padding: '0.5rem 0.75rem',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                backgroundColor: 'white',
                color: '#6b7280',
                fontSize: '0.875rem',
                cursor: 'pointer'
              }}>
                ‹
              </button>
              <button style={{
                padding: '0.5rem 0.75rem',
                border: '1px solid #6366f1',
                borderRadius: '6px',
                backgroundColor: '#6366f1',
                color: 'white',
                fontSize: '0.875rem',
                cursor: 'pointer'
              }}>
                1
              </button>
              <button style={{
                padding: '0.5rem 0.75rem',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                backgroundColor: 'white',
                color: '#6b7280',
                fontSize: '0.875rem',
                cursor: 'pointer'
              }}>
                2
              </button>
              <button style={{
                padding: '0.5rem 0.75rem',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                backgroundColor: 'white',
                color: '#6b7280',
                fontSize: '0.875rem',
                cursor: 'pointer'
              }}>
                ...
              </button>
              <button style={{
                padding: '0.5rem 0.75rem',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                backgroundColor: 'white',
                color: '#6b7280',
                fontSize: '0.875rem',
                cursor: 'pointer'
              }}>
                8
              </button>
              <button style={{
                padding: '0.5rem 0.75rem',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                backgroundColor: 'white',
                color: '#6b7280',
                fontSize: '0.875rem',
                cursor: 'pointer'
              }}>
                ›
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      {showAddModal && (
        <AddDepartmentModal
          onClose={handleModalClose}
        />
      )}

      {showEditModal && selectedDepartment && (
        <EditDepartmentModal
          department={selectedDepartment}
          onClose={handleModalClose}
        />
      )}

      {showDeleteModal && selectedDepartment && (
        <DeleteDepartmentModal
          department={selectedDepartment}
          onClose={handleModalClose}
        />
      )}
    </div>
  );
};

export default DepartmentList;