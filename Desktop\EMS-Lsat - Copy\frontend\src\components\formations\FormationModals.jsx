import React, { useState, useEffect } from 'react';

// Modal pour ajouter/modifier une formation
export const FormationModal = ({ isOpen, onClose, onSave, formation }) => {
  const [formData, setFormData] = useState({
    nom_formation: '',
    organisme: '',
    date_debut: '',
    date_fin: '',
    description: '',
    type: 'externe',
    certifiante: false,
    modalite: 'presentielle',
    lieu: '',
    lien_connexion: '',
    capacite_max: 20
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (formation) {
      setFormData({
        nom_formation: formation.nom_formation || '',
        organisme: formation.organisme || '',
        date_debut: formation.date_debut ? new Date(formation.date_debut).toISOString().split('T')[0] : '',
        date_fin: formation.date_fin ? new Date(formation.date_fin).toISOString().split('T')[0] : '',
        description: formation.description || '',
        type: formation.type || 'externe',
        certifiante: formation.certifiante || false,
        modalite: formation.modalite || 'presentielle',
        lieu: formation.lieu || '',
        lien_connexion: formation.lien_connexion || '',
        capacite_max: formation.capacite_max || 20
      });
    } else {
      // Reset form for new formation
      setFormData({
        nom_formation: '',
        organisme: '',
        date_debut: '',
        date_fin: '',
        description: '',
        type: 'externe',
        certifiante: false,
        modalite: 'presentielle',
        lieu: '',
        lien_connexion: '',
        capacite_max: 20
      });
    }
  }, [formation, isOpen]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const url = formation
        ? `http://localhost:4000/api/formations/${formation._id}`
        : 'http://localhost:4000/api/formations';

      const method = formation ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        onSave();
        onClose();
      } else {
        const data = await response.json();
        setError(data.message || 'Erreur lors de la sauvegarde');
      }
    } catch (error) {
      setError('Erreur de connexion');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '2rem',
        width: '90%',
        maxWidth: '600px',
        maxHeight: '90vh',
        overflowY: 'auto'
      }}>
        <h2 style={{ margin: '0 0 1.5rem 0', fontSize: '1.5rem', fontWeight: '700' }}>
          {formation ? 'Modifier la formation' : 'Nouvelle formation'}
        </h2>

        {error && (
          <div style={{
            backgroundColor: '#fef2f2',
            color: '#dc2626',
            padding: '1rem',
            borderRadius: '8px',
            marginBottom: '1rem'
          }}>
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div style={{ display: 'grid', gap: '1rem' }}>
            {/* Nom de la formation */}
            <div>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
                Nom de la formation *
              </label>
              <input
                type="text"
                required
                value={formData.nom_formation}
                onChange={(e) => setFormData({...formData, nom_formation: e.target.value})}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px'
                }}
              />
            </div>

            {/* Organisme */}
            <div>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
                Organisme *
              </label>
              <input
                type="text"
                required
                value={formData.organisme}
                onChange={(e) => setFormData({...formData, organisme: e.target.value})}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px'
                }}
              />
            </div>

            {/* Dates */}
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
              <div>
                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
                  Date de début *
                </label>
                <input
                  type="date"
                  required
                  value={formData.date_debut}
                  onChange={(e) => setFormData({...formData, date_debut: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px'
                  }}
                />
              </div>
              <div>
                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
                  Date de fin *
                </label>
                <input
                  type="date"
                  required
                  value={formData.date_fin}
                  onChange={(e) => setFormData({...formData, date_fin: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px'
                  }}
                />
              </div>
            </div>

            {/* Description */}
            <div>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
                Description *
              </label>
              <textarea
                required
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  resize: 'vertical'
                }}
              />
            </div>

            {/* Type et options */}
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '1rem' }}>
              <div>
                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
                  Type
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData({...formData, type: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px'
                  }}
                >
                  <option value="externe">Externe</option>
                  <option value="interne">Interne</option>
                </select>
              </div>
              <div>
                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
                  Modalité
                </label>
                <select
                  value={formData.modalite}
                  onChange={(e) => setFormData({...formData, modalite: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px'
                  }}
                >
                  <option value="presentielle">Présentielle</option>
                  <option value="en_ligne">En ligne</option>
                  <option value="mixte">Mixte</option>
                </select>
              </div>
              <div>
                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
                  Capacité max
                </label>
                <input
                  type="number"
                  min="1"
                  value={formData.capacite_max}
                  onChange={(e) => setFormData({...formData, capacite_max: parseInt(e.target.value)})}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px'
                  }}
                />
              </div>
            </div>

            {/* Lieu ou lien */}
            {(formData.modalite === 'presentielle' || formData.modalite === 'mixte') && (
              <div>
                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
                  Lieu
                </label>
                <input
                  type="text"
                  value={formData.lieu}
                  onChange={(e) => setFormData({...formData, lieu: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px'
                  }}
                />
              </div>
            )}

            {(formData.modalite === 'en_ligne' || formData.modalite === 'mixte') && (
              <div>
                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
                  Lien de connexion
                </label>
                <input
                  type="url"
                  value={formData.lien_connexion}
                  onChange={(e) => setFormData({...formData, lien_connexion: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px'
                  }}
                />
              </div>
            )}

            {/* Certifiante */}
            <div>
              <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', cursor: 'pointer' }}>
                <input
                  type="checkbox"
                  checked={formData.certifiante}
                  onChange={(e) => setFormData({...formData, certifiante: e.target.checked})}
                />
                <span style={{ fontWeight: '600' }}>Formation certifiante</span>
              </label>
            </div>
          </div>

          {/* Actions */}
          <div style={{ display: 'flex', gap: '1rem', marginTop: '2rem', justifyContent: 'flex-end' }}>
            <button
              type="button"
              onClick={onClose}
              style={{
                padding: '0.75rem 1.5rem',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                backgroundColor: 'white',
                cursor: 'pointer'
              }}
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={loading}
              style={{
                padding: '0.75rem 1.5rem',
                border: 'none',
                borderRadius: '8px',
                backgroundColor: '#3b82f6',
                color: 'white',
                cursor: loading ? 'not-allowed' : 'pointer',
                opacity: loading ? 0.7 : 1
              }}
            >
              {loading ? 'Sauvegarde...' : (formation ? 'Modifier' : 'Créer')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
