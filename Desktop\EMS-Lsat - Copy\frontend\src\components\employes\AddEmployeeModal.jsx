import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { FaTimes, FaUser, FaUpload, FaBriefcase, FaUniversity, FaIdCard } from 'react-icons/fa';

const AddEmployeeModal = ({ onClose, onSuccess }) => {
  const [formData, setFormData] = useState({
    // 🧍 Informations personnelles
    prenom: '',
    nom: '',
    email: '',
    password: '',
    sexe: '',
    cin: '',
    cnss: '',
    statut_matrimonial: '',
    telephone: '',
    mobile: '',
    adresse: '',
    ville: '',
    pays: 'Maroc',
    date_naissance: '',

    // 💼 Informations professionnelles
    departement: '',
    service: '',
    fonction: '',
    type_contrat: '',
    date_entree: '',
    salaire_base: '',
    cout_heures_supplementaires: '0',
    superviseur: '',
    conges_consommes: '0',

    // 🏦 Informations bancaires
    nom_banque: '',
    rib: '',
    adresse_banque: '',
    ville_banque: ''
  });
  const [photo, setPhoto] = useState(null);
  const [photoPreview, setPhotoPreview] = useState(null);
  const [departments, setDepartments] = useState([]);
  const [services, setServices] = useState([]);
  const [superviseurs, setSuperviseurs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [currentStep, setCurrentStep] = useState(1);
  const [validationErrors, setValidationErrors] = useState({});

  // Charger les départements
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const token = localStorage.getItem('token');

        // Charger les départements
        const deptResponse = await axios.get('http://localhost:4000/api/departments', {
          headers: { Authorization: `Bearer ${token}` }
        });
        if (deptResponse.data.success) {
          setDepartments(deptResponse.data.data);
        }
      } catch (error) {
        console.error('Erreur lors du chargement des données initiales:', error);
        setError('Erreur lors du chargement des données');
      }
    };
    fetchInitialData();
  }, []);

  // Charger les services quand le département change
  useEffect(() => {
    const fetchServices = async () => {
      if (!formData.departement) {
        setServices([]);
        return;
      }

      try {
        const token = localStorage.getItem('token');
        const response = await axios.get(`http://localhost:4000/api/services?departement=${formData.departement}`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        if (response.data.success) {
          setServices(response.data.data);
        }
      } catch (error) {
        console.error('Erreur lors du chargement des services:', error);
      }
    };

    fetchServices();
  }, [formData.departement]);

  // Charger les superviseurs potentiels quand le département change
  useEffect(() => {
    const fetchSuperviseurs = async () => {
      if (!formData.departement) {
        setSuperviseurs([]);
        return;
      }

      try {
        const token = localStorage.getItem('token');
        const response = await axios.get(`http://localhost:4000/api/employees/superviseurs/${formData.departement}`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        if (response.data.success) {
          setSuperviseurs(response.data.data);
        }
      } catch (error) {
        console.error('Erreur lors du chargement des superviseurs:', error);
      }
    };

    fetchSuperviseurs();
  }, [formData.departement]);

  // Gérer les changements de formulaire
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Effacer l'erreur de validation pour ce champ
    if (validationErrors[name]) {
      setValidationErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Validation des champs
  const validateStep = (step) => {
    const errors = {};

    if (step === 1) {
      // Validation des informations personnelles
      if (!formData.prenom.trim()) errors.prenom = 'Le prénom est requis';
      if (!formData.nom.trim()) errors.nom = 'Le nom est requis';
      if (!formData.email.trim()) errors.email = 'L\'email est requis';
      else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) errors.email = 'Format d\'email invalide';
      if (!formData.password.trim()) errors.password = 'Le mot de passe est requis';
      else if (formData.password.length < 6) errors.password = 'Le mot de passe doit contenir au moins 6 caractères';
      if (!formData.sexe) errors.sexe = 'Le sexe est requis';
      if (!formData.cin.trim()) errors.cin = 'Le CIN est requis';
      if (!formData.statut_matrimonial) errors.statut_matrimonial = 'Le statut matrimonial est requis';
      if (!formData.mobile.trim()) errors.mobile = 'Le numéro mobile est requis';
      if (!formData.adresse.trim()) errors.adresse = 'L\'adresse est requise';
      if (!formData.ville.trim()) errors.ville = 'La ville est requise';
      if (!formData.date_naissance) errors.date_naissance = 'La date de naissance est requise';
    }

    if (step === 2) {
      // Validation des informations professionnelles
      if (!formData.departement) errors.departement = 'Le département est requis';
      if (!formData.fonction.trim()) errors.fonction = 'La fonction est requise';
      if (!formData.type_contrat) errors.type_contrat = 'Le type de contrat est requis';
      if (!formData.date_entree) errors.date_entree = 'La date d\'entrée est requise';
      if (!formData.salaire_base || parseFloat(formData.salaire_base) <= 0) {
        errors.salaire_base = 'Le salaire de base est requis et doit être supérieur à 0';
      }
    }

    // Validation du RIB si fourni
    if (formData.rib && !/^\d{24}$/.test(formData.rib.replace(/\s/g, ''))) {
      errors.rib = 'Le RIB doit contenir 24 chiffres';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 3));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  // Gérer l'upload de photo
  const handlePhotoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setPhoto(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setPhotoPreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Soumettre le formulaire
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Valider toutes les étapes
    if (!validateStep(1) || !validateStep(2)) {
      setError('Veuillez corriger les erreurs dans le formulaire');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const token = localStorage.getItem('token');
      const submitData = new FormData();

      // Ajouter tous les champs du formulaire
      Object.keys(formData).forEach(key => {
        if (formData[key] !== '' && formData[key] !== null && formData[key] !== undefined) {
          submitData.append(key, formData[key]);
        }
      });

      // Ajouter la photo si elle existe
      if (photo) {
        submitData.append('photo', photo);
      }

      console.log('Données envoyées:', Object.fromEntries(submitData));

      const response = await axios.post('http://localhost:4000/api/employees', submitData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data'
        }
      });

      if (response.data.success) {
        onSuccess();
        onClose();
      }
    } catch (error) {
      console.error('Erreur lors de la création:', error);
      const errorMessage = error.response?.data?.message || 'Erreur lors de la création de l\'employé';
      setError(errorMessage);

      // Si c'est une erreur de validation du serveur, afficher les détails
      if (error.response?.status === 400) {
        setValidationErrors({ general: errorMessage });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '1rem'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        width: '100%',
        maxWidth: '600px',
        maxHeight: '90vh',
        overflow: 'auto',
        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)'
      }}>
        {/* Header */}
        <div style={{
          padding: '1.5rem',
          borderBottom: '1px solid #e5e7eb'
        }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '1rem'
          }}>
            <h2 style={{ margin: 0, fontSize: '1.5rem', fontWeight: '600' }}>
              Ajouter un employé
            </h2>
            <button
              onClick={onClose}
              style={{
                background: 'none',
                border: 'none',
                fontSize: '1.5rem',
                cursor: 'pointer',
                color: '#6b7280',
                padding: '0.5rem'
              }}
            >
              <FaTimes />
            </button>
          </div>

          {/* Indicateur d'étapes */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '1rem'
          }}>
            {[
              { step: 1, icon: FaIdCard, label: 'Informations personnelles' },
              { step: 2, icon: FaBriefcase, label: 'Informations professionnelles' },
              { step: 3, icon: FaUniversity, label: 'Informations bancaires' }
            ].map(({ step, icon: Icon, label }, index) => (
              <div key={step} style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  padding: '0.5rem 1rem',
                  borderRadius: '8px',
                  backgroundColor: currentStep === step ? '#3b82f6' : currentStep > step ? '#10b981' : '#f3f4f6',
                  color: currentStep >= step ? 'white' : '#6b7280',
                  fontSize: '0.875rem',
                  fontWeight: '500'
                }}>
                  <Icon />
                  <span>{label}</span>
                </div>
                {index < 2 && (
                  <div style={{
                    flex: 1,
                    height: '2px',
                    backgroundColor: currentStep > step + 1 ? '#10b981' : '#e5e7eb',
                    margin: '0 0.5rem'
                  }} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Formulaire */}
        <form onSubmit={handleSubmit} style={{ padding: '1.5rem' }}>
          {/* Message d'erreur */}
          {error && (
            <div style={{
              backgroundColor: '#fef2f2',
              color: '#dc2626',
              padding: '1rem',
              borderRadius: '8px',
              marginBottom: '1rem',
              border: '1px solid #fecaca'
            }}>
              {error}
            </div>
          )}

          {/* Upload de photo */}
          <div style={{ marginBottom: '1.5rem', textAlign: 'center' }}>
            <div style={{
              width: '120px',
              height: '120px',
              borderRadius: '50%',
              backgroundColor: '#f3f4f6',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 1rem',
              overflow: 'hidden',
              border: '2px dashed #d1d5db'
            }}>
              {photoPreview ? (
                <img
                  src={photoPreview}
                  alt="Aperçu"
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover'
                  }}
                />
              ) : (
                <FaUser style={{ fontSize: '2rem', color: '#9ca3af' }} />
              )}
            </div>
            <label style={{
              display: 'inline-flex',
              alignItems: 'center',
              gap: '0.5rem',
              padding: '0.5rem 1rem',
              backgroundColor: '#f3f4f6',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '0.9rem',
              color: '#374151'
            }}>
              <FaUpload />
              Choisir une photo
              <input
                type="file"
                accept="image/*"
                onChange={handlePhotoChange}
                style={{ display: 'none' }}
              />
            </label>
          </div>

          {/* Grille de formulaire */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '1rem',
            marginBottom: '1.5rem'
          }}>
            {/* Nom */}
            <div>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500' }}>
                Nom *
              </label>
              <input
                type="text"
                name="nom"
                value={formData.nom}
                onChange={handleChange}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '1rem'
                }}
              />
            </div>

            {/* Prénom */}
            <div>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500' }}>
                Prénom *
              </label>
              <input
                type="text"
                name="prenom"
                value={formData.prenom}
                onChange={handleChange}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '1rem'
                }}
              />
            </div>

            {/* CIN */}
            <div>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500' }}>
                CIN
              </label>
              <input
                type="text"
                name="cin"
                value={formData.cin}
                onChange={handleChange}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '1rem'
                }}
              />
            </div>

            {/* Date de naissance */}
            <div>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500' }}>
                Date de naissance
              </label>
              <input
                type="date"
                name="date_naissance"
                value={formData.date_naissance}
                onChange={handleChange}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '1rem'
                }}
              />
            </div>

            {/* Poste */}
            <div>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500' }}>
                Poste
              </label>
              <input
                type="text"
                name="poste"
                value={formData.poste}
                onChange={handleChange}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '1rem'
                }}
              />
            </div>

            {/* Téléphone */}
            <div>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500' }}>
                Téléphone
              </label>
              <input
                type="tel"
                name="telephone"
                value={formData.telephone}
                onChange={handleChange}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '1rem'
                }}
              />
            </div>

            {/* Département */}
            <div>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500' }}>
                Département
              </label>
              <select
                name="departement"
                value={formData.departement}
                onChange={handleChange}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '1rem'
                }}
              >
                <option value="">Sélectionner un département</option>
                {departments.map(dept => (
                  <option key={dept._id} value={dept._id}>
                    {dept.nom_departement}
                  </option>
                ))}
              </select>
            </div>

            {/* Date d'embauche */}
            <div>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500' }}>
                Date d'embauche
              </label>
              <input
                type="date"
                name="date_embauche"
                value={formData.date_embauche}
                onChange={handleChange}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '1rem'
                }}
              />
            </div>
          </div>

          {/* Adresse */}
          <div style={{ marginBottom: '1.5rem' }}>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500' }}>
              Adresse
            </label>
            <textarea
              name="adresse"
              value={formData.adresse}
              onChange={handleChange}
              rows="3"
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                fontSize: '1rem',
                resize: 'vertical'
              }}
            />
          </div>

          {/* Informations de connexion */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '1rem',
            marginBottom: '1.5rem'
          }}>
            {/* Email */}
            <div>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500' }}>
                Email *
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '1rem'
                }}
              />
            </div>

            {/* Mot de passe */}
            <div>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500' }}>
                Mot de passe *
              </label>
              <input
                type="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '1rem'
                }}
              />
            </div>
          </div>

          {/* Boutons */}
          <div style={{
            display: 'flex',
            gap: '1rem',
            justifyContent: 'flex-end'
          }}>
            <button
              type="button"
              onClick={onClose}
              style={{
                padding: '0.75rem 1.5rem',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                backgroundColor: 'white',
                color: '#374151',
                cursor: 'pointer',
                fontSize: '1rem'
              }}
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={loading}
              style={{
                padding: '0.75rem 1.5rem',
                border: 'none',
                borderRadius: '6px',
                backgroundColor: loading ? '#9ca3af' : '#3b82f6',
                color: 'white',
                cursor: loading ? 'not-allowed' : 'pointer',
                fontSize: '1rem',
                fontWeight: '600'
              }}
            >
              {loading ? 'Création...' : 'Créer l\'employé'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddEmployeeModal;
