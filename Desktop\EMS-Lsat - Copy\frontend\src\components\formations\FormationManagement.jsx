import React, { useState, useEffect } from 'react';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaUsers,
  FaCalendarAlt,
  FaFilter,
  FaSearch,
  FaEye,
  FaDownload,
  FaChartBar
} from 'react-icons/fa';
import { FormationModal } from './FormationModals';
import CandidaturesViewModal from './CandidaturesViewModal';

const FormationManagement = () => {
  const [formations, setFormations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showParticipantsModal, setShowParticipantsModal] = useState(false);
  const [showCandidaturesModal, setShowCandidaturesModal] = useState(false);
  const [selectedFormation, setSelectedFormation] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    type: '',
    statut: '',
    certifiante: '',
    organisme: ''
  });

  // Charger les formations
  useEffect(() => {
    fetchFormations();
  }, [filters, searchTerm]);

  const fetchFormations = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams({
        ...filters,
        search: searchTerm
      }).toString();

      const response = await fetch(`http://localhost:4000/api/formations?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('📋 Données reçues:', data);
        setFormations(data.formations || []);
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Erreur lors du chargement des formations');
      }
    } catch (error) {
      console.error('Erreur:', error);
      setError('Erreur de connexion');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteFormation = async (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette formation ?')) {
      try {
        const response = await fetch(`http://localhost:4000/api/formations/${id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (response.ok) {
          fetchFormations();
        } else {
          setError('Erreur lors de la suppression');
        }
      } catch (error) {
        setError('Erreur de connexion');
      }
    }
  };

  const getStatusColor = (statut) => {
    switch (statut) {
      case 'planifiee': return '#3b82f6';
      case 'en_cours': return '#f59e0b';
      case 'terminee': return '#10b981';
      case 'annulee': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getStatusText = (statut) => {
    switch (statut) {
      case 'planifiee': return 'Planifiée';
      case 'en_cours': return 'En cours';
      case 'terminee': return 'Terminée';
      case 'annulee': return 'Annulée';
      default: return statut;
    }
  };

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px',
        fontSize: '1.2rem',
        color: '#6b7280'
      }}>
        Chargement des formations...
      </div>
    );
  }

  return (
    <div style={{ backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '2rem'
      }}>
        <div>
          <h1 style={{ margin: 0, fontSize: '2rem', fontWeight: '700', color: '#1f2937' }}>
            Gestion des Formations
          </h1>
          <p style={{ margin: '0.5rem 0 0 0', color: '#6b7280' }}>
            Planifiez, gérez et suivez les formations de vos employés
          </p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          style={{
            backgroundColor: '#3b82f6',
            color: 'white',
            border: 'none',
            padding: '0.75rem 1.5rem',
            borderRadius: '8px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            fontSize: '1rem',
            fontWeight: '600',
            transition: 'background-color 0.2s'
          }}
          onMouseEnter={(e) => e.target.style.backgroundColor = '#2563eb'}
          onMouseLeave={(e) => e.target.style.backgroundColor = '#3b82f6'}
        >
          <FaPlus /> Nouvelle Formation
        </button>
      </div>

      {/* Filtres et recherche */}
      <div style={{
        backgroundColor: 'white',
        padding: '1.5rem',
        borderRadius: '12px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        marginBottom: '2rem'
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '1rem',
          alignItems: 'end'
        }}>
          {/* Recherche */}
          <div>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
              Rechercher
            </label>
            <div style={{ position: 'relative' }}>
              <FaSearch style={{
                position: 'absolute',
                left: '12px',
                top: '50%',
                transform: 'translateY(-50%)',
                color: '#9ca3af'
              }} />
              <input
                type="text"
                placeholder="Nom, organisme..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.75rem 0.75rem 0.75rem 2.5rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '0.875rem'
                }}
              />
            </div>
          </div>

          {/* Filtre Type */}
          <div>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
              Type
            </label>
            <select
              value={filters.type}
              onChange={(e) => setFilters({...filters, type: e.target.value})}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '0.875rem'
              }}
            >
              <option value="">Tous les types</option>
              <option value="interne">Interne</option>
              <option value="externe">Externe</option>
            </select>
          </div>

          {/* Filtre Statut */}
          <div>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
              Statut
            </label>
            <select
              value={filters.statut}
              onChange={(e) => setFilters({...filters, statut: e.target.value})}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '0.875rem'
              }}
            >
              <option value="">Tous les statuts</option>
              <option value="planifiee">Planifiée</option>
              <option value="en_cours">En cours</option>
              <option value="terminee">Terminée</option>
              <option value="annulee">Annulée</option>
            </select>
          </div>

          {/* Filtre Certifiante */}
          <div>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
              Certification
            </label>
            <select
              value={filters.certifiante}
              onChange={(e) => setFilters({...filters, certifiante: e.target.value})}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '0.875rem'
              }}
            >
              <option value="">Toutes</option>
              <option value="true">Certifiante</option>
              <option value="false">Non certifiante</option>
            </select>
          </div>

          {/* Bouton Reset */}
          <div style={{ display: 'flex', alignItems: 'end' }}>
            <button
              onClick={() => {
                setFilters({ type: '', statut: '', certifiante: '', organisme: '' });
                setSearchTerm('');
              }}
              style={{
                padding: '0.75rem 1rem',
                backgroundColor: '#6b7280',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '0.875rem'
              }}
            >
              🔄 Reset
            </button>
          </div>
        </div>
      </div>

      {/* Message d'erreur */}
      {error && (
        <div style={{
          backgroundColor: '#fef2f2',
          color: '#dc2626',
          padding: '1rem',
          borderRadius: '8px',
          marginBottom: '1rem',
          border: '1px solid #fecaca'
        }}>
          {error}
        </div>
      )}

      {/* Liste des formations */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(400px, 1fr))',
        gap: '1.5rem'
      }}>
        {formations.map((formation) => (
          <div
            key={formation._id}
            style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              padding: '1.5rem',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              border: '1px solid #e5e7eb',
              transition: 'transform 0.2s, box-shadow 0.2s'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
            }}
          >
            {/* Header de la carte */}
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start', marginBottom: '1rem' }}>
              <div style={{ flex: 1 }}>
                <h3 style={{ margin: 0, fontSize: '1.25rem', fontWeight: '700', color: '#1f2937' }}>
                  {formation.nom_formation}
                </h3>
                <p style={{ margin: '0.25rem 0', color: '#6b7280', fontSize: '0.875rem' }}>
                  {formation.organisme}
                </p>
              </div>
              <span
                style={{
                  backgroundColor: getStatusColor(formation.statut),
                  color: 'white',
                  padding: '0.25rem 0.75rem',
                  borderRadius: '20px',
                  fontSize: '0.75rem',
                  fontWeight: '600'
                }}
              >
                {getStatusText(formation.statut)}
              </span>
            </div>

            {/* Informations */}
            <div style={{ marginBottom: '1rem' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                <FaCalendarAlt style={{ color: '#6b7280' }} />
                <span style={{ fontSize: '0.875rem', color: '#374151' }}>
                  {new Date(formation.date_debut).toLocaleDateString()} - {new Date(formation.date_fin).toLocaleDateString()}
                </span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                <span style={{
                  backgroundColor: formation.type === 'interne' ? '#dbeafe' : '#fef3c7',
                  color: formation.type === 'interne' ? '#1e40af' : '#92400e',
                  padding: '0.25rem 0.5rem',
                  borderRadius: '6px',
                  fontSize: '0.75rem',
                  fontWeight: '600'
                }}>
                  {formation.type === 'interne' ? 'Interne' : 'Externe'}
                </span>
                {formation.certifiante && (
                  <span style={{
                    backgroundColor: '#d1fae5',
                    color: '#065f46',
                    padding: '0.25rem 0.5rem',
                    borderRadius: '6px',
                    fontSize: '0.75rem',
                    fontWeight: '600'
                  }}>
                    Certifiante
                  </span>
                )}
              </div>
              <p style={{
                margin: '0.5rem 0 0 0',
                color: '#6b7280',
                fontSize: '0.875rem',
                lineHeight: '1.4',
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden'
              }}>
                {formation.description}
              </p>
            </div>

            {/* Actions */}
            <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
              <button
                onClick={() => {
                  setSelectedFormation(formation);
                  setShowCandidaturesModal(true);
                }}
                style={{
                  backgroundColor: '#3b82f6',
                  color: 'white',
                  border: 'none',
                  padding: '0.5rem 1rem',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  fontSize: '0.875rem'
                }}
              >
                <FaUsers /> Candidatures
              </button>
              <button
                onClick={() => {
                  setSelectedFormation(formation);
                  setShowEditModal(true);
                }}
                style={{
                  backgroundColor: '#f59e0b',
                  color: 'white',
                  border: 'none',
                  padding: '0.5rem 1rem',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  fontSize: '0.875rem'
                }}
              >
                <FaEdit /> Modifier
              </button>
              <button
                onClick={() => handleDeleteFormation(formation._id)}
                style={{
                  backgroundColor: '#ef4444',
                  color: 'white',
                  border: 'none',
                  padding: '0.5rem 1rem',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  fontSize: '0.875rem'
                }}
              >
                <FaTrash /> Supprimer
              </button>
            </div>
          </div>
        ))}
      </div>

      {formations.length === 0 && !loading && (
        <div style={{
          textAlign: 'center',
          padding: '3rem',
          color: '#6b7280'
        }}>
          <FaChartBar style={{ fontSize: '3rem', marginBottom: '1rem', opacity: 0.5 }} />
          <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '1.25rem' }}>Aucune formation trouvée</h3>
          <p style={{ margin: 0 }}>Commencez par créer votre première formation</p>
        </div>
      )}

      {/* Modals */}
      {showAddModal && (
        <FormationModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          onSave={fetchFormations}
          formation={null}
        />
      )}

      {showEditModal && selectedFormation && (
        <FormationModal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          onSave={fetchFormations}
          formation={selectedFormation}
        />
      )}

      {showCandidaturesModal && selectedFormation && (
        <CandidaturesViewModal
          formation={selectedFormation}
          onClose={() => {
            setShowCandidaturesModal(false);
            setSelectedFormation(null);
          }}
        />
      )}
    </div>
  );
};



export default FormationManagement;
