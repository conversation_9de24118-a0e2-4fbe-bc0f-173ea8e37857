import React, { useState, useEffect } from 'react';
import { FaUser, FaIdCard, FaCalendar, FaPhone, FaMapMarkerAlt, FaBuilding, FaBriefcase, FaEnvelope, FaFileContract } from 'react-icons/fa';

const EmployeeProfile = ({ employeeData }) => {
  const [contracts, setContracts] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (employeeData) {
      loadContracts();
    }
  }, [employeeData]);

  const loadContracts = async () => {
    setLoading(true);
    try {
      // TODO: Implémenter l'API pour récupérer les contrats de l'employé
      // const response = await contractService.getMyContracts();
      // setContracts(response.data);
      
      // Données de test pour l'instant
      setContracts([
        {
          _id: '1',
          type_contrat: 'CDI',
          date_debut: '2022-01-01',
          date_fin: null,
          statut: 'Actif',
          salaire: 45000
        }
      ]);
    } catch (error) {
      console.error('Erreur lors du chargement des contrats:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Non renseigné';
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const calculateAge = (dateString) => {
    if (!dateString) return '';
    const today = new Date();
    const birthDate = new Date(dateString);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return `(${age} ans)`;
  };

  const calculateSeniority = (dateString) => {
    if (!dateString) return '';
    const today = new Date();
    const startDate = new Date(dateString);
    const diffTime = Math.abs(today - startDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const years = Math.floor(diffDays / 365);
    const months = Math.floor((diffDays % 365) / 30);
    
    if (years > 0) {
      return `(${years} an${years > 1 ? 's' : ''} ${months > 0 ? `et ${months} mois` : ''})`;
    } else {
      return `(${months} mois)`;
    }
  };

  const InfoItem = ({ icon, label, value, extra }) => (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      gap: '1rem',
      padding: '0.75rem',
      backgroundColor: '#f9fafb',
      borderRadius: '8px',
      border: '1px solid #e5e7eb'
    }}>
      <div style={{
        width: '40px',
        height: '40px',
        borderRadius: '50%',
        backgroundColor: '#667eea',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        fontSize: '0.875rem'
      }}>
        {icon}
      </div>
      <div style={{ flex: 1 }}>
        <div style={{
          fontSize: '0.875rem',
          color: '#6b7280',
          fontWeight: '500'
        }}>
          {label}
        </div>
        <div style={{
          fontSize: '1rem',
          color: '#374151',
          fontWeight: '600'
        }}>
          {value} {extra && <span style={{ color: '#6b7280', fontWeight: '400' }}>{extra}</span>}
        </div>
      </div>
    </div>
  );

  if (!employeeData) {
    return (
      <div style={{ textAlign: 'center', padding: '2rem' }}>
        <p>Chargement des données...</p>
      </div>
    );
  }

  return (
    <div style={{ padding: '1rem' }}>
      {/* En-tête du profil */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '2rem',
        marginBottom: '2rem',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '2rem' }}>
          <div style={{
            width: '120px',
            height: '120px',
            borderRadius: '50%',
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            overflow: 'hidden',
            border: '4px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)'
          }}>
            {employeeData.photo ? (
              <img
                src={`http://localhost:4000${employeeData.photo}`}
                alt={`${employeeData.prenom} ${employeeData.nom}`}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover'
                }}
              />
            ) : (
              <FaUser style={{ fontSize: '3rem', color: 'rgba(255, 255, 255, 0.8)' }} />
            )}
          </div>
          <div style={{ flex: 1 }}>
            <h1 style={{
              margin: '0 0 0.5rem 0',
              fontSize: '2.5rem',
              fontWeight: '700'
            }}>
              {employeeData.prenom} {employeeData.nom}
            </h1>
            <p style={{
              margin: '0 0 1rem 0',
              fontSize: '1.3rem',
              opacity: 0.9,
              fontWeight: '500'
            }}>
              {employeeData.poste || 'Poste non défini'}
            </p>
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', flexWrap: 'wrap' }}>
              <span style={{
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                padding: '0.5rem 1rem',
                borderRadius: '20px',
                fontSize: '0.9rem',
                fontWeight: '600'
              }}>
                {employeeData.statut_compte || 'Actif'}
              </span>
              <span style={{
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                padding: '0.5rem 1rem',
                borderRadius: '20px',
                fontSize: '0.9rem',
                fontWeight: '600'
              }}>
                {employeeData.departement?.nom_departement || 'Département non assigné'}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
        gap: '2rem'
      }}>
        {/* Informations personnelles */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '1.5rem',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
          border: '1px solid #e5e7eb'
        }}>
          <h2 style={{
            margin: '0 0 1.5rem 0',
            fontSize: '1.3rem',
            color: '#374151',
            fontWeight: '600',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            <FaUser style={{ color: '#667eea' }} />
            Informations personnelles
          </h2>
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            <InfoItem
              icon={<FaIdCard />}
              label="CIN"
              value={employeeData.cin || 'Non renseigné'}
            />
            <InfoItem
              icon={<FaCalendar />}
              label="Date de naissance"
              value={formatDate(employeeData.date_naissance)}
              extra={calculateAge(employeeData.date_naissance)}
            />
            <InfoItem
              icon={<FaPhone />}
              label="Téléphone"
              value={employeeData.telephone || 'Non renseigné'}
            />
            <InfoItem
              icon={<FaMapMarkerAlt />}
              label="Adresse"
              value={employeeData.adresse || 'Non renseignée'}
            />
          </div>
        </div>

        {/* Informations professionnelles */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '1.5rem',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
          border: '1px solid #e5e7eb'
        }}>
          <h2 style={{
            margin: '0 0 1.5rem 0',
            fontSize: '1.3rem',
            color: '#374151',
            fontWeight: '600',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            <FaBriefcase style={{ color: '#667eea' }} />
            Informations professionnelles
          </h2>
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            <InfoItem
              icon={<FaBuilding />}
              label="Département"
              value={employeeData.departement?.nom_departement || 'Non assigné'}
            />
            <InfoItem
              icon={<FaBriefcase />}
              label="Poste"
              value={employeeData.poste || 'Non défini'}
            />
            <InfoItem
              icon={<FaCalendar />}
              label="Date d'embauche"
              value={formatDate(employeeData.date_embauche)}
              extra={calculateSeniority(employeeData.date_embauche)}
            />
            <InfoItem
              icon={<FaEnvelope />}
              label="Email professionnel"
              value={employeeData.utilisateur?.email || 'Non renseigné'}
            />
          </div>
        </div>
      </div>

      {/* Contrats */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '1.5rem',
        marginTop: '2rem',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        border: '1px solid #e5e7eb'
      }}>
        <h2 style={{
          margin: '0 0 1.5rem 0',
          fontSize: '1.3rem',
          color: '#374151',
          fontWeight: '600',
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          <FaFileContract style={{ color: '#667eea' }} />
          Mes contrats
        </h2>

        {loading ? (
          <p>Chargement des contrats...</p>
        ) : contracts.length > 0 ? (
          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ backgroundColor: '#f9fafb' }}>
                  <th style={{ padding: '0.75rem', textAlign: 'left', fontWeight: '600', color: '#374151' }}>Type</th>
                  <th style={{ padding: '0.75rem', textAlign: 'left', fontWeight: '600', color: '#374151' }}>Début</th>
                  <th style={{ padding: '0.75rem', textAlign: 'left', fontWeight: '600', color: '#374151' }}>Fin</th>
                  <th style={{ padding: '0.75rem', textAlign: 'left', fontWeight: '600', color: '#374151' }}>Statut</th>
                </tr>
              </thead>
              <tbody>
                {contracts.map((contract) => (
                  <tr key={contract._id} style={{ borderTop: '1px solid #e5e7eb' }}>
                    <td style={{ padding: '0.75rem', color: '#374151' }}>
                      <span style={{
                        backgroundColor: '#dbeafe',
                        color: '#1e40af',
                        padding: '0.25rem 0.5rem',
                        borderRadius: '4px',
                        fontSize: '0.875rem',
                        fontWeight: '500'
                      }}>
                        {contract.type_contrat}
                      </span>
                    </td>
                    <td style={{ padding: '0.75rem', color: '#374151' }}>{formatDate(contract.date_debut)}</td>
                    <td style={{ padding: '0.75rem', color: '#374151' }}>{contract.date_fin ? formatDate(contract.date_fin) : 'Indéterminée'}</td>
                    <td style={{ padding: '0.75rem', color: '#374151' }}>
                      <span style={{
                        backgroundColor: contract.statut === 'Actif' ? '#dcfce7' : '#fef2f2',
                        color: contract.statut === 'Actif' ? '#166534' : '#dc2626',
                        padding: '0.25rem 0.5rem',
                        borderRadius: '4px',
                        fontSize: '0.875rem',
                        fontWeight: '500'
                      }}>
                        {contract.statut}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p style={{ color: '#6b7280', textAlign: 'center', padding: '2rem' }}>
            Aucun contrat trouvé
          </p>
        )}
      </div>
    </div>
  );
};

export default EmployeeProfile;
