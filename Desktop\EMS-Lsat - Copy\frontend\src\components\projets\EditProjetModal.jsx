import React, { useState, useEffect } from 'react';
import { FaTimes, FaProjectDiagram, FaSave } from 'react-icons/fa';
import { useProjets } from '../../hooks/useProjets';
import { useEmployees } from '../../hooks/useEmployees';
import { useDepartments } from '../../hooks/useDepartments';
import { projetService } from '../../services/projetService';

const EditProjetModal = ({ projet, onClose }) => {
  const { updateProjet } = useProjets();
  const { employees } = useEmployees();
  const { departments } = useDepartments();
  const [formData, setFormData] = useState({
    nom_projet: '',
    description: '',
    date_debut: '',
    date_fin_prevue: '',
    chef_projet: '',
    priorite: 'Moyenne',
    statut: 'En attente',
    budget_alloue: '',
    budget_utilise: '',
    client: '',
    departement: '',
    progression: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const priorites = projetService.getPriorites();
  const statuts = projetService.getStatuts();

  // Initialiser le formulaire avec les données du projet
  useEffect(() => {
    if (projet) {
      setFormData({
        nom_projet: projet.nom_projet || '',
        description: projet.description || '',
        date_debut: projet.date_debut ? projet.date_debut.split('T')[0] : '',
        date_fin_prevue: projet.date_fin_prevue ? projet.date_fin_prevue.split('T')[0] : '',
        chef_projet: projet.chef_projet?._id || '',
        priorite: projet.priorite || 'Moyenne',
        statut: projet.statut || 'En attente',
        budget_alloue: projet.budget_alloue || '',
        budget_utilise: projet.budget_utilise || '',
        client: projet.client || '',
        departement: projet.departement?._id || '',
        progression: projet.progression || ''
      });
    }
  }, [projet]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    if (error) setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validation
    if (!formData.nom_projet.trim()) {
      setError('Le nom du projet est requis');
      return;
    }
    if (!formData.description.trim()) {
      setError('La description est requise');
      return;
    }
    if (!formData.date_debut) {
      setError('La date de début est requise');
      return;
    }
    if (!formData.date_fin_prevue) {
      setError('La date de fin prévue est requise');
      return;
    }
    if (!formData.chef_projet) {
      setError('Le chef de projet est requis');
      return;
    }

    // Validation des dates
    if (new Date(formData.date_fin_prevue) <= new Date(formData.date_debut)) {
      setError('La date de fin prévue doit être postérieure à la date de début');
      return;
    }

    // Validation du budget
    if (formData.budget_alloue && parseFloat(formData.budget_alloue) < 0) {
      setError('Le budget alloué doit être positif');
      return;
    }
    if (formData.budget_utilise && parseFloat(formData.budget_utilise) < 0) {
      setError('Le budget utilisé doit être positif');
      return;
    }
    if (formData.budget_utilise && formData.budget_alloue && 
        parseFloat(formData.budget_utilise) > parseFloat(formData.budget_alloue)) {
      setError('Le budget utilisé ne peut pas dépasser le budget alloué');
      return;
    }

    // Validation de la progression
    if (formData.progression && (parseFloat(formData.progression) < 0 || parseFloat(formData.progression) > 100)) {
      setError('La progression doit être entre 0 et 100%');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const projetData = {
        nom_projet: formData.nom_projet.trim(),
        description: formData.description.trim(),
        date_debut: formData.date_debut,
        date_fin_prevue: formData.date_fin_prevue,
        chef_projet: formData.chef_projet,
        priorite: formData.priorite,
        statut: formData.statut,
        budget_alloue: formData.budget_alloue ? parseFloat(formData.budget_alloue) : 0,
        budget_utilise: formData.budget_utilise ? parseFloat(formData.budget_utilise) : 0,
        client: formData.client.trim(),
        departement: formData.departement || undefined,
        progression: formData.progression ? parseFloat(formData.progression) : 0
      };

      await updateProjet(projet._id, projetData);
      onClose();
    } catch (error) {
      setError(error.message || 'Erreur lors de la mise à jour du projet');
    } finally {
      setLoading(false);
    }
  };

  // Vérifier si les données ont changé
  const hasChanges = projet && (
    formData.nom_projet !== (projet.nom_projet || '') ||
    formData.description !== (projet.description || '') ||
    formData.date_debut !== (projet.date_debut ? projet.date_debut.split('T')[0] : '') ||
    formData.date_fin_prevue !== (projet.date_fin_prevue ? projet.date_fin_prevue.split('T')[0] : '') ||
    formData.chef_projet !== (projet.chef_projet?._id || '') ||
    formData.priorite !== (projet.priorite || 'Moyenne') ||
    formData.statut !== (projet.statut || 'En attente') ||
    formData.budget_alloue !== (projet.budget_alloue || '') ||
    formData.budget_utilise !== (projet.budget_utilise || '') ||
    formData.client !== (projet.client || '') ||
    formData.departement !== (projet.departement?._id || '') ||
    formData.progression !== (projet.progression || '')
  );

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '1rem'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        width: '100%',
        maxWidth: '700px',
        maxHeight: '90vh',
        overflow: 'auto',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
      }}>
        {/* En-tête */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '1.5rem',
          borderBottom: '1px solid #e5e7eb'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.75rem'
          }}>
            <div style={{
              width: '40px',
              height: '40px',
              borderRadius: '8px',
              backgroundColor: '#f59e0b',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white'
            }}>
              <FaProjectDiagram />
            </div>
            <div>
              <h2 style={{
                margin: 0,
                fontSize: '1.5rem',
                fontWeight: '600',
                color: '#1f2937'
              }}>
                Modifier le projet
              </h2>
              <p style={{
                margin: '0.25rem 0 0 0',
                fontSize: '0.875rem',
                color: '#6b7280'
              }}>
                {projet?.nom_projet}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            style={{
              backgroundColor: 'transparent',
              border: 'none',
              color: '#6b7280',
              cursor: 'pointer',
              padding: '0.5rem',
              borderRadius: '6px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'background-color 0.2s'
            }}
            onMouseOver={(e) => e.target.style.backgroundColor = '#f3f4f6'}
            onMouseOut={(e) => e.target.style.backgroundColor = 'transparent'}
          >
            <FaTimes size={20} />
          </button>
        </div>

        {/* Contenu */}
        <form onSubmit={handleSubmit}>
          <div style={{ padding: '1.5rem' }}>
            {error && (
              <div style={{
                backgroundColor: '#fef2f2',
                border: '1px solid #fecaca',
                borderRadius: '8px',
                padding: '0.75rem',
                marginBottom: '1rem',
                color: '#dc2626',
                fontSize: '0.875rem'
              }}>
                {error}
              </div>
            )}

            {/* Nom du projet */}
            <div style={{ marginBottom: '1rem' }}>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                Nom du projet *
              </label>
              <input
                type="text"
                name="nom_projet"
                value={formData.nom_projet}
                onChange={handleChange}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  outline: 'none',
                  transition: 'border-color 0.2s',
                  boxSizing: 'border-box'
                }}
                onFocus={(e) => e.target.style.borderColor = '#f59e0b'}
                onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
              />
            </div>

            {/* Description */}
            <div style={{ marginBottom: '1rem' }}>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                Description *
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleChange}
                rows={3}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  outline: 'none',
                  transition: 'border-color 0.2s',
                  resize: 'vertical',
                  boxSizing: 'border-box'
                }}
                onFocus={(e) => e.target.style.borderColor = '#f59e0b'}
                onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
              />
            </div>

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '1rem' }}>
              {/* Statut */}
              <div style={{ marginBottom: '1rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  Statut *
                </label>
                <select
                  name="statut"
                  value={formData.statut}
                  onChange={handleChange}
                  required
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    outline: 'none',
                    transition: 'border-color 0.2s',
                    backgroundColor: 'white',
                    cursor: 'pointer',
                    boxSizing: 'border-box'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#f59e0b'}
                  onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                >
                  {statuts.map((statut) => (
                    <option key={statut.value} value={statut.value}>
                      {statut.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Priorité */}
              <div style={{ marginBottom: '1rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  Priorité
                </label>
                <select
                  name="priorite"
                  value={formData.priorite}
                  onChange={handleChange}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    outline: 'none',
                    transition: 'border-color 0.2s',
                    backgroundColor: 'white',
                    cursor: 'pointer',
                    boxSizing: 'border-box'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#f59e0b'}
                  onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                >
                  {priorites.map((priorite) => (
                    <option key={priorite.value} value={priorite.value}>
                      {priorite.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Progression */}
              <div style={{ marginBottom: '1rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  Progression (%)
                </label>
                <input
                  type="number"
                  name="progression"
                  value={formData.progression}
                  onChange={handleChange}
                  min="0"
                  max="100"
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    outline: 'none',
                    transition: 'border-color 0.2s',
                    boxSizing: 'border-box'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#f59e0b'}
                  onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                />
              </div>
            </div>

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
              {/* Date de début */}
              <div style={{ marginBottom: '1rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  Date de début *
                </label>
                <input
                  type="date"
                  name="date_debut"
                  value={formData.date_debut}
                  onChange={handleChange}
                  required
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    outline: 'none',
                    transition: 'border-color 0.2s',
                    boxSizing: 'border-box'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#f59e0b'}
                  onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                />
              </div>

              {/* Date de fin prévue */}
              <div style={{ marginBottom: '1rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  Date de fin prévue *
                </label>
                <input
                  type="date"
                  name="date_fin_prevue"
                  value={formData.date_fin_prevue}
                  onChange={handleChange}
                  required
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    outline: 'none',
                    transition: 'border-color 0.2s',
                    boxSizing: 'border-box'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#f59e0b'}
                  onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                />
              </div>
            </div>

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '1rem' }}>
              {/* Chef de projet */}
              <div style={{ marginBottom: '1rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  Chef de projet *
                </label>
                <select
                  name="chef_projet"
                  value={formData.chef_projet}
                  onChange={handleChange}
                  required
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    outline: 'none',
                    transition: 'border-color 0.2s',
                    backgroundColor: 'white',
                    cursor: 'pointer',
                    boxSizing: 'border-box'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#f59e0b'}
                  onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                >
                  <option value="">Sélectionner un chef de projet</option>
                  {employees.map((emp) => (
                    <option key={emp._id} value={emp._id}>
                      {emp.prenom} {emp.nom}
                    </option>
                  ))}
                </select>
              </div>

              {/* Budget alloué */}
              <div style={{ marginBottom: '1rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  Budget alloué (€)
                </label>
                <input
                  type="number"
                  name="budget_alloue"
                  value={formData.budget_alloue}
                  onChange={handleChange}
                  min="0"
                  step="0.01"
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    outline: 'none',
                    transition: 'border-color 0.2s',
                    boxSizing: 'border-box'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#f59e0b'}
                  onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                />
              </div>

              {/* Budget utilisé */}
              <div style={{ marginBottom: '1rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  Budget utilisé (€)
                </label>
                <input
                  type="number"
                  name="budget_utilise"
                  value={formData.budget_utilise}
                  onChange={handleChange}
                  min="0"
                  step="0.01"
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    outline: 'none',
                    transition: 'border-color 0.2s',
                    boxSizing: 'border-box'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#f59e0b'}
                  onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                />
              </div>
            </div>

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
              {/* Client */}
              <div style={{ marginBottom: '1rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  Client
                </label>
                <input
                  type="text"
                  name="client"
                  value={formData.client}
                  onChange={handleChange}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    outline: 'none',
                    transition: 'border-color 0.2s',
                    boxSizing: 'border-box'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#f59e0b'}
                  onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                />
              </div>

              {/* Département */}
              <div style={{ marginBottom: '1rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  Département
                </label>
                <select
                  name="departement"
                  value={formData.departement}
                  onChange={handleChange}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    outline: 'none',
                    transition: 'border-color 0.2s',
                    backgroundColor: 'white',
                    cursor: 'pointer',
                    boxSizing: 'border-box'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#f59e0b'}
                  onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                >
                  <option value="">Sélectionner un département</option>
                  {departments.map((dept) => (
                    <option key={dept._id} value={dept._id}>
                      {dept.nom_departement}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Pied de page */}
          <div style={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: '0.75rem',
            padding: '1.5rem',
            borderTop: '1px solid #e5e7eb',
            backgroundColor: '#f9fafb'
          }}>
            <button
              type="button"
              onClick={onClose}
              disabled={loading}
              style={{
                backgroundColor: 'white',
                color: '#374151',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                padding: '0.75rem 1.5rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: loading ? 'not-allowed' : 'pointer',
                transition: 'background-color 0.2s',
                opacity: loading ? 0.6 : 1
              }}
              onMouseOver={(e) => !loading && (e.target.style.backgroundColor = '#f9fafb')}
              onMouseOut={(e) => !loading && (e.target.style.backgroundColor = 'white')}
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={loading || !hasChanges}
              style={{
                backgroundColor: '#f59e0b',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                padding: '0.75rem 1.5rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: (loading || !hasChanges) ? 'not-allowed' : 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                transition: 'background-color 0.2s',
                opacity: (loading || !hasChanges) ? 0.6 : 1
              }}
              onMouseOver={(e) => {
                if (!loading && hasChanges) {
                  e.target.style.backgroundColor = '#d97706';
                }
              }}
              onMouseOut={(e) => {
                if (!loading && hasChanges) {
                  e.target.style.backgroundColor = '#f59e0b';
                }
              }}
            >
              <FaSave />
              {loading ? 'Mise à jour...' : 'Mettre à jour'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditProjetModal;
