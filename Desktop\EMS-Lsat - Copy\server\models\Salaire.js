import mongoose from "mongoose";

const salaireSchema = new mongoose.Schema({
  employe: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Employe",
    required: true,
    index: true
  },
  periode: {
    mois: {
      type: Number,
      required: true,
      min: 1,
      max: 12
    },
    annee: {
      type: Number,
      required: true,
      min: 2020
    }
  },
  // Salaire de base en Dirhams
  salaire_base: {
    type: Number,
    required: true,
    min: 0,
    comment: "Salaire de base mensuel en Dirhams"
  },

  // Heures supplémentaires
  heures_supplementaires: {
    nombre_heures: {
      type: Number,
      default: 0,
      min: 0
    },
    cout_horaire: {
      type: Number,
      default: 0,
      min: 0,
      comment: "Coût par heure supplémentaire en Dirhams"
    },
    montant_total: {
      type: Number,
      default: 0,
      min: 0
    }
  },

  // Primes et avantages
  primes: {
    prime_performance: { type: Number, default: 0 },
    prime_anciennete: { type: Number, default: 0 },
    prime_transport: { type: Number, default: 0 },
    prime_repas: { type: Number, default: 0 },
    autres_primes: { type: Number, default: 0 },
    total_primes: { type: Number, default: 0 }
  },

  avantages: {
    assurance_maladie: { type: Number, default: 0 },
    assurance_vie: { type: Number, default: 0 },
    formation: { type: Number, default: 0 },
    autres_avantages: { type: Number, default: 0 },
    total_avantages: { type: Number, default: 0 }
  },

  // Déductions et retenues
  deductions: {
    cnss_employe: { type: Number, default: 0 },
    ir: { type: Number, default: 0 }, // Impôt sur le revenu
    amo: { type: Number, default: 0 }, // Assurance Maladie Obligatoire
    cimr: { type: Number, default: 0 }, // Caisse Interprofessionnelle Marocaine de Retraite
    mutuelle: { type: Number, default: 0 },
    avance_salaire: { type: Number, default: 0 },
    autres_deductions: { type: Number, default: 0 },
    total_deductions: { type: Number, default: 0 }
  },

  // Calculs finaux
  salaire_brut: {
    type: Number,
    required: true,
    min: 0
  },
  salaire_net: {
    type: Number,
    required: true,
    min: 0
  },

  // Informations de versement
  date_versement: {
    type: Date,
    required: true
  },
  mode_paiement: {
    type: String,
    enum: ['Virement', 'Chèque', 'Espèces', 'Autre'],
    default: 'Virement'
  },

  // Statut du paiement
  statut_paiement: {
    type: String,
    enum: ['En attente', 'Payé', 'Annulé', 'Reporté'],
    default: 'En attente',
    index: true
  },

  // Informations bancaires au moment du paiement
  banque_versement: {
    nom_banque: String,
    rib: String,
    reference_virement: String
  },

  // Métadonnées
  cree_par: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Utilisateur",
    required: true
  },
  valide_par: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Utilisateur"
  },
  date_validation: Date,

  // Commentaires et notes
  commentaires: {
    type: String,
    trim: true
  },

  // Champs de compatibilité avec l'ancien modèle
  mois: {
    type: String,
    default: function() {
      return `${this.periode.mois.toString().padStart(2, '0')}/${this.periode.annee}`;
    }
  },
  total_primes: {
    type: Number,
    default: function() { return this.primes.total_primes; }
  },
  total_heures_supp: {
    type: Number,
    default: function() { return this.heures_supplementaires.nombre_heures; }
  },
  montant_heures_supp: {
    type: Number,
    default: function() { return this.heures_supplementaires.montant_total; }
  },
  retenues: {
    type: Number,
    default: function() { return this.deductions.total_deductions; }
  }
}, {
  timestamps: true
});

// Index composés pour optimiser les requêtes
salaireSchema.index({ employe: 1, 'periode.mois': 1, 'periode.annee': 1 }, { unique: true });
salaireSchema.index({ 'periode.annee': 1, 'periode.mois': 1 });
salaireSchema.index({ statut_paiement: 1 });
salaireSchema.index({ date_versement: 1 });

// Middleware pre-save pour calculer les totaux
salaireSchema.pre('save', function(next) {
  // Calculer le total des primes
  this.primes.total_primes =
    this.primes.prime_performance +
    this.primes.prime_anciennete +
    this.primes.prime_transport +
    this.primes.prime_repas +
    this.primes.autres_primes;

  // Calculer le total des avantages
  this.avantages.total_avantages =
    this.avantages.assurance_maladie +
    this.avantages.assurance_vie +
    this.avantages.formation +
    this.avantages.autres_avantages;

  // Calculer le total des déductions
  this.deductions.total_deductions =
    this.deductions.cnss_employe +
    this.deductions.ir +
    this.deductions.amo +
    this.deductions.cimr +
    this.deductions.mutuelle +
    this.deductions.avance_salaire +
    this.deductions.autres_deductions;

  // Calculer le montant total des heures supplémentaires
  this.heures_supplementaires.montant_total =
    this.heures_supplementaires.nombre_heures * this.heures_supplementaires.cout_horaire;

  // Calculer le salaire brut
  this.salaire_brut =
    this.salaire_base +
    this.primes.total_primes +
    this.heures_supplementaires.montant_total +
    this.avantages.total_avantages;

  // Calculer le salaire net
  this.salaire_net = this.salaire_brut - this.deductions.total_deductions;

  next();
});

// Méthode pour générer le bulletin de paie
salaireSchema.methods.genererBulletinPaie = function() {
  return {
    employe: this.employe,
    periode: `${this.periode.mois.toString().padStart(2, '0')}/${this.periode.annee}`,
    salaire_base: this.salaire_base,
    heures_supplementaires: this.heures_supplementaires,
    primes: this.primes,
    avantages: this.avantages,
    deductions: this.deductions,
    salaire_brut: this.salaire_brut,
    salaire_net: this.salaire_net,
    date_versement: this.date_versement,
    mode_paiement: this.mode_paiement
  };
};

const Salaire = mongoose.model("Salaire", salaireSchema);
export default Salaire;
