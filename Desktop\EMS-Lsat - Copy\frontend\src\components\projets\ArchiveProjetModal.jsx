import React, { useState } from 'react';
import { FaTimes, FaArchive, FaBoxOpen } from 'react-icons/fa';
import { useProjets } from '../../hooks/useProjets';
import { projetService } from '../../services/projetService';

const ArchiveProjetModal = ({ projet, onClose }) => {
  const { archiverProjet } = useProjets();
  const [formData, setFormData] = useState({
    raison_archivage: 'Projet terminé',
    description_archivage: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const raisonsArchivage = projetService.getRaisonsArchivage();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    if (error) setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.raison_archivage) {
      setError('La raison d\'archivage est requise');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const archiveData = {
        raison_archivage: formData.raison_archivage,
        description_archivage: formData.description_archivage.trim()
      };

      await archiverProjet(projet._id, archiveData);
      onClose(); // Fermer le modal et recharger la liste
    } catch (error) {
      setError(error.message || 'Erreur lors de l\'archivage du projet');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '1rem'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '16px',
        width: '500px',
        maxWidth: '90vw',
        height: '85vh',
        maxHeight: '85vh',
        overflow: 'hidden',
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        border: '1px solid #e5e7eb',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* En-tête compact */}
        <div style={{
          background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',
          color: 'white',
          padding: '1rem',
          position: 'relative',
          flexShrink: 0
        }}>
          <button
            onClick={onClose}
            disabled={loading}
            style={{
              position: 'absolute',
              top: '1rem',
              right: '1rem',
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              border: 'none',
              color: 'white',
              cursor: loading ? 'not-allowed' : 'pointer',
              padding: '0.5rem',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'background-color 0.2s',
              opacity: loading ? 0.6 : 1,
              width: '32px',
              height: '32px'
            }}
            onMouseOver={(e) => !loading && (e.target.style.backgroundColor = 'rgba(255, 255, 255, 0.3)')}
            onMouseOut={(e) => !loading && (e.target.style.backgroundColor = 'rgba(255, 255, 255, 0.2)')}
          >
            <FaTimes size={14} />
          </button>

          <div style={{ textAlign: 'center' }}>
            <div style={{
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 0.75rem',
              fontSize: '1.25rem'
            }}>
              <FaArchive />
            </div>
            <h2 style={{
              margin: '0 0 0.25rem 0',
              fontSize: '1.125rem',
              fontWeight: '600'
            }}>
              Archiver le projet
            </h2>
            <p style={{
              margin: 0,
              fontSize: '0.8rem',
              opacity: 0.9,
              fontWeight: '500'
            }}>
              {projet?.nom_projet}
            </p>
          </div>
        </div>

        {/* Contenu avec scroll */}
        <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', flex: 1 }}>
          <div style={{
            flex: 1,
            overflowY: 'auto',
            padding: '1rem'
          }}>
              {error && (
                <div style={{
                  backgroundColor: '#fef2f2',
                  border: '1px solid #fecaca',
                  borderRadius: '8px',
                  padding: '0.75rem',
                  marginBottom: '1rem',
                  color: '#dc2626',
                  fontSize: '0.875rem'
                }}>
                  {error}
                </div>
              )}

              {/* Aperçu compact du projet */}
              <div style={{
                backgroundColor: '#f8fafc',
                border: '1px solid #e2e8f0',
                borderRadius: '8px',
                padding: '1rem',
                marginBottom: '1.25rem'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem',
                  marginBottom: '0.75rem'
                }}>
                  <div style={{
                    width: '40px',
                    height: '40px',
                    borderRadius: '8px',
                    backgroundColor: projetService.getPrioriteColor(projet.priorite),
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '1.25rem'
                  }}>
                    {projetService.getPrioriteIcon(projet.priorite)}
                  </div>
                  <div style={{ flex: 1 }}>
                    <h4 style={{
                      margin: 0,
                      fontSize: '1rem',
                      fontWeight: '600',
                      color: '#1f2937'
                    }}>
                      {projet.nom_projet}
                    </h4>
                    <p style={{
                      margin: '0.25rem 0 0 0',
                      fontSize: '0.8rem',
                      color: '#6b7280'
                    }}>
                      Chef: {projet.chef_projet?.prenom} {projet.chef_projet?.nom}
                    </p>
                  </div>
                </div>

                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  fontSize: '0.8rem',
                  color: '#6b7280'
                }}>
                  <span>📅 {projetService.formatDate(projet.date_debut)}</span>
                  <span>💰 {projetService.formatBudget(projet.budget_alloue)}</span>
                  <span>📊 {projet.progressionCalculee || projet.progression || 0}%</span>
                </div>
              </div>

              {/* Formulaire simple */}
              <div style={{ marginBottom: '1.25rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  Raison de l'archivage *
                </label>
                <select
                  name="raison_archivage"
                  value={formData.raison_archivage}
                  onChange={handleChange}
                  required
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '0.875rem',
                    outline: 'none',
                    transition: 'border-color 0.2s',
                    backgroundColor: 'white',
                    cursor: 'pointer',
                    boxSizing: 'border-box'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#6b7280'}
                  onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                >
                  {raisonsArchivage.map((raison) => (
                    <option key={raison.value} value={raison.value}>
                      {raison.label}
                    </option>
                  ))}
                </select>
              </div>

              

              {/* Note importante */}
              <div style={{
                backgroundColor: '#fffbeb',
                border: '1px solid #fed7aa',
                borderRadius: '8px',
                padding: '1rem',
                fontSize: '0.875rem',
                color: '#92400e'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  marginBottom: '0.5rem'
                }}>
                  <span style={{ fontSize: '1.25rem' }}>⚖️</span>
                  <strong>Conservation légale</strong>
                </div>
                <ul style={{
                  margin: 0,
                  paddingLeft: '1.25rem',
                  lineHeight: '1.5'
                }}>
                  <li>Conservation pendant 5 ans minimum</li>
                  <li>Toutes les données et tâches incluses</li>
                  <li>Restauration possible à tout moment</li>
                  <li>Données sécurisées et chiffrées</li>
                </ul>
              </div>
          </div>

          {/* Pied de page simple - toujours visible */}
          <div style={{
            borderTop: '1px solid #e5e7eb',
            backgroundColor: '#f9fafb',
            padding: '1rem',
            display: 'flex',
            justifyContent: 'flex-end',
            gap: '0.75rem',
            flexShrink: 0,
            marginTop: 'auto'
          }}>
            <button
              type="button"
              onClick={onClose}
              disabled={loading}
              style={{
                backgroundColor: 'white',
                color: '#374151',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                padding: '0.75rem 1.25rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: loading ? 'not-allowed' : 'pointer',
                transition: 'background-color 0.2s',
                opacity: loading ? 0.6 : 1
              }}
              onMouseOver={(e) => !loading && (e.target.style.backgroundColor = '#f3f4f6')}
              onMouseOut={(e) => !loading && (e.target.style.backgroundColor = 'white')}
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={loading || !formData.raison_archivage}
              style={{
                backgroundColor: '#6b7280',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                padding: '0.75rem 1.5rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: (loading || !formData.raison_archivage) ? 'not-allowed' : 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                transition: 'background-color 0.2s',
                opacity: (loading || !formData.raison_archivage) ? 0.6 : 1
              }}
              onMouseOver={(e) => {
                if (!loading && formData.raison_archivage) {
                  e.target.style.backgroundColor = '#4b5563';
                }
              }}
              onMouseOut={(e) => {
                if (!loading && formData.raison_archivage) {
                  e.target.style.backgroundColor = '#6b7280';
                }
              }}
            >
              <FaArchive />
              {loading ? 'Archivage...' : 'Archiver'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ArchiveProjetModal;
