/**
 * Constantes pour les permissions
 */

export const PERMISSIONS = {
    // Permissions utilisateurs
    READ_USERS: 'read_users',
    WRITE_USERS: 'write_users',
    DELETE_USERS: 'delete_users',
    
    // Permissions employés
    READ_EMPLOYEES: 'read_employees',
    WRITE_EMPLOYEES: 'write_employees',
    DELETE_EMPLOYEES: 'delete_employees',
    
    // Permissions départements
    READ_DEPARTMENTS: 'read_departments',
    WRITE_DEPARTMENTS: 'write_departments',
    DELETE_DEPARTMENTS: 'delete_departments',
    
    // Permissions système
    SYSTEM_ADMIN: 'system_admin',
    BACKUP_RESTORE: 'backup_restore',
    VIEW_LOGS: 'view_logs',
    MANAGE_SECURITY: 'manage_security'
};

export const PERMISSION_LABELS = {
    [PERMISSIONS.READ_USERS]: 'Lire les utilisateurs',
    [PERMISSIONS.WRITE_USERS]: 'Créer/Modifier les utilisateurs',
    [PERMISSIONS.DELETE_USERS]: 'Supprimer les utilisateurs',
    
    [PERMISSIONS.READ_EMPLOYEES]: 'Lire les employés',
    [PERMISSIONS.WRITE_EMPLOYEES]: 'Créer/Modifier les employés',
    [PERMISSIONS.DELETE_EMPLOYEES]: 'Supprimer les employés',
    
    [PERMISSIONS.READ_DEPARTMENTS]: 'Lire les départements',
    [PERMISSIONS.WRITE_DEPARTMENTS]: 'Créer/Modifier les départements',
    [PERMISSIONS.DELETE_DEPARTMENTS]: 'Supprimer les départements',
    
    [PERMISSIONS.SYSTEM_ADMIN]: 'Administration système',
    [PERMISSIONS.BACKUP_RESTORE]: 'Sauvegarde/Restauration',
    [PERMISSIONS.VIEW_LOGS]: 'Voir les logs',
    [PERMISSIONS.MANAGE_SECURITY]: 'Gérer la sécurité'
};

export const PERMISSION_GROUPS = {
    users: {
        label: 'Utilisateurs',
        permissions: [
            PERMISSIONS.READ_USERS,
            PERMISSIONS.WRITE_USERS,
            PERMISSIONS.DELETE_USERS
        ]
    },
    employees: {
        label: 'Employés',
        permissions: [
            PERMISSIONS.READ_EMPLOYEES,
            PERMISSIONS.WRITE_EMPLOYEES,
            PERMISSIONS.DELETE_EMPLOYEES
        ]
    },
    departments: {
        label: 'Départements',
        permissions: [
            PERMISSIONS.READ_DEPARTMENTS,
            PERMISSIONS.WRITE_DEPARTMENTS,
            PERMISSIONS.DELETE_DEPARTMENTS
        ]
    },
    system: {
        label: 'Système',
        permissions: [
            PERMISSIONS.SYSTEM_ADMIN,
            PERMISSIONS.BACKUP_RESTORE,
            PERMISSIONS.VIEW_LOGS,
            PERMISSIONS.MANAGE_SECURITY
        ]
    }
};

/**
 * Obtenir le label d'une permission
 * @param {string} permission - Permission
 * @returns {string} Label de la permission
 */
export const getPermissionLabel = (permission) => {
    return PERMISSION_LABELS[permission] || permission;
};
