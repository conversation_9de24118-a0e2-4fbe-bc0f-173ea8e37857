import React from 'react';
import { FaTimes, FaUser, FaEnvelope, FaPhone, FaMapMarkerAlt, FaCalendar, FaBuilding, FaBriefcase, FaIdCard, FaEdit } from 'react-icons/fa';

const MyInfoModal = ({ employee, onClose }) => {
  const formatDate = (dateString) => {
    if (!dateString) return 'Non défini';
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const getStatusColor = (status) => {
    return status === 'actif' ? '#10b981' : '#ef4444';
  };

  const getStatusBgColor = (status) => {
    return status === 'actif' ? '#d1fae5' : '#fee2e2';
  };

  const calculateAge = (birthDate) => {
    if (!birthDate) return 'Non défini';
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    return `${age} ans`;
  };

  const calculateSeniority = (hireDate) => {
    if (!hireDate) return 'Non défini';
    const today = new Date();
    const hire = new Date(hireDate);
    const diffTime = Math.abs(today - hire);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const years = Math.floor(diffDays / 365);
    const months = Math.floor((diffDays % 365) / 30);
    
    if (years > 0) {
      return `${years} an${years > 1 ? 's' : ''} et ${months} mois`;
    } else {
      return `${months} mois`;
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '1rem'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '16px',
        width: '100%',
        maxWidth: '800px',
        maxHeight: '90vh',
        overflow: 'auto',
        boxShadow: '0 20px 25px rgba(0, 0, 0, 0.3)'
      }}>
        {/* Header */}
        <div style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          padding: '2rem',
          borderRadius: '16px 16px 0 0',
          position: 'relative'
        }}>
          <button
            onClick={onClose}
            style={{
              position: 'absolute',
              top: '1rem',
              right: '1rem',
              background: 'rgba(255, 255, 255, 0.2)',
              border: 'none',
              borderRadius: '50%',
              width: '40px',
              height: '40px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '1.2rem',
              cursor: 'pointer',
              color: 'white',
              transition: 'background-color 0.2s'
            }}
            onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(255, 255, 255, 0.3)'}
            onMouseLeave={(e) => e.target.style.backgroundColor = 'rgba(255, 255, 255, 0.2)'}
          >
            <FaTimes />
          </button>

          <div style={{ display: 'flex', alignItems: 'center', gap: '2rem' }}>
            {/* Photo de profil */}
            <div style={{
              width: '120px',
              height: '120px',
              borderRadius: '50%',
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              overflow: 'hidden',
              border: '4px solid rgba(255, 255, 255, 0.3)',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)'
            }}>
              {employee.photo ? (
                <img
                  src={`http://localhost:4000${employee.photo}`}
                  alt={`${employee.prenom} ${employee.nom}`}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover'
                  }}
                />
              ) : (
                <FaUser style={{ fontSize: '3rem', color: 'rgba(255, 255, 255, 0.8)' }} />
              )}
            </div>

            {/* Informations principales */}
            <div style={{ flex: 1 }}>
              <h1 style={{
                margin: '0 0 0.5rem 0',
                fontSize: '2.5rem',
                fontWeight: '700'
              }}>
                {employee.prenom} {employee.nom}
              </h1>
              <p style={{
                margin: '0 0 1rem 0',
                fontSize: '1.3rem',
                opacity: 0.9,
                fontWeight: '500'
              }}>
                {employee.poste || 'Poste non défini'}
              </p>
              <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', flexWrap: 'wrap' }}>
                <span style={{
                  backgroundColor: getStatusBgColor(employee.statut_compte),
                  color: getStatusColor(employee.statut_compte),
                  padding: '0.5rem 1rem',
                  borderRadius: '20px',
                  fontSize: '0.9rem',
                  fontWeight: '600',
                  textTransform: 'capitalize'
                }}>
                  {employee.statut_compte || 'Actif'}
                </span>
                <span style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  padding: '0.5rem 1rem',
                  borderRadius: '20px',
                  fontSize: '0.9rem',
                  fontWeight: '600'
                }}>
                  {employee.departement?.nom_departement || 'Aucun département'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Contenu */}
        <div style={{ padding: '2rem' }}>
          {/* Grille d'informations */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
            gap: '2rem'
          }}>
            {/* Informations personnelles */}
            <div style={{
              backgroundColor: '#f8fafc',
              border: '1px solid #e2e8f0',
              borderRadius: '12px',
              padding: '1.5rem'
            }}>
              <h3 style={{
                margin: '0 0 1.5rem 0',
                fontSize: '1.3rem',
                fontWeight: '600',
                color: '#374151',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}>
                <FaUser style={{ color: '#667eea' }} />
                Informations personnelles
              </h3>
              
              <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                <InfoItem
                  icon={<FaIdCard />}
                  label="CIN"
                  value={employee.cin || 'Non renseigné'}
                />
                <InfoItem
                  icon={<FaCalendar />}
                  label="Date de naissance"
                  value={formatDate(employee.date_naissance)}
                  extra={calculateAge(employee.date_naissance)}
                />
                <InfoItem
                  icon={<FaPhone />}
                  label="Téléphone"
                  value={employee.telephone || 'Non renseigné'}
                />
                <InfoItem
                  icon={<FaMapMarkerAlt />}
                  label="Adresse"
                  value={employee.adresse || 'Non renseignée'}
                />
              </div>
            </div>

            {/* Informations professionnelles */}
            <div style={{
              backgroundColor: '#f0fdf4',
              border: '1px solid #bbf7d0',
              borderRadius: '12px',
              padding: '1.5rem'
            }}>
              <h3 style={{
                margin: '0 0 1.5rem 0',
                fontSize: '1.3rem',
                fontWeight: '600',
                color: '#374151',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}>
                <FaBriefcase style={{ color: '#10b981' }} />
                Informations professionnelles
              </h3>
              
              <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                <InfoItem
                  icon={<FaBuilding />}
                  label="Département"
                  value={employee.departement?.nom_departement || 'Non assigné'}
                />
                <InfoItem
                  icon={<FaBriefcase />}
                  label="Poste"
                  value={employee.poste || 'Non défini'}
                />
                <InfoItem
                  icon={<FaCalendar />}
                  label="Date d'embauche"
                  value={formatDate(employee.date_embauche)}
                  extra={calculateSeniority(employee.date_embauche)}
                />
                <InfoItem
                  icon={<FaEnvelope />}
                  label="Email professionnel"
                  value={employee.utilisateur?.email || 'Non renseigné'}
                />
              </div>
            </div>
          </div>

          {/* Contacts d'urgence */}
          {employee.contacts_urgence && employee.contacts_urgence.length > 0 && (
            <div style={{
              marginTop: '2rem',
              backgroundColor: '#fef3c7',
              border: '1px solid #fbbf24',
              borderRadius: '12px',
              padding: '1.5rem'
            }}>
              <h3 style={{
                margin: '0 0 1rem 0',
                fontSize: '1.3rem',
                fontWeight: '600',
                color: '#374151',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}>
                <FaPhone style={{ color: '#f59e0b' }} />
                Contacts d'urgence
              </h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
                {employee.contacts_urgence.map((contact, index) => (
                  <div key={index} style={{
                    padding: '1rem',
                    backgroundColor: 'rgba(255, 255, 255, 0.7)',
                    borderRadius: '8px',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    flexWrap: 'wrap',
                    gap: '0.5rem'
                  }}>
                    <div>
                      <strong style={{ color: '#374151' }}>{contact.nom}</strong>
                      <span style={{ color: '#6b7280', marginLeft: '0.5rem' }}>({contact.relation})</span>
                    </div>
                    <div style={{ 
                      color: '#374151', 
                      fontWeight: '500',
                      backgroundColor: 'white',
                      padding: '0.25rem 0.75rem',
                      borderRadius: '20px'
                    }}>
                      {contact.telephone}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Personnes à charge */}
          {employee.personnes_a_charge && employee.personnes_a_charge.length > 0 && (
            <div style={{
              marginTop: '2rem',
              backgroundColor: '#e0f2fe',
              border: '1px solid #0ea5e9',
              borderRadius: '12px',
              padding: '1.5rem'
            }}>
              <h3 style={{
                margin: '0 0 1rem 0',
                fontSize: '1.3rem',
                fontWeight: '600',
                color: '#374151',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}>
                <FaUser style={{ color: '#0ea5e9' }} />
                Personnes à charge
              </h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
                {employee.personnes_a_charge.map((personne, index) => (
                  <div key={index} style={{
                    padding: '1rem',
                    backgroundColor: 'rgba(255, 255, 255, 0.7)',
                    borderRadius: '8px',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    flexWrap: 'wrap',
                    gap: '0.5rem'
                  }}>
                    <div>
                      <strong style={{ color: '#374151' }}>{personne.nom}</strong>
                      <span style={{ color: '#6b7280', marginLeft: '0.5rem' }}>({personne.relation})</span>
                    </div>
                    <div style={{ 
                      color: '#374151', 
                      fontWeight: '500',
                      backgroundColor: 'white',
                      padding: '0.25rem 0.75rem',
                      borderRadius: '20px'
                    }}>
                      {formatDate(personne.date_naissance)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Bouton de fermeture */}
          <div style={{
            marginTop: '2rem',
            textAlign: 'center'
          }}>
            <button
              onClick={onClose}
              style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                border: 'none',
                padding: '1rem 2rem',
                borderRadius: '8px',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'transform 0.2s, box-shadow 0.2s'
              }}
              onMouseEnter={(e) => {
                e.target.style.transform = 'translateY(-2px)';
                e.target.style.boxShadow = '0 8px 25px rgba(102, 126, 234, 0.4)';
              }}
              onMouseLeave={(e) => {
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = 'none';
              }}
            >
              Fermer
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Composant pour afficher une information
const InfoItem = ({ icon, label, value, extra }) => (
  <div style={{
    display: 'flex',
    alignItems: 'flex-start',
    gap: '1rem',
    padding: '0.75rem 0'
  }}>
    <div style={{ 
      color: '#6b7280', 
      fontSize: '1.1rem', 
      minWidth: '24px',
      marginTop: '0.1rem'
    }}>
      {icon}
    </div>
    <div style={{ flex: 1 }}>
      <div style={{ 
        color: '#6b7280', 
        fontSize: '0.9rem', 
        fontWeight: '500',
        marginBottom: '0.25rem'
      }}>
        {label}:
      </div>
      <div style={{ 
        color: '#374151', 
        fontWeight: '600',
        fontSize: '1rem'
      }}>
        {value}
      </div>
      {extra && (
        <div style={{ 
          color: '#9ca3af', 
          fontSize: '0.8rem',
          fontStyle: 'italic',
          marginTop: '0.25rem'
        }}>
          {extra}
        </div>
      )}
    </div>
  </div>
);

export default MyInfoModal;
