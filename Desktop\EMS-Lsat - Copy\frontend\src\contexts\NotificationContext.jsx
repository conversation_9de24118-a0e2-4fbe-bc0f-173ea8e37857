import React, { createContext, useContext, useState, useCallback } from 'react';
import ToastNotification from '../components/notifications/ToastNotification';

/**
 * Contexte global pour gérer les notifications toast
 * Permet d'afficher des notifications depuis n'importe quel composant
 * Gère la pile de notifications avec auto-dismiss et animations
 */
const NotificationContext = createContext();

/**
 * Hook pour utiliser le contexte de notifications
 * @returns {Object} Fonctions pour gérer les notifications
 */
export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications doit être utilisé dans un NotificationProvider');
  }
  return context;
};

/**
 * Provider de notifications global
 * Doit envelopper l'application pour permettre l'utilisation des notifications
 * @param {Object} children - Composants enfants
 */
export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);

  /**
   * Ajouter une nouvelle notification
   * @param {Object} notification - Objet notification
   * @param {string} notification.titre - Titre de la notification
   * @param {string} notification.message - Message de la notification
   * @param {string} notification.type - Type: 'success', 'error', 'warning', 'info'
   * @param {number} notification.duration - Durée d'affichage (défaut: 5000ms)
   * @param {Object} notification.data - Données additionnelles
   */
  const addNotification = useCallback((notification) => {
    const id = Date.now() + Math.random(); // ID unique
    const newNotification = {
      id,
      titre: notification.titre || 'Notification',
      message: notification.message || '',
      type: notification.type || 'info',
      duration: notification.duration !== undefined ? notification.duration : 5000,
      date: notification.date || new Date(),
      ...notification // Spread pour inclure toutes les propriétés additionnelles
    };

    setNotifications(prev => [...prev, newNotification]);
    
    console.log('🔔 Nouvelle notification ajoutée:', newNotification.titre);
    return id;
  }, []);

  /**
   * Supprimer une notification par son ID
   * @param {string|number} id - ID de la notification à supprimer
   */
  const removeNotification = useCallback((id) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
    console.log('🗑️ Notification supprimée:', id);
  }, []);

  /**
   * Supprimer toutes les notifications
   */
  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
    console.log('🧹 Toutes les notifications supprimées');
  }, []);

  /**
   * Fonctions de raccourci pour les différents types de notifications
   */
  const showSuccess = useCallback((titre, message, options = {}) => {
    return addNotification({
      titre,
      message,
      type: 'success',
      ...options
    });
  }, [addNotification]);

  const showError = useCallback((titre, message, options = {}) => {
    return addNotification({
      titre,
      message,
      type: 'error',
      duration: 7000, // Plus long pour les erreurs
      ...options
    });
  }, [addNotification]);

  const showWarning = useCallback((titre, message, options = {}) => {
    return addNotification({
      titre,
      message,
      type: 'warning',
      duration: 6000,
      ...options
    });
  }, [addNotification]);

  const showInfo = useCallback((titre, message, options = {}) => {
    return addNotification({
      titre,
      message,
      type: 'info',
      ...options
    });
  }, [addNotification]);

  /**
   * Fonction spécialisée pour les notifications de candidatures
   * @param {Object} candidature - Objet candidature avec statut
   */
  const showCandidatureNotification = useCallback((candidature) => {
    const isApproved = candidature.statut === 'approuvee';
    
    return addNotification({
      titre: isApproved ? '🎉 Candidature approuvée !' : '📝 Candidature traitée',
      message: isApproved 
        ? `Votre candidature pour "${candidature.formation_nom}" a été approuvée !`
        : `Votre candidature pour "${candidature.formation_nom}" a été traitée.`,
      type: 'candidature_response',
      statut: candidature.statut,
      formation_nom: candidature.formation_nom,
      message_rh: candidature.message_rh,
      candidature_id: candidature.candidature_id,
      duration: isApproved ? 8000 : 6000, // Plus long pour les bonnes nouvelles
      priority: isApproved ? 'high' : 'medium'
    });
  }, [addNotification]);

  /**
   * Fonction pour afficher plusieurs notifications de candidatures
   * @param {Array} candidatures - Liste des candidatures à notifier
   */
  const showMultipleCandidatureNotifications = useCallback((candidatures) => {
    candidatures.forEach((candidature, index) => {
      // Délai progressif pour éviter l'encombrement
      setTimeout(() => {
        showCandidatureNotification(candidature);
      }, index * 500);
    });
  }, [showCandidatureNotification]);

  // Valeurs du contexte
  const contextValue = {
    notifications,
    addNotification,
    removeNotification,
    clearAllNotifications,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showCandidatureNotification,
    showMultipleCandidatureNotifications,
    // Statistiques utiles
    hasNotifications: notifications.length > 0,
    notificationCount: notifications.length
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
      
      {/* Container des notifications toast */}
      <div style={{
        position: 'fixed',
        top: 0,
        right: 0,
        zIndex: 9999,
        pointerEvents: 'none' // Permet les clics à travers le container
      }}>
        {notifications.map((notification, index) => (
          <div
            key={notification.id}
            style={{
              marginBottom: '0.5rem',
              marginTop: index === 0 ? '1rem' : '0',
              marginRight: '1rem',
              pointerEvents: 'auto' // Réactive les clics pour les toasts individuels
            }}
          >
            <ToastNotification
              notification={notification}
              onClose={removeNotification}
              duration={notification.duration}
              position="top-right"
            />
          </div>
        ))}
      </div>
    </NotificationContext.Provider>
  );
};

export default NotificationProvider;
