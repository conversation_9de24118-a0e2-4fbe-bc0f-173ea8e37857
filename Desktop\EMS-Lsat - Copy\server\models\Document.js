const mongoose = require("mongoose");
const documentSchema = new mongoose.Schema({
  employe: { type: mongoose.Schema.Types.ObjectId, ref: "employes" },
  nom_fichier: String,
  type: { type: String, enum: ["CV", "attestation", "autre"] },
  chemin_fichier: String,
  type_stockage: { type: String, enum: ["local", "cloud"], default: "local" },
  langue: { type: String, default: "fr" },
  date_upload: { type: Date, default: Date.now }
}, { timestamps: true });

module.exports = mongoose.model("Document", documentSchema);
