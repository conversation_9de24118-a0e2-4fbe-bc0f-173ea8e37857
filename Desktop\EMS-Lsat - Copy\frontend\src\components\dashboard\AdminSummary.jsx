import React from 'react';
import { FaUsers, FaBuilding, FaShieldAlt, FaChartLine, FaDatabase, FaBell, FaUserShield, FaCog } from 'react-icons/fa';
import ProjetStats from '../projets/ProjetStats';

const AdminSummary = () => {
  const stats = [
    {
      title: 'Total Utilisateurs',
      value: '156',
      icon: FaUsers,
      color: '#3b82f6',
      bgColor: '#eff6ff',
      change: '+12%',
      changeType: 'positive'
    },
    {
      title: 'Administrateurs',
      value: '3',
      icon: FaUserShield,
      color: '#dc2626',
      bgColor: '#fef2f2',
      change: '+1',
      changeType: 'positive'
    },
    {
      title: 'Départements',
      value: '8',
      icon: FaBuilding,
      color: '#10b981',
      bgColor: '#f0fdf4',
      change: '+2',
      changeType: 'positive'
    },
    {
      title: 'Alertes Sécurité',
      value: '3',
      icon: FaShieldAlt,
      color: '#f59e0b',
      bgColor: '#fffbeb',
      change: '-5',
      changeType: 'negative'
    },
    {
      title: 'Performance Système',
      value: '98.5%',
      icon: FaChartLine,
      color: '#8b5cf6',
      bgColor: '#f5f3ff',
      change: '+0.3%',
      changeType: 'positive'
    },
    {
      title: 'Stockage Utilisé',
      value: '2.4 GB',
      icon: FaDatabase,
      color: '#06b6d4',
      bgColor: '#f0f9ff',
      change: '+156 MB',
      changeType: 'neutral'
    }
  ];

  const quickActions = [
    { title: 'Créer un utilisateur', desc: 'Ajouter un nouvel utilisateur au système', icon: FaUsers, color: '#3b82f6' },
    { title: 'Sauvegarder la base', desc: 'Effectuer une sauvegarde complète', icon: FaDatabase, color: '#10b981' },
    { title: 'Voir les logs', desc: 'Consulter les journaux système', icon: FaBell, color: '#f59e0b' },
    { title: 'Paramètres système', desc: 'Configurer les paramètres globaux', icon: FaCog, color: '#8b5cf6' }
  ];

  return (
    <div style={{ padding: '1rem' }}>
      {/* Header */}
      <div style={{ marginBottom: '2rem' }}>
        <h1 style={{
          fontSize: '2.5rem',
          fontWeight: 'bold',
          color: '#1a202c',
          marginBottom: '0.5rem'
        }}>
          🛡️ Panneau d'Administration
        </h1>
        <p style={{ color: '#6b7280', fontSize: '1.1rem' }}>
          Vue d'ensemble du système et des opérations administratives
        </p>
      </div>

      {/* Stats Grid */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '1.5rem',
        marginBottom: '2rem'
      }}>
        {stats.map((stat, index) => {
          const IconComponent = stat.icon;
          return (
            <div
              key={index}
              style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                padding: '1.5rem',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                border: '1px solid #e5e7eb',
                transition: 'transform 0.2s, box-shadow 0.2s',
                cursor: 'pointer'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div>
                  <p style={{
                    color: '#6b7280',
                    fontSize: '0.9rem',
                    margin: '0 0 0.5rem 0',
                    fontWeight: '500'
                  }}>
                    {stat.title}
                  </p>
                  <p style={{
                    fontSize: '2rem',
                    fontWeight: 'bold',
                    color: '#1a202c',
                    margin: '0 0 0.5rem 0'
                  }}>
                    {stat.value}
                  </p>
                  <p style={{
                    fontSize: '0.8rem',
                    color: stat.changeType === 'positive' ? '#10b981' :
                           stat.changeType === 'negative' ? '#ef4444' : '#6b7280',
                    margin: 0,
                    fontWeight: '500'
                  }}>
                    {stat.change} ce mois
                  </p>
                </div>
                <div style={{
                  width: '60px',
                  height: '60px',
                  borderRadius: '12px',
                  backgroundColor: stat.bgColor,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <IconComponent style={{ fontSize: '1.5rem', color: stat.color }} />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '2rem',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        border: '1px solid #e5e7eb',
        marginBottom: '2rem'
      }}>
        <h2 style={{
          fontSize: '1.5rem',
          fontWeight: 'bold',
          color: '#1a202c',
          marginBottom: '1.5rem'
        }}>
          ⚡ Actions Rapides
        </h2>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '1rem'
        }}>
          {quickActions.map((action, index) => {
            const IconComponent = action.icon;
            return (
              <div
                key={index}
                style={{
                  padding: '1.5rem',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '1rem'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#f9fafb';
                  e.currentTarget.style.borderColor = action.color;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                  e.currentTarget.style.borderColor = '#e5e7eb';
                }}
              >
                <div style={{
                  width: '48px',
                  height: '48px',
                  borderRadius: '8px',
                  backgroundColor: action.color + '20',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <IconComponent style={{ fontSize: '1.2rem', color: action.color }} />
                </div>
                <div>
                  <h3 style={{
                    fontSize: '1rem',
                    fontWeight: '600',
                    color: '#374151',
                    margin: '0 0 0.5rem 0'
                  }}>
                    {action.title}
                  </h3>
                  <p style={{
                    fontSize: '0.9rem',
                    color: '#6b7280',
                    margin: 0
                  }}>
                    {action.desc}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Statistiques des Projets */}
      <div style={{ marginBottom: '2rem' }}>
        <h2 style={{
          fontSize: '1.5rem',
          fontWeight: 'bold',
          color: '#1a202c',
          marginBottom: '1.5rem'
        }}>
          🚀 Statistiques des Projets
        </h2>
        <ProjetStats />
      </div>

      {/* System Status */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '2rem',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        border: '1px solid #e5e7eb'
      }}>
        <h2 style={{
          fontSize: '1.5rem',
          fontWeight: 'bold',
          color: '#1a202c',
          marginBottom: '1.5rem'
        }}>
          📊 État du Système
        </h2>

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
          <div style={{ textAlign: 'center', padding: '1rem' }}>
            <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🟢</div>
            <div style={{ fontWeight: '600', color: '#10b981' }}>Serveur</div>
            <div style={{ fontSize: '0.9rem', color: '#6b7280' }}>En ligne</div>
          </div>
          <div style={{ textAlign: 'center', padding: '1rem' }}>
            <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🟢</div>
            <div style={{ fontWeight: '600', color: '#10b981' }}>Base de données</div>
            <div style={{ fontSize: '0.9rem', color: '#6b7280' }}>Connectée</div>
          </div>
          <div style={{ textAlign: 'center', padding: '1rem' }}>
            <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🟡</div>
            <div style={{ fontWeight: '600', color: '#f59e0b' }}>Sauvegardes</div>
            <div style={{ fontSize: '0.9rem', color: '#6b7280' }}>Il y a 2h</div>
          </div>
          <div style={{ textAlign: 'center', padding: '1rem' }}>
            <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🟢</div>
            <div style={{ fontWeight: '600', color: '#10b981' }}>Sécurité</div>
            <div style={{ fontSize: '0.9rem', color: '#6b7280' }}>Protégé</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminSummary;
