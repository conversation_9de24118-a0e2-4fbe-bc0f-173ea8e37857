import bcrypt from "bcrypt";
import utilisateurModel from "../models/utilisateur.js";
import Jwt from "jsonwebtoken";

const login = async (req, res) => {
    console.log("Request body:", req.body); // Debugging log
    try {
        const { email, password, mot_de_passe } = req.body;
        const passwordToCheck = password || mot_de_passe;
        console.log({ email, password, mot_de_passe, passwordToCheck }); // Debugging log

        if (!email || !passwordToCheck) {
            return res.status(400).json({
                success: false,
                message: "Email et mot de passe requis"
            });
        }

        const utilisateur = await utilisateurModel.findOne({ email });
        if (!utilisateur) {
            return res.status(404).json({ success: false, message: "Utilisateur non trouvé" });
        }

        console.log("Hashed password from database:", utilisateur.mot_de_passe); // Debugging log

        const isMatch = await bcrypt.compare(passwordToCheck, utilisateur.mot_de_passe);
        if (!isMatch) {
            return res.status(400).json({ success: false, message: "Mot de passe incorrect" });
        }

        const token = Jwt.sign(
            {
                _id: utilisateur._id,
                role: utilisateur.role,
                email: utilisateur.email,
                permissions: utilisateur.permissions || []
            },
            process.env.JWT_SECRET,
            { expiresIn: "10d" }
        );

        res.status(200).json({
            success: true,
            token,
            utilisateur: {
                _id: utilisateur._id,
                email: utilisateur.email,
                role: utilisateur.role,
                permissions: utilisateur.permissions || []
            }
        });
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
};

// Middleware to verify the token
const verify= (req,res) => {
    try{
        return res.status(200).json({
            success: true,
            utilisateur: req.utilisateur
    });
    }catch(error){
        console.error('Error dans /verify:', error);
        return res.status(500).json({
            success:     false,
            message: "Error interne du server"
        });
    };
}


export {login, verify};

