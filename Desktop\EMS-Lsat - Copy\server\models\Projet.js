import mongoose from "mongoose";

const projetSchema = new mongoose.Schema({
  nom_projet: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200,
    index: true
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000
  },
  date_debut: {
    type: Date,
    required: true,
    index: true
  },
  date_fin_prevue: {
    type: Date,
    required: true,
    validate: {
      validator: function(value) {
        return value > this.date_debut;
      },
      message: 'La date de fin prévue doit être postérieure à la date de début'
    }
  },
  date_fin_reelle: {
    type: Date,
    validate: {
      validator: function(value) {
        return !value || value >= this.date_debut;
      },
      message: 'La date de fin réelle doit être postérieure à la date de début'
    }
  },
  chef_projet: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Employe",
    required: true,
    index: true
  },
  priorite: {
    type: String,
    enum: ['Faible', 'Moyenne', 'Haute', 'Urgente'],
    default: 'Moyenne',
    required: true,
    index: true
  },
  statut: {
    type: String,
    enum: ['En attente', 'En cours', 'Terminé', 'Annulé', 'En pause'],
    default: 'En attente',
    required: true,
    index: true
  },
  budget_alloue: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  budget_utilise: {
    type: Number,
    min: 0,
    default: 0,
    validate: {
      validator: function(value) {
        return value <= this.budget_alloue;
      },
      message: 'Le budget utilisé ne peut pas dépasser le budget alloué'
    }
  },
  taches: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: "Tache"
  }],
  equipe: [{
    employe: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Employe"
    },
    role: {
      type: String,
      enum: ['Chef de projet', 'Développeur', 'Designer', 'Testeur', 'Analyste', 'Autre'],
      default: 'Autre'
    },
    date_assignation: {
      type: Date,
      default: Date.now
    }
  }],
  client: {
    type: String,
    trim: true
  },
  departement: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Departement"
  },
  progression: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  archive: {
    type: Boolean,
    default: false,
    index: true
  }
}, {
  timestamps: true
});

// Index composé pour optimiser les requêtes
projetSchema.index({ statut: 1, priorite: 1 });
projetSchema.index({ chef_projet: 1, statut: 1 });
projetSchema.index({ date_debut: 1, date_fin_prevue: 1 });
projetSchema.index({ archive: 1, statut: 1 });

// Méthodes d'instance
projetSchema.methods.estEnRetard = function() {
  if (this.statut === 'Terminé' || this.statut === 'Annulé') return false;
  return new Date() > this.date_fin_prevue;
};

projetSchema.methods.joursRestants = function() {
  if (this.statut === 'Terminé' || this.statut === 'Annulé') return 0;

  const maintenant = new Date();
  const dateFin = this.date_fin_reelle || this.date_fin_prevue;
  const diffTime = dateFin - maintenant;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return Math.max(0, diffDays);
};

projetSchema.methods.dureeProjet = function() {
  const dateFin = this.date_fin_reelle || this.date_fin_prevue;
  const diffTime = dateFin - this.date_debut;
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

projetSchema.methods.budgetRestant = function() {
  return Math.max(0, this.budget_alloue - this.budget_utilise);
};

// Middleware pre-save
projetSchema.pre('save', function(next) {
  if (this.statut === 'Terminé' && !this.date_fin_reelle) {
    this.date_fin_reelle = new Date();
  }
  if (this.statut !== 'Terminé' && this.date_fin_reelle) {
    this.date_fin_reelle = undefined;
  }
  next();
});

const Projet = mongoose.model("Projet", projetSchema);
export default Projet;
