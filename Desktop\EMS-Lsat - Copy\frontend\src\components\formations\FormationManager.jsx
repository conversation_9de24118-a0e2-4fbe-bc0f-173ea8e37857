import React, { useState, useEffect } from 'react';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaCalendarAlt,
  FaMapMarkerAlt,
  FaUsers,
  FaGraduationCap,
  FaBuilding
} from 'react-icons/fa';
import FormationModal from './FormationModal';
import CandidaturesViewModal from './CandidaturesViewModal';

const FormationManager = () => {
  const [formations, setFormations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingFormation, setEditingFormation] = useState(null);
  const [showCandidaturesModal, setShowCandidaturesModal] = useState(false);
  const [selectedFormationForCandidatures, setSelectedFormationForCandidatures] = useState(null);

  // Charger les formations au démarrage
  useEffect(() => {
    loadFormations();
  }, []);

  // Fonction pour charger les formations
  const loadFormations = async () => {
    try {
      setLoading(true);
      setError('');

      const token = localStorage.getItem('token');
      if (!token) {
        setError('Token d\'authentification manquant');
        return;
      }

      const response = await fetch('http://localhost:4000/api/formations', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Formations chargées:', data);
        setFormations(data.formations || []);
      } else {
        const errorData = await response.json();
        setError(errorData.message || `Erreur ${response.status}`);
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement:', error);
      setError('Erreur de connexion au serveur');
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour supprimer une formation
  const deleteFormation = async (id) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer cette formation ?')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:4000/api/formations/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        // Recharger la liste après suppression
        await loadFormations();
        alert('Formation supprimée avec succès');
      } else {
        const errorData = await response.json();
        alert(errorData.message || 'Erreur lors de la suppression');
      }
    } catch (error) {
      console.error('❌ Erreur suppression:', error);
      alert('Erreur de connexion');
    }
  };

  // Ouvrir le modal pour créer une nouvelle formation
  const openCreateModal = () => {
    setEditingFormation(null);
    setShowModal(true);
  };

  // Ouvrir le modal pour modifier une formation
  const openEditModal = (formation) => {
    setEditingFormation(formation);
    setShowModal(true);
  };

  // Fermer le modal
  const closeModal = () => {
    setShowModal(false);
    setEditingFormation(null);
  };

  // Callback après sauvegarde réussie
  const onFormationSaved = () => {
    closeModal();
    loadFormations(); // Recharger la liste
  };

  // Ouvrir le modal des candidatures
  const openCandidaturesModal = (formation) => {
    setSelectedFormationForCandidatures(formation);
    setShowCandidaturesModal(true);
  };

  // Fermer le modal des candidatures
  const closeCandidaturesModal = () => {
    setShowCandidaturesModal(false);
    setSelectedFormationForCandidatures(null);
  };

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px',
        fontSize: '1.2rem',
        color: '#6b7280'
      }}>
        <FaGraduationCap style={{ marginRight: '0.5rem' }} />
        Chargement des formations...
      </div>
    );
  }

  return (
    <div style={{ padding: '2rem', backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '2rem'
      }}>
        <div>
          <h1 style={{
            margin: 0,
            fontSize: '2rem',
            fontWeight: '700',
            color: '#1f2937',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            <FaGraduationCap style={{ color: '#3b82f6' }} />
            Gestion des Formations
          </h1>
          <p style={{ margin: '0.5rem 0 0 0', color: '#6b7280' }}>
            Créez et gérez les formations pour vos employés
          </p>
        </div>

        <button
          onClick={openCreateModal}
          style={{
            backgroundColor: '#3b82f6',
            color: 'white',
            border: 'none',
            padding: '0.75rem 1.5rem',
            borderRadius: '8px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            fontSize: '1rem',
            fontWeight: '600',
            boxShadow: '0 2px 4px rgba(59, 130, 246, 0.3)',
            transition: 'all 0.2s'
          }}
          onMouseEnter={(e) => {
            e.target.style.backgroundColor = '#2563eb';
            e.target.style.transform = 'translateY(-1px)';
          }}
          onMouseLeave={(e) => {
            e.target.style.backgroundColor = '#3b82f6';
            e.target.style.transform = 'translateY(0)';
          }}
        >
          <FaPlus />
          Nouvelle Formation
        </button>
      </div>

      {/* Message d'erreur */}
      {error && (
        <div style={{
          backgroundColor: '#fef2f2',
          color: '#dc2626',
          padding: '1rem',
          borderRadius: '8px',
          marginBottom: '2rem',
          border: '1px solid #fecaca',
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          <span>⚠️</span>
          {error}
        </div>
      )}

      {/* Statistiques rapides */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '1rem',
        marginBottom: '2rem'
      }}>
        <div style={{
          backgroundColor: 'white',
          padding: '1.5rem',
          borderRadius: '12px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2rem', fontWeight: '700', color: '#3b82f6' }}>
            {formations.length}
          </div>
          <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>
            Total Formations
          </div>
        </div>

        <div style={{
          backgroundColor: 'white',
          padding: '1.5rem',
          borderRadius: '12px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2rem', fontWeight: '700', color: '#10b981' }}>
            {formations.filter(f => f.statut === 'planifiee').length}
          </div>
          <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>
            Planifiées
          </div>
        </div>

        <div style={{
          backgroundColor: 'white',
          padding: '1.5rem',
          borderRadius: '12px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2rem', fontWeight: '700', color: '#f59e0b' }}>
            {formations.filter(f => f.statut === 'en_cours').length}
          </div>
          <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>
            En Cours
          </div>
        </div>
      </div>

      {/* Liste des formations */}
      {formations.length === 0 ? (
        <div style={{
          textAlign: 'center',
          padding: '4rem 2rem',
          backgroundColor: 'white',
          borderRadius: '12px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <FaGraduationCap style={{
            fontSize: '4rem',
            color: '#d1d5db',
            marginBottom: '1rem'
          }} />
          <h3 style={{
            margin: '0 0 0.5rem 0',
            fontSize: '1.5rem',
            color: '#6b7280'
          }}>
            Aucune formation créée
          </h3>
          <p style={{
            margin: '0 0 2rem 0',
            color: '#9ca3af'
          }}>
            Commencez par créer votre première formation
          </p>
          <button
            onClick={openCreateModal}
            style={{
              backgroundColor: '#3b82f6',
              color: 'white',
              border: 'none',
              padding: '0.75rem 2rem',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '1rem',
              fontWeight: '600'
            }}
          >
            <FaPlus style={{ marginRight: '0.5rem' }} />
            Créer une Formation
          </button>
        </div>
      ) : (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(400px, 1fr))',
          gap: '1.5rem'
        }}>
          {formations.map((formation) => (
            <FormationCard
              key={formation._id}
              formation={formation}
              onEdit={() => openEditModal(formation)}
              onDelete={() => deleteFormation(formation._id)}
              onViewCandidatures={() => openCandidaturesModal(formation)}
            />
          ))}
        </div>
      )}

      {/* Modal de création/modification */}
      {showModal && (
        <FormationModal
          formation={editingFormation}
          onClose={closeModal}
          onSave={onFormationSaved}
        />
      )}

      {/* Modal des candidatures */}
      {showCandidaturesModal && selectedFormationForCandidatures && (
        <CandidaturesViewModal
          formation={selectedFormationForCandidatures}
          onClose={closeCandidaturesModal}
        />
      )}
    </div>
  );
};

// Composant pour afficher une carte de formation
const FormationCard = ({ formation, onEdit, onDelete, onViewCandidatures }) => {
  const getStatutColor = (statut) => {
    switch (statut) {
      case 'planifiee': return '#3b82f6';
      case 'en_cours': return '#f59e0b';
      case 'terminee': return '#10b981';
      case 'annulee': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getStatutLabel = (statut) => {
    switch (statut) {
      case 'planifiee': return 'Planifiée';
      case 'en_cours': return 'En cours';
      case 'terminee': return 'Terminée';
      case 'annulee': return 'Annulée';
      default: return statut;
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      padding: '1.5rem',
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
      border: '1px solid #e5e7eb',
      transition: 'all 0.2s',
      position: 'relative'
    }}>
      {/* Badge statut */}
      <div style={{
        position: 'absolute',
        top: '1rem',
        right: '1rem',
        backgroundColor: getStatutColor(formation.statut),
        color: 'white',
        padding: '0.25rem 0.75rem',
        borderRadius: '12px',
        fontSize: '0.75rem',
        fontWeight: '600'
      }}>
        {getStatutLabel(formation.statut)}
      </div>

      {/* Titre et organisme */}
      <div style={{ marginBottom: '1rem', paddingRight: '5rem' }}>
        <h3 style={{
          margin: '0 0 0.5rem 0',
          fontSize: '1.25rem',
          fontWeight: '600',
          color: '#1f2937',
          lineHeight: '1.4'
        }}>
          {formation.nom_formation}
        </h3>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem',
          color: '#6b7280',
          fontSize: '0.875rem'
        }}>
          <FaBuilding />
          {formation.organisme}
        </div>
      </div>

      {/* Informations principales */}
      <div style={{ marginBottom: '1rem' }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem',
          marginBottom: '0.5rem',
          fontSize: '0.875rem',
          color: '#6b7280'
        }}>
          <FaCalendarAlt />
          <span>
            Du {formatDate(formation.date_debut)} au {formatDate(formation.date_fin)}
          </span>
        </div>

        {formation.lieu && (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            marginBottom: '0.5rem',
            fontSize: '0.875rem',
            color: '#6b7280'
          }}>
            <FaMapMarkerAlt />
            {formation.lieu}
          </div>
        )}

        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem',
          fontSize: '0.875rem',
          color: '#6b7280'
        }}>
          <FaUsers />
          Capacité: {formation.capacite_max} personnes
        </div>
      </div>

      {/* Description */}
      {formation.description && (
        <p style={{
          margin: '0 0 1rem 0',
          fontSize: '0.875rem',
          color: '#6b7280',
          lineHeight: '1.5',
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden'
        }}>
          {formation.description}
        </p>
      )}

      {/* Tags */}
      <div style={{
        display: 'flex',
        gap: '0.5rem',
        marginBottom: '1rem',
        flexWrap: 'wrap'
      }}>
        <span style={{
          backgroundColor: formation.type === 'interne' ? '#dbeafe' : '#fef3c7',
          color: formation.type === 'interne' ? '#1e40af' : '#92400e',
          padding: '0.25rem 0.5rem',
          borderRadius: '6px',
          fontSize: '0.75rem',
          fontWeight: '500'
        }}>
          {formation.type === 'interne' ? 'Interne' : 'Externe'}
        </span>

        <span style={{
          backgroundColor: '#f3e8ff',
          color: '#7c3aed',
          padding: '0.25rem 0.5rem',
          borderRadius: '6px',
          fontSize: '0.75rem',
          fontWeight: '500'
        }}>
          {formation.modalite === 'presentielle' ? 'Présentielle' :
           formation.modalite === 'en_ligne' ? 'En ligne' : 'Mixte'}
        </span>

        {formation.certifiante && (
          <span style={{
            backgroundColor: '#dcfce7',
            color: '#166534',
            padding: '0.25rem 0.5rem',
            borderRadius: '6px',
            fontSize: '0.75rem',
            fontWeight: '500'
          }}>
            Certifiante
          </span>
        )}
      </div>

      {/* Actions */}
      <div style={{
        display: 'flex',
        gap: '0.5rem',
        justifyContent: 'flex-end',
        flexWrap: 'wrap'
      }}>
        <button
          onClick={onViewCandidatures}
          style={{
            backgroundColor: '#3b82f6',
            color: 'white',
            border: 'none',
            padding: '0.5rem 1rem',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '0.875rem',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}
        >
          <FaUsers />
          Candidatures
        </button>

        <button
          onClick={onEdit}
          style={{
            backgroundColor: '#f59e0b',
            color: 'white',
            border: 'none',
            padding: '0.5rem 1rem',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '0.875rem',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}
        >
          <FaEdit />
          Modifier
        </button>

        <button
          onClick={onDelete}
          style={{
            backgroundColor: '#ef4444',
            color: 'white',
            border: 'none',
            padding: '0.5rem 1rem',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '0.875rem',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}
        >
          <FaTrash />
          Supprimer
        </button>
      </div>
    </div>
  );
};

export default FormationManager;
