{"version": 3, "file": "detectPlatform.js", "sourceRoot": "", "sources": ["../../src/detectPlatform.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;AASH,sDAmBC;AA1BD,sDAAyB;AAEzB,oEAA+D;AAE/D;;GAEG;AACH,SAAgB,qBAAqB;IACnC,MAAM,QAAQ,GAAG,iBAAE,CAAC,QAAQ,EAAE,CAAC;IAC/B,MAAM,IAAI,GAAG,iBAAE,CAAC,IAAI,EAAE,CAAC;IACvB,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,QAAQ;YACX,OAAO,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,iCAAe,CAAC,OAAO,CAAC,CAAC,CAAC,iCAAe,CAAC,GAAG,CAAC;QAC1E,KAAK,OAAO;YACV,OAAO,IAAI,KAAK,OAAO;gBACrB,CAAC,CAAC,iCAAe,CAAC,SAAS;gBAC3B,CAAC,CAAC,iCAAe,CAAC,KAAK,CAAC;QAC5B,KAAK,OAAO;YACV,OAAO,IAAI,KAAK,KAAK;gBACnB,4CAA4C;gBAC5C,CAAC,IAAI,KAAK,OAAO,IAAI,WAAW,CAAC,iBAAE,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC/C,CAAC,CAAC,iCAAe,CAAC,KAAK;gBACvB,CAAC,CAAC,iCAAe,CAAC,KAAK,CAAC;QAC5B;YACE,OAAO,SAAS,CAAC;IACrB,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,SAAS,WAAW,CAAC,OAAe;IAClC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACjC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACrB,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAW,EAAE,EAAE,CAAC,CAAC;QAC/C,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAW,EAAE,EAAE,CAAC,CAAC;QAC/C,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAW,EAAE,EAAE,CAAC,CAAC;QAC/C,OAAO,CACL,KAAK,GAAG,EAAE;YACV,CAAC,KAAK,KAAK,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC;YAC3B,CAAC,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC,CAChD,CAAC;IACJ,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC"}