import { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { useAuth } from '../../context/authContext';
import {
  FaHome,
  FaUsers,
  FaUserShield,
  FaBuilding,
  FaEnvelope,
  FaChevronRight,
  FaCrown,
  FaCogs,
  FaFileContract,
  FaProjectDiagram,
  FaArchive,
  FaBars,
  FaTimes,
  FaSignOutAlt
} from 'react-icons/fa';

const AdminSidebar = () => {
  const { utilisateur } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const menuItems = [
    {
      path: '/AdminPage',
      icon: FaHome,
      label: 'Tableau de bord',
      exact: true
    },
    {
      path: '/AdminPage/utilisateurs',
      icon: FaUserShield,
      label: 'Gestion Utilisateurs'
    },
    {
      path: '/AdminPage/employes',
      icon: FaUsers,
      label: 'Employés'
    },
    {
      path: '/AdminPage/departements',
      icon: FaBuilding,
      label: 'Départements & Services'
    },
    {
      path: '/AdminPage/contrats',
      icon: FaFileContract,
      label: 'Contrats'
    },
    {
      path: '/AdminPage/projets',
      icon: FaProjectDiagram,
      label: 'Projets'
    },
    {
      path: '/AdminPage/archives',
      icon: FaArchive,
      label: 'Archives'
    },
    {
      path: '/AdminPage/messages',
      icon: FaEnvelope,
      label: 'Messages'
    }
  ];

  return (
    <div
      style={{
        width: sidebarOpen ? '280px' : '80px',
        backgroundColor: 'white',
        boxShadow: '2px 0 10px rgba(0,0,0,0.1)',
        transition: 'width 0.3s ease',
        position: 'fixed',
        height: '100vh',
        zIndex: 1000,
        overflow: 'hidden',
        fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {/* Header du sidebar */}
      <div style={{
        padding: '1.5rem',
        borderBottom: '1px solid #e5e7eb',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        {sidebarOpen && (
          <div>
            <h2 style={{
              margin: '0 0 0.25rem 0',
              fontSize: '1.2rem',
              color: '#374151',
              fontWeight: '700',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              <FaCrown style={{ color: '#7c3aed', fontSize: '1.1rem' }} />
              Admin Panel
            </h2>
            <p style={{
              margin: 0,
              fontSize: '0.875rem',
              color: '#6b7280'
            }}>
              {utilisateur?.email || 'Administrateur'}
            </p>
          </div>
        )}
        <button
          onClick={() => setSidebarOpen(!sidebarOpen)}
          style={{
            backgroundColor: 'transparent',
            border: 'none',
            color: '#6b7280',
            cursor: 'pointer',
            padding: '0.5rem',
            borderRadius: '4px',
            fontSize: '1.2rem'
          }}
        >
          {sidebarOpen ? <FaTimes /> : <FaBars />}
        </button>
      </div>

      {/* Navigation */}
      <nav style={{
        padding: '1rem 0',
        flex: 1,
        overflowY: 'auto',
        overflowX: 'hidden'
      }}>
        {menuItems.map((item, index) => {
          const Icon = item.icon;
          return (
            <NavLink
              key={index}
              to={item.path}
              end={item.exact}
              style={({ isActive }) => ({
                width: '100%',
                padding: sidebarOpen ? '0.75rem 1.5rem' : '0.75rem',
                border: 'none',
                backgroundColor: isActive ? '#7c3aed' : 'transparent',
                color: isActive ? 'white' : '#6b7280',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: sidebarOpen ? '0.75rem' : '0',
                justifyContent: sidebarOpen ? 'flex-start' : 'center',
                fontSize: '0.875rem',
                fontWeight: '500',
                transition: 'all 0.2s ease',
                borderLeft: isActive ? '4px solid #7c3aed' : '4px solid transparent',
                textDecoration: 'none'
              })}
              onMouseEnter={(e) => {
                const isActive = e.target.style.backgroundColor === 'rgb(124, 58, 237)';
                if (!isActive) {
                  e.target.style.backgroundColor = '#f3f4f6';
                  e.target.style.color = '#374151';
                }
              }}
              onMouseLeave={(e) => {
                const isActive = e.target.style.backgroundColor === 'rgb(124, 58, 237)';
                if (!isActive) {
                  e.target.style.backgroundColor = 'transparent';
                  e.target.style.color = '#6b7280';
                }
              }}
            >
              <Icon style={{ fontSize: '1.1rem' }} />
              {sidebarOpen && item.label}
            </NavLink>
          );
        })}
      </nav>

      {/* Footer avec informations utilisateur */}
      {sidebarOpen && (
        <div style={{
          padding: '1rem 1.5rem',
          borderTop: '1px solid #e5e7eb',
          backgroundColor: '#f9fafb'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.75rem'
          }}>
            <div style={{
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              backgroundColor: '#7c3aed',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: '600',
              fontSize: '0.875rem'
            }}>
              {utilisateur?.email?.charAt(0).toUpperCase() || 'A'}
            </div>
            <div style={{ flex: 1, minWidth: 0 }}>
              <div style={{
                fontSize: '0.875rem',
                fontWeight: '600',
                color: '#374151',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}>
                {utilisateur?.email || 'Administrateur'}
              </div>
              <div style={{
                fontSize: '0.75rem',
                color: '#6b7280',
                textTransform: 'uppercase',
                letterSpacing: '0.5px',
                fontWeight: '500'
              }}>
                👑 Administrateur
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminSidebar;
