import mongoose from 'mongoose';

const typeCongeSchema = new mongoose.Schema({
  nom_type: {
    type: String,
    required: true,
    trim: true,
    unique: true
  },
  description: {
    type: String,
    trim: true
  },
  duree_max: {
    type: Number,
    required: true,
    min: 1
  },
  unite: {
    type: String,
    enum: ['jours', 'heures'],
    default: 'jours'
  },
  actif: {
    type: Boolean,
    default: true
  },
  couleur: {
    type: String,
    default: '#3B82F6'
  },
  necessite_justificatif: {
    type: Boolean,
    default: false
  },
  delai_minimum: {
    type: Number,
    default: 0
  },
  cumul_autorise: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  collection: 'typeconges'
});

// Index pour optimiser les requêtes
typeCongeSchema.index({ nom_type: 1 });
typeCongeSchema.index({ actif: 1 });

export default mongoose.model('TypeConge', typeCongeSchema);
