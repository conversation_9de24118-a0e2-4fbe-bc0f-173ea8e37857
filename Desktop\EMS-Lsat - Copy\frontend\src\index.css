/* Import des styles globaux */
@import './styles/variables.css';
@import './styles/components.css';

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light;
  color: var(--text-primary);
  background-color: var(--bg-secondary);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

#root {
  min-height: 100vh;
}

/* Styles pour les liens */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-dark);
}

/* Styles pour les listes */
ul, ol {
  margin: 0;
  padding: 0;
}

/* Styles pour les images */
img {
  max-width: 100%;
  height: auto;
}

/* Styles pour les tableaux */
table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: var(--spacing-sm);
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

th {
  font-weight: var(--font-weight-semibold);
  background-color: var(--bg-secondary);
}

/* Styles pour le scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: var(--border-radius);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-dark);
}

/* Styles responsives */
@media (max-width: 768px) {
  .container {
    padding: var(--spacing-sm);
  }

  .modal-dialog {
    margin: var(--spacing-sm);
  }

  .btn {
    padding: var(--spacing-sm);
    font-size: var(--font-size-sm);
  }
}