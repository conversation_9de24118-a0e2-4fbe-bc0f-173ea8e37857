import React from 'react';
import { FaUser, FaCalendarAlt, FaTasks, FaFileContract, FaChartLine, FaClock } from 'react-icons/fa';

const EmployeeDashboard = ({ employeeData }) => {
  if (!employeeData) {
    return (
      <div style={{ textAlign: 'center', padding: '2rem' }}>
        <p>Chargement des données...</p>
      </div>
    );
  }

  const stats = [
    {
      title: 'Profil',
      value: 'Complet',
      icon: FaUser,
      color: '#10b981',
      description: 'Informations personnelles'
    },
    {
      title: 'Congés',
      value: '25 jours',
      icon: FaCalendarAlt,
      color: '#3b82f6',
      description: 'Solde disponible'
    },
    {
      title: 'Projets',
      value: '3 actifs',
      icon: FaTasks,
      color: '#8b5cf6',
      description: 'Projets en cours'
    },
    {
      title: 'Contrat',
      value: 'CDI',
      icon: FaFileContract,
      color: '#f59e0b',
      description: 'Type de contrat'
    }
  ];

  return (
    <div style={{ padding: '1rem' }}>
      {/* En-tête de bienvenue */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '2rem',
        marginBottom: '2rem',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '1.5rem' }}>
          <div style={{
            width: '80px',
            height: '80px',
            borderRadius: '50%',
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            overflow: 'hidden',
            border: '3px solid rgba(255, 255, 255, 0.3)'
          }}>
            {employeeData.photo ? (
              <img
                src={`http://localhost:4000${employeeData.photo}`}
                alt={`${employeeData.prenom} ${employeeData.nom}`}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover'
                }}
              />
            ) : (
              <FaUser style={{ fontSize: '2rem', color: 'rgba(255, 255, 255, 0.8)' }} />
            )}
          </div>
          <div>
            <h1 style={{
              margin: '0 0 0.5rem 0',
              fontSize: '2rem',
              fontWeight: '700'
            }}>
              Bonjour, {employeeData.prenom} {employeeData.nom}
            </h1>
            <p style={{
              margin: '0 0 0.5rem 0',
              fontSize: '1.1rem',
              opacity: 0.9
            }}>
              {employeeData.poste || 'Poste non défini'}
            </p>
            <p style={{
              margin: 0,
              fontSize: '0.9rem',
              opacity: 0.8
            }}>
              {employeeData.departement?.nom_departement || 'Département non assigné'}
            </p>
          </div>
        </div>
      </div>

      {/* Statistiques rapides */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '1.5rem',
        marginBottom: '2rem'
      }}>
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div
              key={index}
              style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                padding: '1.5rem',
                boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                border: '1px solid #e5e7eb',
                transition: 'transform 0.2s ease',
                cursor: 'pointer'
              }}
              onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
              onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
            >
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '1rem',
                marginBottom: '1rem'
              }}>
                <div style={{
                  width: '50px',
                  height: '50px',
                  borderRadius: '50%',
                  backgroundColor: stat.color,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white'
                }}>
                  <Icon style={{ fontSize: '1.2rem' }} />
                </div>
                <div>
                  <h3 style={{
                    margin: 0,
                    fontSize: '1.1rem',
                    color: '#374151',
                    fontWeight: '600'
                  }}>
                    {stat.title}
                  </h3>
                  <p style={{
                    margin: 0,
                    fontSize: '0.875rem',
                    color: '#6b7280'
                  }}>
                    {stat.description}
                  </p>
                </div>
              </div>
              <div style={{
                fontSize: '1.5rem',
                fontWeight: '700',
                color: stat.color
              }}>
                {stat.value}
              </div>
            </div>
          );
        })}
      </div>

      {/* Actions rapides */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '1.5rem',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        border: '1px solid #e5e7eb'
      }}>
        <h2 style={{
          margin: '0 0 1.5rem 0',
          fontSize: '1.3rem',
          color: '#374151',
          fontWeight: '600'
        }}>
          Actions rapides
        </h2>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '1rem'
        }}>
          <button style={{
            backgroundColor: '#3b82f6',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            padding: '1rem',
            fontSize: '0.875rem',
            fontWeight: '500',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            justifyContent: 'center',
            transition: 'background-color 0.2s'
          }}>
            <FaCalendarAlt />
            Demander un congé
          </button>
          <button style={{
            backgroundColor: '#10b981',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            padding: '1rem',
            fontSize: '0.875rem',
            fontWeight: '500',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            justifyContent: 'center',
            transition: 'background-color 0.2s'
          }}>
            <FaFileContract />
            Générer attestation
          </button>
          <button style={{
            backgroundColor: '#8b5cf6',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            padding: '1rem',
            fontSize: '0.875rem',
            fontWeight: '500',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            justifyContent: 'center',
            transition: 'background-color 0.2s'
          }}>
            <FaTasks />
            Voir mes tâches
          </button>
        </div>
      </div>
    </div>
  );
};

export default EmployeeDashboard;
