import React, { useState, useEffect } from 'react';
import { FaTimes, FaSave, FaCalendarAlt, FaMapMarkerAlt, FaUsers, FaGraduationCap } from 'react-icons/fa';

const FormationModal = ({ formation, onClose, onSave }) => {
  const [formData, setFormData] = useState({
    nom_formation: '',
    organisme: '',
    date_debut: '',
    date_fin: '',
    description: '',
    type: 'externe',
    certifiante: false,
    modalite: 'presentielle',
    lieu: '',
    lien_connexion: '',
    capacite_max: 20,
    statut: 'planifiee'
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Initialiser le formulaire avec les données de la formation (si modification)
  useEffect(() => {
    if (formation) {
      setFormData({
        nom_formation: formation.nom_formation || '',
        organisme: formation.organisme || '',
        date_debut: formation.date_debut ? formation.date_debut.split('T')[0] : '',
        date_fin: formation.date_fin ? formation.date_fin.split('T')[0] : '',
        description: formation.description || '',
        type: formation.type || 'externe',
        certifiante: formation.certifiante || false,
        modalite: formation.modalite || 'presentielle',
        lieu: formation.lieu || '',
        lien_connexion: formation.lien_connexion || '',
        capacite_max: formation.capacite_max || 20,
        statut: formation.statut || 'planifiee'
      });
    }
  }, [formation]);

  // Gérer les changements dans le formulaire
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Valider le formulaire
  const validateForm = () => {
    const errors = [];

    if (!formData.nom_formation.trim()) {
      errors.push('Le nom de la formation est requis');
    }

    if (!formData.organisme.trim()) {
      errors.push('L\'organisme est requis');
    }

    if (!formData.date_debut) {
      errors.push('La date de début est requise');
    }

    if (!formData.date_fin) {
      errors.push('La date de fin est requise');
    }

    if (formData.date_debut && formData.date_fin && formData.date_debut > formData.date_fin) {
      errors.push('La date de début doit être antérieure à la date de fin');
    }

    if (!formData.description.trim()) {
      errors.push('La description est requise');
    }

    if (formData.capacite_max < 1) {
      errors.push('La capacité doit être d\'au moins 1 personne');
    }

    if (formData.modalite === 'presentielle' && !formData.lieu.trim()) {
      errors.push('Le lieu est requis pour une formation présentielle');
    }

    if (formData.modalite === 'en_ligne' && !formData.lien_connexion.trim()) {
      errors.push('Le lien de connexion est requis pour une formation en ligne');
    }

    return errors;
  };

  // Soumettre le formulaire
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Valider le formulaire
    const validationErrors = validateForm();
    if (validationErrors.length > 0) {
      setError(validationErrors.join(', '));
      return;
    }

    setLoading(true);
    setError('');

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setError('Token d\'authentification manquant');
        return;
      }

      const url = formation 
        ? `http://localhost:4000/api/formations/${formation._id}`
        : 'http://localhost:4000/api/formations';
      
      const method = formation ? 'PUT' : 'POST';

      console.log('📤 Envoi des données:', formData);

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();
      console.log('📥 Réponse serveur:', data);

      if (response.ok && data.success) {
        // Succès - fermer le modal et recharger la liste
        onSave();
        alert(formation ? 'Formation modifiée avec succès' : 'Formation créée avec succès');
      } else {
        setError(data.message || `Erreur ${response.status}`);
      }
    } catch (error) {
      console.error('❌ Erreur lors de la sauvegarde:', error);
      setError('Erreur de connexion au serveur');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '1rem'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        width: '100%',
        maxWidth: '800px',
        maxHeight: '90vh',
        overflow: 'auto',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '1.5rem',
          borderBottom: '1px solid #e5e7eb'
        }}>
          <h2 style={{
            margin: 0,
            fontSize: '1.5rem',
            fontWeight: '600',
            color: '#1f2937',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            <FaGraduationCap style={{ color: '#3b82f6' }} />
            {formation ? 'Modifier la Formation' : 'Nouvelle Formation'}
          </h2>
          
          <button
            onClick={onClose}
            style={{
              backgroundColor: 'transparent',
              border: 'none',
              fontSize: '1.5rem',
              color: '#6b7280',
              cursor: 'pointer',
              padding: '0.5rem'
            }}
          >
            <FaTimes />
          </button>
        </div>

        {/* Formulaire */}
        <form onSubmit={handleSubmit} style={{ padding: '1.5rem' }}>
          {/* Message d'erreur */}
          {error && (
            <div style={{
              backgroundColor: '#fef2f2',
              color: '#dc2626',
              padding: '1rem',
              borderRadius: '8px',
              marginBottom: '1.5rem',
              border: '1px solid #fecaca'
            }}>
              {error}
            </div>
          )}

          {/* Grille de champs */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '1.5rem',
            marginBottom: '1.5rem'
          }}>
            {/* Nom de la formation */}
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151'
              }}>
                Nom de la formation *
              </label>
              <input
                type="text"
                name="nom_formation"
                value={formData.nom_formation}
                onChange={handleChange}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '1rem'
                }}
                placeholder="Ex: Formation React Avancé"
              />
            </div>

            {/* Organisme */}
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151'
              }}>
                Organisme *
              </label>
              <input
                type="text"
                name="organisme"
                value={formData.organisme}
                onChange={handleChange}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '1rem'
                }}
                placeholder="Ex: Centre de Formation ABC"
              />
            </div>

            {/* Date de début */}
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151'
              }}>
                <FaCalendarAlt style={{ marginRight: '0.5rem' }} />
                Date de début *
              </label>
              <input
                type="date"
                name="date_debut"
                value={formData.date_debut}
                onChange={handleChange}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '1rem'
                }}
              />
            </div>

            {/* Date de fin */}
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151'
              }}>
                <FaCalendarAlt style={{ marginRight: '0.5rem' }} />
                Date de fin *
              </label>
              <input
                type="date"
                name="date_fin"
                value={formData.date_fin}
                onChange={handleChange}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '1rem'
                }}
              />
            </div>

            {/* Type */}
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151'
              }}>
                Type de formation
              </label>
              <select
                name="type"
                value={formData.type}
                onChange={handleChange}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '1rem'
                }}
              >
                <option value="externe">Externe</option>
                <option value="interne">Interne</option>
              </select>
            </div>

            {/* Modalité */}
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151'
              }}>
                Modalité
              </label>
              <select
                name="modalite"
                value={formData.modalite}
                onChange={handleChange}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '1rem'
                }}
              >
                <option value="presentielle">Présentielle</option>
                <option value="en_ligne">En ligne</option>
                <option value="mixte">Mixte</option>
              </select>
            </div>

            {/* Capacité */}
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151'
              }}>
                <FaUsers style={{ marginRight: '0.5rem' }} />
                Capacité maximale
              </label>
              <input
                type="number"
                name="capacite_max"
                value={formData.capacite_max}
                onChange={handleChange}
                min="1"
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '1rem'
                }}
              />
            </div>

            {/* Statut */}
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151'
              }}>
                Statut
              </label>
              <select
                name="statut"
                value={formData.statut}
                onChange={handleChange}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '1rem'
                }}
              >
                <option value="planifiee">Planifiée</option>
                <option value="en_cours">En cours</option>
                <option value="terminee">Terminée</option>
                <option value="annulee">Annulée</option>
              </select>
            </div>
          </div>

          {/* Lieu (conditionnel) */}
          {(formData.modalite === 'presentielle' || formData.modalite === 'mixte') && (
            <div style={{ marginBottom: '1.5rem' }}>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151'
              }}>
                <FaMapMarkerAlt style={{ marginRight: '0.5rem' }} />
                Lieu {formData.modalite === 'presentielle' ? '*' : ''}
              </label>
              <input
                type="text"
                name="lieu"
                value={formData.lieu}
                onChange={handleChange}
                required={formData.modalite === 'presentielle'}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '1rem'
                }}
                placeholder="Ex: Salle de conférence A, 123 Rue de la Formation"
              />
            </div>
          )}

          {/* Lien de connexion (conditionnel) */}
          {(formData.modalite === 'en_ligne' || formData.modalite === 'mixte') && (
            <div style={{ marginBottom: '1.5rem' }}>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151'
              }}>
                Lien de connexion {formData.modalite === 'en_ligne' ? '*' : ''}
              </label>
              <input
                type="url"
                name="lien_connexion"
                value={formData.lien_connexion}
                onChange={handleChange}
                required={formData.modalite === 'en_ligne'}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '1rem'
                }}
                placeholder="https://zoom.us/j/123456789"
              />
            </div>
          )}

          {/* Description */}
          <div style={{ marginBottom: '1.5rem' }}>
            <label style={{
              display: 'block',
              marginBottom: '0.5rem',
              fontWeight: '600',
              color: '#374151'
            }}>
              Description *
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              required
              rows={4}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '1rem',
                resize: 'vertical'
              }}
              placeholder="Décrivez le contenu et les objectifs de la formation..."
            />
          </div>

          {/* Checkbox certifiante */}
          <div style={{ marginBottom: '2rem' }}>
            <label style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              cursor: 'pointer'
            }}>
              <input
                type="checkbox"
                name="certifiante"
                checked={formData.certifiante}
                onChange={handleChange}
                style={{
                  width: '1.25rem',
                  height: '1.25rem'
                }}
              />
              <span style={{ fontWeight: '600', color: '#374151' }}>
                Formation certifiante
              </span>
            </label>
          </div>

          {/* Boutons d'action */}
          <div style={{
            display: 'flex',
            gap: '1rem',
            justifyContent: 'flex-end',
            paddingTop: '1rem',
            borderTop: '1px solid #e5e7eb'
          }}>
            <button
              type="button"
              onClick={onClose}
              disabled={loading}
              style={{
                padding: '0.75rem 1.5rem',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                backgroundColor: 'white',
                color: '#374151',
                cursor: loading ? 'not-allowed' : 'pointer',
                fontSize: '1rem',
                opacity: loading ? 0.7 : 1
              }}
            >
              Annuler
            </button>
            
            <button
              type="submit"
              disabled={loading}
              style={{
                padding: '0.75rem 1.5rem',
                border: 'none',
                borderRadius: '8px',
                backgroundColor: '#3b82f6',
                color: 'white',
                cursor: loading ? 'not-allowed' : 'pointer',
                fontSize: '1rem',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                opacity: loading ? 0.7 : 1
              }}
            >
              <FaSave />
              {loading ? 'Sauvegarde...' : (formation ? 'Modifier' : 'Créer')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default FormationModal;
