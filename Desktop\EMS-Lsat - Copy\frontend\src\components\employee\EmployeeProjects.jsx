import React, { useState, useEffect } from 'react';
import { FaTasks, FaProjectDiagram, FaClock, FaCheckCircle, FaExclamationTriangle, FaUser, FaCalendarAlt } from 'react-icons/fa';

const EmployeeProjects = ({ employeeData }) => {
  const [projects, setProjects] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('projects');

  useEffect(() => {
    loadProjectsAndTasks();
  }, []);

  const loadProjectsAndTasks = async () => {
    setLoading(true);
    try {
      // TODO: Implémenter l'API pour récupérer les projets et tâches de l'employé
      // const projectsResponse = await projectService.getMyProjects();
      // const tasksResponse = await taskService.getMyTasks();
      // setProjects(projectsResponse.data);
      // setTasks(tasksResponse.data);
      
      // Données de test pour l'instant
      setProjects([
        {
          _id: '1',
          nom_projet: 'Refonte du site web',
          description: 'Modernisation complète du site web de l\'entreprise',
          statut: 'En cours',
          priorite: 'Haute',
          date_debut: '2024-01-15',
          date_fin_prevue: '2024-06-30',
          progression: 65,
          chef_projet: { prenom: 'Marie', nom: 'Dupont' }
        },
        {
          _id: '2',
          nom_projet: 'Application mobile',
          description: 'Développement d\'une application mobile pour les clients',
          statut: 'En cours',
          priorite: 'Moyenne',
          date_debut: '2024-03-01',
          date_fin_prevue: '2024-08-15',
          progression: 30,
          chef_projet: { prenom: 'Jean', nom: 'Martin' }
        }
      ]);

      setTasks([
        {
          _id: '1',
          titre: 'Conception de la page d\'accueil',
          description: 'Créer le design de la nouvelle page d\'accueil',
          statut: 'En cours',
          priorite: 'Haute',
          date_echeance: '2024-06-15',
          projet: { nom_projet: 'Refonte du site web' }
        },
        {
          _id: '2',
          titre: 'Tests unitaires',
          description: 'Écrire les tests unitaires pour les nouvelles fonctionnalités',
          statut: 'À faire',
          priorite: 'Moyenne',
          date_echeance: '2024-06-20',
          projet: { nom_projet: 'Refonte du site web' }
        },
        {
          _id: '3',
          titre: 'Interface utilisateur mobile',
          description: 'Développer l\'interface utilisateur de l\'application mobile',
          statut: 'Terminé',
          priorite: 'Haute',
          date_echeance: '2024-05-30',
          projet: { nom_projet: 'Application mobile' }
        }
      ]);
    } catch (error) {
      console.error('Erreur lors du chargement des projets et tâches:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'En cours': return { bg: '#dbeafe', color: '#1e40af' };
      case 'Terminé': return { bg: '#dcfce7', color: '#166534' };
      case 'À faire': return { bg: '#fef3c7', color: '#92400e' };
      case 'En pause': return { bg: '#f3f4f6', color: '#374151' };
      default: return { bg: '#f3f4f6', color: '#374151' };
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'Haute': return { bg: '#fef2f2', color: '#dc2626' };
      case 'Moyenne': return { bg: '#fef3c7', color: '#d97706' };
      case 'Faible': return { bg: '#f0fdf4', color: '#16a34a' };
      default: return { bg: '#f3f4f6', color: '#374151' };
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'En cours': return <FaClock />;
      case 'Terminé': return <FaCheckCircle />;
      case 'À faire': return <FaExclamationTriangle />;
      default: return <FaClock />;
    }
  };

  const ProjectCard = ({ project }) => {
    const statusStyle = getStatusColor(project.statut);
    const priorityStyle = getPriorityColor(project.priorite);

    return (
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '1.5rem',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        border: '1px solid #e5e7eb',
        transition: 'transform 0.2s ease',
        cursor: 'pointer'
      }}
      onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
      onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start', marginBottom: '1rem' }}>
          <h3 style={{
            margin: 0,
            fontSize: '1.2rem',
            color: '#374151',
            fontWeight: '600'
          }}>
            {project.nom_projet}
          </h3>
          <div style={{ display: 'flex', gap: '0.5rem' }}>
            <span style={{
              backgroundColor: statusStyle.bg,
              color: statusStyle.color,
              padding: '0.25rem 0.75rem',
              borderRadius: '20px',
              fontSize: '0.75rem',
              fontWeight: '500'
            }}>
              {project.statut}
            </span>
            <span style={{
              backgroundColor: priorityStyle.bg,
              color: priorityStyle.color,
              padding: '0.25rem 0.75rem',
              borderRadius: '20px',
              fontSize: '0.75rem',
              fontWeight: '500'
            }}>
              {project.priorite}
            </span>
          </div>
        </div>

        <p style={{
          margin: '0 0 1rem 0',
          fontSize: '0.875rem',
          color: '#6b7280',
          lineHeight: '1.5'
        }}>
          {project.description}
        </p>

        <div style={{ marginBottom: '1rem' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.5rem' }}>
            <span style={{ fontSize: '0.875rem', color: '#374151', fontWeight: '500' }}>
              Progression
            </span>
            <span style={{ fontSize: '0.875rem', color: '#374151', fontWeight: '600' }}>
              {project.progression}%
            </span>
          </div>
          <div style={{
            width: '100%',
            height: '8px',
            backgroundColor: '#e5e7eb',
            borderRadius: '4px',
            overflow: 'hidden'
          }}>
            <div style={{
              width: `${project.progression}%`,
              height: '100%',
              backgroundColor: project.progression >= 70 ? '#10b981' : project.progression >= 40 ? '#f59e0b' : '#ef4444',
              transition: 'width 0.3s ease'
            }} />
          </div>
        </div>

        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', fontSize: '0.875rem', color: '#6b7280' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
            <FaUser />
            {project.chef_projet?.prenom} {project.chef_projet?.nom}
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
            <FaCalendarAlt />
            {formatDate(project.date_fin_prevue)}
          </div>
        </div>
      </div>
    );
  };

  const TaskCard = ({ task }) => {
    const statusStyle = getStatusColor(task.statut);
    const priorityStyle = getPriorityColor(task.priorite);

    return (
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        padding: '1rem',
        border: '1px solid #e5e7eb',
        transition: 'transform 0.2s ease'
      }}
      onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-1px)'}
      onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start', marginBottom: '0.75rem' }}>
          <h4 style={{
            margin: 0,
            fontSize: '1rem',
            color: '#374151',
            fontWeight: '600'
          }}>
            {task.titre}
          </h4>
          <div style={{ display: 'flex', gap: '0.5rem' }}>
            <span style={{
              backgroundColor: statusStyle.bg,
              color: statusStyle.color,
              padding: '0.25rem 0.5rem',
              borderRadius: '12px',
              fontSize: '0.75rem',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '0.25rem'
            }}>
              {getStatusIcon(task.statut)}
              {task.statut}
            </span>
            <span style={{
              backgroundColor: priorityStyle.bg,
              color: priorityStyle.color,
              padding: '0.25rem 0.5rem',
              borderRadius: '12px',
              fontSize: '0.75rem',
              fontWeight: '500'
            }}>
              {task.priorite}
            </span>
          </div>
        </div>

        <p style={{
          margin: '0 0 0.75rem 0',
          fontSize: '0.875rem',
          color: '#6b7280',
          lineHeight: '1.4'
        }}>
          {task.description}
        </p>

        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', fontSize: '0.75rem', color: '#6b7280' }}>
          <span>
            📁 {task.projet?.nom_projet}
          </span>
          <span>
            📅 {formatDate(task.date_echeance)}
          </span>
        </div>
      </div>
    );
  };

  return (
    <div style={{ padding: '1rem' }}>
      {/* En-tête avec onglets */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '1.5rem',
        marginBottom: '2rem',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        border: '1px solid #e5e7eb'
      }}>
        <h1 style={{
          margin: '0 0 1.5rem 0',
          fontSize: '1.5rem',
          color: '#374151',
          fontWeight: '600',
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          <FaProjectDiagram style={{ color: '#8b5cf6' }} />
          Mes projets et tâches
        </h1>

        <div style={{ display: 'flex', gap: '1rem' }}>
          <button
            onClick={() => setActiveTab('projects')}
            style={{
              padding: '0.75rem 1.5rem',
              border: 'none',
              borderRadius: '8px',
              backgroundColor: activeTab === 'projects' ? '#8b5cf6' : '#f3f4f6',
              color: activeTab === 'projects' ? 'white' : '#374151',
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
          >
            <FaProjectDiagram />
            Projets ({projects.length})
          </button>
          <button
            onClick={() => setActiveTab('tasks')}
            style={{
              padding: '0.75rem 1.5rem',
              border: 'none',
              borderRadius: '8px',
              backgroundColor: activeTab === 'tasks' ? '#8b5cf6' : '#f3f4f6',
              color: activeTab === 'tasks' ? 'white' : '#374151',
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
          >
            <FaTasks />
            Tâches ({tasks.length})
          </button>
        </div>
      </div>

      {/* Contenu des onglets */}
      {loading ? (
        <div style={{ textAlign: 'center', padding: '2rem' }}>
          <p>Chargement...</p>
        </div>
      ) : (
        <>
          {activeTab === 'projects' && (
            <div>
              {projects.length > 0 ? (
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
                  gap: '1.5rem'
                }}>
                  {projects.map((project) => (
                    <ProjectCard key={project._id} project={project} />
                  ))}
                </div>
              ) : (
                <div style={{
                  backgroundColor: 'white',
                  borderRadius: '12px',
                  padding: '3rem',
                  textAlign: 'center',
                  boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                  border: '1px solid #e5e7eb'
                }}>
                  <FaProjectDiagram style={{ fontSize: '3rem', color: '#d1d5db', marginBottom: '1rem' }} />
                  <h3 style={{ margin: '0 0 0.5rem 0', color: '#374151' }}>Aucun projet assigné</h3>
                  <p style={{ margin: 0, color: '#6b7280' }}>Vous n'êtes actuellement assigné à aucun projet</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'tasks' && (
            <div>
              {tasks.length > 0 ? (
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
                  gap: '1rem'
                }}>
                  {tasks.map((task) => (
                    <TaskCard key={task._id} task={task} />
                  ))}
                </div>
              ) : (
                <div style={{
                  backgroundColor: 'white',
                  borderRadius: '12px',
                  padding: '3rem',
                  textAlign: 'center',
                  boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                  border: '1px solid #e5e7eb'
                }}>
                  <FaTasks style={{ fontSize: '3rem', color: '#d1d5db', marginBottom: '1rem' }} />
                  <h3 style={{ margin: '0 0 0.5rem 0', color: '#374151' }}>Aucune tâche assignée</h3>
                  <p style={{ margin: 0, color: '#6b7280' }}>Vous n'avez actuellement aucune tâche assignée</p>
                </div>
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default EmployeeProjects;
