export default {
  // Utiliser ES modules
  preset: 'es-modules',
  
  // Environnement de test
  testEnvironment: 'node',
  
  // Extensions de fichiers à traiter
  moduleFileExtensions: ['js', 'json'],
  
  // Transformation des fichiers
  transform: {
    '^.+\\.js$': 'babel-jest'
  },
  
  // Patterns pour trouver les fichiers de test
  testMatch: [
    '**/tests/**/*.test.js',
    '**/__tests__/**/*.js',
    '**/?(*.)+(spec|test).js'
  ],
  
  // Répertoires à ignorer
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/'
  ],
  
  // Configuration de la couverture de code
  collectCoverage: true,
  collectCoverageFrom: [
    'models/**/*.js',
    'controllers/**/*.js',
    'routes/**/*.js',
    'middleware/**/*.js',
    '!**/node_modules/**',
    '!**/tests/**',
    '!**/coverage/**'
  ],
  
  // Répertoire de sortie pour la couverture
  coverageDirectory: 'coverage',
  
  // Formats de rapport de couverture
  coverageReporters: [
    'text',
    'lcov',
    'html'
  ],
  
  // Seuils de couverture
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },
  
  // Configuration de l'environnement de test
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  
  // Variables d'environnement pour les tests
  testEnvironmentOptions: {
    NODE_ENV: 'test'
  },
  
  // Timeout pour les tests
  testTimeout: 30000,
  
  // Affichage détaillé
  verbose: true,
  
  // Forcer la sortie en cas d'erreur
  forceExit: true,
  
  // Nettoyer les mocks après chaque test
  clearMocks: true,
  
  // Restaurer les mocks après chaque test
  restoreMocks: true
};
