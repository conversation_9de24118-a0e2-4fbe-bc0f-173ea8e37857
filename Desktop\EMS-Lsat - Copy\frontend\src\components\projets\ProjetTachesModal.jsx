import React, { useState } from 'react';
import { FaTimes, FaTasks, FaPlus, FaEdit, FaTrash, FaUser, Fa<PERSON>lock, FaCalendarAlt } from 'react-icons/fa';
import { useTachesByProjet } from '../../hooks/useTaches';
import { useEmployees } from '../../hooks/useEmployees';
import { tacheService } from '../../services/tacheService';
import AddTacheModal from './AddTacheModal';
import EditTacheModal from './EditTacheModal';

const ProjetTachesModal = ({ projet, onClose }) => {
  const { taches, loading, error, createTache, updateTache, deleteTache } = useTachesByProjet(projet._id);
  const { employees } = useEmployees();
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedTache, setSelectedTache] = useState(null);

  const handleAddTache = () => {
    setShowAddModal(true);
  };

  const handleEditTache = (tache) => {
    setSelectedTache(tache);
    setShowEditModal(true);
  };

  const handleDeleteTache = async (tache) => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer la tâche "${tache.nom_tache}" ?`)) {
      try {
        await deleteTache(tache._id);
      } catch (error) {
        alert('Erreur lors de la suppression : ' + error.message);
      }
    }
  };

  const handleModalClose = () => {
    setShowAddModal(false);
    setShowEditModal(false);
    setSelectedTache(null);
  };

  const getProgressionColor = (progression) => {
    if (progression >= 80) return '#10b981';
    if (progression >= 50) return '#f59e0b';
    if (progression >= 25) return '#3b82f6';
    return '#ef4444';
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '1rem'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        width: '100%',
        maxWidth: '1000px',
        maxHeight: '90vh',
        overflow: 'hidden',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* En-tête */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '1.5rem',
          borderBottom: '1px solid #e5e7eb',
          backgroundColor: '#f9fafb'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.75rem'
          }}>
            <div style={{
              width: '40px',
              height: '40px',
              borderRadius: '8px',
              backgroundColor: '#3b82f6',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white'
            }}>
              <FaTasks />
            </div>
            <div>
              <h2 style={{
                margin: 0,
                fontSize: '1.5rem',
                fontWeight: '600',
                color: '#1f2937'
              }}>
                Tâches du projet
              </h2>
              <p style={{
                margin: '0.25rem 0 0 0',
                fontSize: '0.875rem',
                color: '#6b7280'
              }}>
                {projet?.nom_projet}
              </p>
            </div>
          </div>
          <div style={{ display: 'flex', gap: '0.5rem' }}>
            <button
              onClick={handleAddTache}
              style={{
                backgroundColor: '#10b981',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                padding: '0.5rem 1rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                transition: 'background-color 0.2s'
              }}
              onMouseOver={(e) => e.target.style.backgroundColor = '#059669'}
              onMouseOut={(e) => e.target.style.backgroundColor = '#10b981'}
            >
              <FaPlus />
              Nouvelle tâche
            </button>
            <button
              onClick={onClose}
              style={{
                backgroundColor: 'transparent',
                border: 'none',
                color: '#6b7280',
                cursor: 'pointer',
                padding: '0.5rem',
                borderRadius: '6px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                transition: 'background-color 0.2s'
              }}
              onMouseOver={(e) => e.target.style.backgroundColor = '#f3f4f6'}
              onMouseOut={(e) => e.target.style.backgroundColor = 'transparent'}
            >
              <FaTimes size={20} />
            </button>
          </div>
        </div>

        {/* Contenu */}
        <div style={{ 
          flex: 1, 
          overflow: 'auto', 
          padding: '1.5rem' 
        }}>
          {loading ? (
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '200px',
              fontSize: '1.1rem',
              color: '#6b7280'
            }}>
              Chargement des tâches...
            </div>
          ) : error ? (
            <div style={{
              backgroundColor: '#fef2f2',
              border: '1px solid #fecaca',
              borderRadius: '8px',
              padding: '1rem',
              color: '#dc2626'
            }}>
              <strong>Erreur:</strong> {error}
            </div>
          ) : taches.length === 0 ? (
            <div style={{
              textAlign: 'center',
              padding: '3rem',
              color: '#6b7280'
            }}>
              <FaTasks style={{ fontSize: '3rem', marginBottom: '1rem', color: '#d1d5db' }} />
              <h3 style={{ margin: '0 0 0.5rem 0' }}>Aucune tâche</h3>
              <p style={{ margin: 0 }}>Ce projet n'a pas encore de tâches assignées</p>
              <button
                onClick={handleAddTache}
                style={{
                  backgroundColor: '#10b981',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '0.75rem 1.5rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer',
                  marginTop: '1rem',
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}
              >
                <FaPlus />
                Créer la première tâche
              </button>
            </div>
          ) : (
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(400px, 1fr))',
              gap: '1rem'
            }}>
              {taches.map((tache) => (
                <div
                  key={tache._id}
                  style={{
                    backgroundColor: 'white',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    padding: '1rem',
                    transition: 'box-shadow 0.2s',
                    position: 'relative'
                  }}
                  onMouseOver={(e) => e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)'}
                  onMouseOut={(e) => e.currentTarget.style.boxShadow = 'none'}
                >
                  {/* En-tête de la tâche */}
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    marginBottom: '0.75rem'
                  }}>
                    <div style={{ flex: 1 }}>
                      <h4 style={{
                        margin: 0,
                        fontSize: '1rem',
                        fontWeight: '600',
                        color: '#1f2937',
                        lineHeight: '1.4'
                      }}>
                        {tache.nom_tache}
                      </h4>
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        marginTop: '0.25rem'
                      }}>
                        <FaUser style={{ color: '#6b7280', fontSize: '0.75rem' }} />
                        <span style={{ color: '#6b7280', fontSize: '0.75rem' }}>
                          {tache.employe_assigne?.prenom} {tache.employe_assigne?.nom}
                        </span>
                      </div>
                    </div>
                    <div style={{ display: 'flex', gap: '0.25rem' }}>
                      <button
                        onClick={() => handleEditTache(tache)}
                        style={{
                          backgroundColor: '#f59e0b',
                          color: 'white',
                          border: 'none',
                          borderRadius: '4px',
                          padding: '0.25rem',
                          cursor: 'pointer',
                          fontSize: '0.75rem'
                        }}
                        title="Modifier"
                      >
                        <FaEdit />
                      </button>
                      <button
                        onClick={() => handleDeleteTache(tache)}
                        style={{
                          backgroundColor: '#ef4444',
                          color: 'white',
                          border: 'none',
                          borderRadius: '4px',
                          padding: '0.25rem',
                          cursor: 'pointer',
                          fontSize: '0.75rem'
                        }}
                        title="Supprimer"
                      >
                        <FaTrash />
                      </button>
                    </div>
                  </div>

                  {/* Description */}
                  <p style={{
                    margin: '0 0 0.75rem 0',
                    color: '#6b7280',
                    fontSize: '0.875rem',
                    lineHeight: '1.4'
                  }}>
                    {tache.description}
                  </p>

                  {/* Dates */}
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    marginBottom: '0.75rem',
                    fontSize: '0.75rem',
                    color: '#6b7280'
                  }}>
                    <FaCalendarAlt />
                    <span>
                      Du {tacheService.formatDate(tache.date_debut)} au {tacheService.formatDate(tache.date_fin_prevue)}
                    </span>
                  </div>

                  {/* Temps */}
                  {(tache.temps_estime > 0 || tache.temps_passe > 0) && (
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      marginBottom: '0.75rem',
                      fontSize: '0.75rem',
                      color: '#6b7280'
                    }}>
                      <FaClock />
                      <span>
                        {tache.temps_passe > 0 && `${tacheService.formatTemps(tache.temps_passe)} passé`}
                        {tache.temps_passe > 0 && tache.temps_estime > 0 && ' / '}
                        {tache.temps_estime > 0 && `${tacheService.formatTemps(tache.temps_estime)} estimé`}
                      </span>
                    </div>
                  )}

                  {/* Progression */}
                  <div style={{ marginBottom: '0.75rem' }}>
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: '0.25rem'
                    }}>
                      <span style={{ fontSize: '0.75rem', color: '#6b7280' }}>
                        Progression
                      </span>
                      <span style={{ fontSize: '0.75rem', fontWeight: '600', color: '#1f2937' }}>
                        {tache.progression || 0}%
                      </span>
                    </div>
                    <div style={{
                      width: '100%',
                      height: '4px',
                      backgroundColor: '#e5e7eb',
                      borderRadius: '2px',
                      overflow: 'hidden'
                    }}>
                      <div style={{
                        width: `${tache.progression || 0}%`,
                        height: '100%',
                        backgroundColor: getProgressionColor(tache.progression || 0),
                        transition: 'width 0.3s ease'
                      }} />
                    </div>
                  </div>

                  {/* Statut et priorité */}
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}>
                    <div style={{ display: 'flex', gap: '0.25rem' }}>
                      <span style={{
                        padding: '0.125rem 0.5rem',
                        borderRadius: '8px',
                        backgroundColor: `${tacheService.getStatutColor(tache.statut)}20`,
                        color: tacheService.getStatutColor(tache.statut),
                        fontSize: '0.625rem',
                        fontWeight: '500'
                      }}>
                        {tacheService.getStatutIcon(tache.statut)} {tache.statut}
                      </span>
                      <span style={{
                        padding: '0.125rem 0.5rem',
                        borderRadius: '8px',
                        backgroundColor: `${tacheService.getPrioriteColor(tache.priorite)}20`,
                        color: tacheService.getPrioriteColor(tache.priorite),
                        fontSize: '0.625rem',
                        fontWeight: '500'
                      }}>
                        {tache.priorite}
                      </span>
                    </div>
                    
                    {tacheService.isTacheEnRetard(tache) && (
                      <span style={{
                        padding: '0.125rem 0.375rem',
                        borderRadius: '8px',
                        backgroundColor: '#fef3c7',
                        color: '#d97706',
                        fontSize: '0.625rem',
                        fontWeight: '500'
                      }}>
                        ⚠️ En retard
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Pied de page avec statistiques */}
        {taches.length > 0 && (
          <div style={{
            borderTop: '1px solid #e5e7eb',
            padding: '1rem 1.5rem',
            backgroundColor: '#f9fafb',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            fontSize: '0.875rem',
            color: '#6b7280'
          }}>
            <div>
              Total: {taches.length} tâche{taches.length > 1 ? 's' : ''}
            </div>
            <div style={{ display: 'flex', gap: '1rem' }}>
              <span>
                ✅ Terminées: {taches.filter(t => t.statut === 'Terminée').length}
              </span>
              <span>
                🚀 En cours: {taches.filter(t => t.statut === 'En cours').length}
              </span>
              <span>
                ⚠️ En retard: {taches.filter(t => tacheService.isTacheEnRetard(t)).length}
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      {showAddModal && (
        <AddTacheModal
          projet={projet}
          onClose={handleModalClose}
          onCreate={createTache}
        />
      )}

      {showEditModal && selectedTache && (
        <EditTacheModal
          tache={selectedTache}
          onClose={handleModalClose}
          onUpdate={updateTache}
        />
      )}
    </div>
  );
};

export default ProjetTachesModal;
