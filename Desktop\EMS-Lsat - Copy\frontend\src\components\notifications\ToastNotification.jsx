import React, { useState, useEffect } from 'react';
import {
  FaCheck,
  FaTimes,
  FaTimesCircle,
  FaGraduationCap,
  FaInfoCircle,
  FaExclamationTriangle
} from 'react-icons/fa';

/**
 * Composant Toast pour afficher les notifications de manière élégante
 * Supporte différents types : success, error, warning, info
 * Auto-dismiss après un délai configurable
 * @param {Object} notification - Objet notification à afficher
 * @param {Function} onClose - Fonction appelée à la fermeture
 * @param {number} duration - Durée d'affichage en ms (défaut: 5000)
 * @param {string} position - Position du toast (défaut: 'top-right')
 */
const ToastNotification = ({
  notification,
  onClose,
  duration = 5000,
  position = 'top-right'
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  // Animation d'entrée
  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  // Auto-dismiss après la durée spécifiée
  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        handleClose();
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [duration]);

  /**
   * Gestion de la fermeture avec animation
   */
  const handleClose = () => {
    setIsLeaving(true);
    setTimeout(() => {
      if (onClose) {
        onClose(notification.id);
      }
    }, 300); // Durée de l'animation de sortie
  };

  /**
   * Obtenir la configuration selon le type de notification
   */
  const getToastConfig = () => {
    const { statut, type } = notification;

    if (type === 'candidature_response') {
      if (statut === 'approuvee') {
        return {
          icon: FaCheck,
          bgColor: '#10b981',
          borderColor: '#059669',
          iconBg: '#065f46',
          textColor: '#ffffff'
        };
      } else if (statut === 'refusee') {
        return {
          icon: FaTimesCircle,
          bgColor: '#ef4444',
          borderColor: '#dc2626',
          iconBg: '#991b1b',
          textColor: '#ffffff'
        };
      }
    }

    // Types génériques
    switch (type) {
      case 'success':
        return {
          icon: FaCheck,
          bgColor: '#10b981',
          borderColor: '#059669',
          iconBg: '#065f46',
          textColor: '#ffffff'
        };
      case 'error':
        return {
          icon: FaTimes,
          bgColor: '#ef4444',
          borderColor: '#dc2626',
          iconBg: '#991b1b',
          textColor: '#ffffff'
        };
      case 'warning':
        return {
          icon: FaExclamationTriangle,
          bgColor: '#f59e0b',
          borderColor: '#d97706',
          iconBg: '#92400e',
          textColor: '#ffffff'
        };
      case 'info':
      default:
        return {
          icon: FaInfoCircle,
          bgColor: '#3b82f6',
          borderColor: '#2563eb',
          iconBg: '#1d4ed8',
          textColor: '#ffffff'
        };
    }
  };

  /**
   * Obtenir les styles de position
   */
  const getPositionStyles = () => {
    const baseStyles = {
      position: 'fixed',
      zIndex: 9999,
      transform: isVisible && !isLeaving
        ? 'translateX(0) scale(1)'
        : position.includes('right')
          ? 'translateX(100%) scale(0.95)'
          : 'translateX(-100%) scale(0.95)',
      opacity: isVisible && !isLeaving ? 1 : 0,
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
    };

    switch (position) {
      case 'top-right':
        return { ...baseStyles, top: '1rem', right: '1rem' };
      case 'top-left':
        return { ...baseStyles, top: '1rem', left: '1rem' };
      case 'bottom-right':
        return { ...baseStyles, bottom: '1rem', right: '1rem' };
      case 'bottom-left':
        return { ...baseStyles, bottom: '1rem', left: '1rem' };
      case 'top-center':
        return {
          ...baseStyles,
          top: '1rem',
          left: '50%',
          transform: `translateX(-50%) ${isVisible && !isLeaving ? 'translateY(0) scale(1)' : 'translateY(-100%) scale(0.95)'}`
        };
      default:
        return { ...baseStyles, top: '1rem', right: '1rem' };
    }
  };

  const config = getToastConfig();
  const IconComponent = config.icon;

  return (
    <div
      style={{
        ...getPositionStyles(),
        minWidth: '320px',
        maxWidth: '400px',
        backgroundColor: config.bgColor,
        border: `2px solid ${config.borderColor}`,
        borderRadius: '12px',
        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)',
        overflow: 'hidden'
      }}
    >
      {/* Barre de progression pour l'auto-dismiss */}
      {duration > 0 && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            height: '3px',
            backgroundColor: 'rgba(255, 255, 255, 0.3)',
            animation: `toast-progress ${duration}ms linear forwards`
          }}
        />
      )}

      <div style={{
        display: 'flex',
        alignItems: 'flex-start',
        padding: '1rem',
        gap: '0.75rem'
      }}>
        {/* Icône */}
        <div style={{
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          backgroundColor: config.iconBg,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0
        }}>
          <IconComponent style={{
            color: 'white',
            fontSize: '1.1rem'
          }} />
        </div>

        {/* Contenu */}
        <div style={{ flex: 1, minWidth: 0 }}>
          <h4 style={{
            margin: '0 0 0.25rem 0',
            fontSize: '0.95rem',
            fontWeight: '600',
            color: config.textColor,
            lineHeight: '1.3'
          }}>
            {notification.titre}
          </h4>

          <p style={{
            margin: '0 0 0.5rem 0',
            fontSize: '0.875rem',
            color: 'rgba(255, 255, 255, 0.9)',
            lineHeight: '1.4'
          }}>
            {notification.message}
          </p>

          {/* Message RH si disponible */}
          {notification.message_rh && (
            <div style={{
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              padding: '0.5rem',
              borderRadius: '6px',
              marginTop: '0.5rem'
            }}>
              <p style={{
                margin: 0,
                fontSize: '0.8rem',
                color: 'rgba(255, 255, 255, 0.8)',
                fontStyle: 'italic',
                lineHeight: '1.3'
              }}>
                💬 "{notification.message_rh}"
              </p>
            </div>
          )}

          {/* Timestamp */}
          <div style={{
            fontSize: '0.75rem',
            color: 'rgba(255, 255, 255, 0.7)',
            marginTop: '0.5rem'
          }}>
            {new Date(notification.date).toLocaleString('fr-FR', {
              day: '2-digit',
              month: '2-digit',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </div>
        </div>

        {/* Bouton fermer */}
        <button
          onClick={handleClose}
          style={{
            backgroundColor: 'transparent',
            border: 'none',
            color: 'rgba(255, 255, 255, 0.8)',
            cursor: 'pointer',
            padding: '0.25rem',
            borderRadius: '4px',
            fontSize: '1rem',
            flexShrink: 0,
            transition: 'color 0.2s'
          }}
          onMouseEnter={(e) => {
            e.target.style.color = 'white';
            e.target.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
          }}
          onMouseLeave={(e) => {
            e.target.style.color = 'rgba(255, 255, 255, 0.8)';
            e.target.style.backgroundColor = 'transparent';
          }}
        >
          <FaTimes />
        </button>
      </div>

      {/* Styles CSS pour l'animation de la barre de progression */}
      <style>{`
        @keyframes toast-progress {
          from {
            width: 100%;
          }
          to {
            width: 0%;
          }
        }
      `}</style>
    </div>
  );
};

export default ToastNotification;
