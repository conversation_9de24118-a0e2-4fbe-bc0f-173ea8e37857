const mongoose = require('mongoose');

const evaluationPerformanceSchema = new mongoose.Schema({
  employe: { type: mongoose.Schema.Types.ObjectId, ref: "employes" },
  evaluateur: { type: mongoose.Schema.Types.ObjectId, ref: "utilisateurs" },
  date: Date,
  objectifs: [String],
  feedback: String,
  score: Number
}, { timestamps: true });

module.exports = mongoose.model("EvaluationPerformance", evaluationPerformanceSchema);
