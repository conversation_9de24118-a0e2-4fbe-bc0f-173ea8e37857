import React, { useEffect, useRef } from 'react';
import { useAuth } from '../../context/authContext';
import { useNotifications } from '../../contexts/NotificationContext';
import useEmployeeNotifications from '../../hooks/useEmployeeNotifications';

/**
 * Composant gestionnaire des notifications employé
 * Se charge automatiquement d'afficher les notifications toast
 * quand l'employé se connecte ou quand de nouvelles notifications arrivent
 * Doit être placé dans l'application pour les employés
 */
const EmployeeNotificationManager = () => {
  const { utilisateur } = useAuth();
  const { showMultipleCandidatureNotifications } = useNotifications();
  const {
    notifications,
    hasNotifications,
    markAsRead,
    canViewNotifications
  } = useEmployeeNotifications();

  // Référence pour éviter les notifications en double au montage
  const hasShownLoginNotifications = useRef(false);
  const lastNotificationCount = useRef(0);

  /**
   * Afficher les notifications au login de l'employé
   */
  useEffect(() => {
    // Vérifier que l'utilisateur est un employé et qu'on n'a pas déjà affiché les notifications
    if (!canViewNotifications || hasShownLoginNotifications.current) {
      return;
    }

    // Attendre un peu que les données se chargent
    const timer = setTimeout(() => {
      if (hasNotifications && notifications.length > 0) {
        console.log('🔔 Affichage des notifications de connexion employé');
        
        // Afficher les notifications avec un délai progressif
        showMultipleCandidatureNotifications(notifications);
        
        // Marquer comme affiché pour éviter les doublons
        hasShownLoginNotifications.current = true;
        lastNotificationCount.current = notifications.length;
      }
    }, 2000); // Délai de 2 secondes après le login

    return () => clearTimeout(timer);
  }, [canViewNotifications, hasNotifications, notifications, showMultipleCandidatureNotifications]);

  /**
   * Détecter les nouvelles notifications en temps réel
   */
  useEffect(() => {
    // Ignorer le premier chargement
    if (!hasShownLoginNotifications.current) {
      return;
    }

    // Détecter si de nouvelles notifications sont arrivées
    if (notifications.length > lastNotificationCount.current) {
      const newNotifications = notifications.slice(lastNotificationCount.current);
      
      console.log(`🔔 ${newNotifications.length} nouvelles notifications détectées`);
      
      // Afficher seulement les nouvelles notifications
      showMultipleCandidatureNotifications(newNotifications);
      
      // Mettre à jour le compteur
      lastNotificationCount.current = notifications.length;
    }
  }, [notifications, showMultipleCandidatureNotifications]);

  /**
   * Marquer automatiquement les notifications comme vues après un délai
   */
  useEffect(() => {
    if (!hasNotifications) return;

    // Marquer comme vues après 10 secondes d'affichage
    const timer = setTimeout(() => {
      notifications.forEach(notification => {
        if (notification.candidature_id) {
          markAsRead(notification.candidature_id);
        }
      });
    }, 10000);

    return () => clearTimeout(timer);
  }, [notifications, hasNotifications, markAsRead]);

  /**
   * Reset des références quand l'utilisateur change
   */
  useEffect(() => {
    hasShownLoginNotifications.current = false;
    lastNotificationCount.current = 0;
  }, [utilisateur?.id]);

  // Ce composant ne rend rien visuellement
  return null;
};

export default EmployeeNotificationManager;
