import React, { useState, useEffect } from 'react';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaUser,
  FaBuilding,
  FaUsers,
  FaSearch,
  FaEye,
  FaUserTie
} from 'react-icons/fa';
import axios from 'axios';
import { AddDepartmentModal, EditDepartmentModal } from './DepartmentModals';
import { AddServiceModal, EditServiceModal } from './ServiceModals';

const DepartmentServiceDashboard = () => {
  const [departments, setDepartments] = useState([]);
  const [services, setServices] = useState([]);
  const [selectedDepartment, setSelectedDepartment] = useState(null);
  const [loading, setLoading] = useState(true);
  const [servicesLoading, setServicesLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  // États pour les modals
  const [showAddDeptModal, setShowAddDeptModal] = useState(false);
  const [showEditDeptModal, setShowEditDeptModal] = useState(false);
  const [showAddServiceModal, setShowAddServiceModal] = useState(false);
  const [showEditServiceModal, setShowEditServiceModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  // Charger les départements au montage
  useEffect(() => {
    fetchDepartments();
  }, []);

  // Charger les services quand un département est sélectionné
  useEffect(() => {
    if (selectedDepartment) {
      fetchServices(selectedDepartment._id);
    }
  }, [selectedDepartment]);

  const fetchDepartments = async () => {
    try {
      console.log('🔄 Rechargement des départements...');
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await axios.get('http://localhost:4000/api/departments', {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        console.log('✅ Départements rechargés:', response.data.data);
        setDepartments(response.data.data);
        // Sélectionner le premier département par défaut
        if (response.data.data.length > 0 && !selectedDepartment) {
          setSelectedDepartment(response.data.data[0]);
        }
      }
    } catch (error) {
      console.error('Erreur lors du chargement des départements:', error);
      setError('Erreur lors du chargement des départements');
    } finally {
      setLoading(false);
    }
  };

  const fetchServices = async (departmentId) => {
    try {
      setServicesLoading(true);
      const token = localStorage.getItem('token');
      const response = await axios.get(`http://localhost:4000/api/services?departement=${departmentId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        setServices(response.data.data.services || []);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des services:', error);
      setError('Erreur lors du chargement des services');
    } finally {
      setServicesLoading(false);
    }
  };

  const handleDepartmentSelect = (department) => {
    setSelectedDepartment(department);
    setServices([]); // Réinitialiser les services
  };

  const handleAddDepartment = () => {
    console.log('🔄 handleAddDepartment appelé');
    setShowAddDeptModal(true);
  };

  const handleEditDepartment = (department) => {
    setSelectedItem(department);
    setShowEditDeptModal(true);
  };

  const handleDeleteDepartment = async (department) => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer le département "${department.nom_departement}" ?`)) {
      try {
        const token = localStorage.getItem('token');
        await axios.delete(`http://localhost:4000/api/departments/${department._id}`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        fetchDepartments();
        if (selectedDepartment?._id === department._id) {
          setSelectedDepartment(null);
          setServices([]);
        }
        setError(''); // Effacer les erreurs précédentes
      } catch (error) {
        console.error('Erreur lors de la suppression:', error);

        // Afficher le message d'erreur du serveur
        if (error.response?.data?.message) {
          setError(error.response.data.message);
        } else {
          setError('Erreur lors de la suppression du département');
        }
      }
    }
  };

  const handleAddService = () => {
    console.log('🔄 handleAddService appelé');
    if (!selectedDepartment) {
      setError('Veuillez sélectionner un département d\'abord');
      return;
    }
    setShowAddServiceModal(true);
  };

  const handleEditService = (service) => {
    setSelectedItem(service);
    setShowEditServiceModal(true);
  };

  const handleDeleteService = async (service) => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer le service "${service.nom_service}" ?`)) {
      try {
        const token = localStorage.getItem('token');
        await axios.delete(`http://localhost:4000/api/services/${service._id}`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        fetchServices(selectedDepartment._id);
        setError(''); // Effacer les erreurs précédentes
      } catch (error) {
        console.error('Erreur lors de la suppression:', error);

        // Afficher le message d'erreur du serveur
        if (error.response?.data?.message) {
          setError(error.response.data.message);
        } else {
          setError('Erreur lors de la suppression du service');
        }
      }
    }
  };

  const handleModalClose = async () => {
    setShowAddDeptModal(false);
    setShowEditDeptModal(false);
    setShowAddServiceModal(false);
    setShowEditServiceModal(false);
    setSelectedItem(null);
    setError('');

    // Recharger les données
    await fetchDepartments();

    // Si un département était sélectionné, le mettre à jour avec les nouvelles données
    if (selectedDepartment) {
      // Trouver le département mis à jour dans la nouvelle liste
      try {
        const token = localStorage.getItem('token');
        const response = await axios.get(`http://localhost:4000/api/departments/${selectedDepartment._id}`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (response.data.success) {
          console.log('🔄 Mise à jour du département sélectionné:', response.data.data);
          setSelectedDepartment(response.data.data);
        }
      } catch (error) {
        console.error('Erreur lors de la mise à jour du département sélectionné:', error);
      }

      fetchServices(selectedDepartment._id);
    }
  };

  // Filtrer les départements selon la recherche
  const filteredDepartments = departments.filter(dept =>
    dept.nom_departement.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (dept.description && dept.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px',
        fontSize: '1.1rem',
        color: '#6b7280'
      }}>
        Chargement des départements...
      </div>
    );
  }

  return (
    <div style={{ padding: '1.5rem' }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '2rem'
      }}>
        <div>
          <h1 style={{
            fontSize: '1.875rem',
            fontWeight: 'bold',
            color: '#1f2937',
            margin: '0 0 0.5rem 0'
          }}>
            Gestion Départements & Services
          </h1>
          <p style={{
            color: '#6b7280',
            margin: 0
          }}>
            Gérez les départements et leurs services associés
          </p>
        </div>

        <div
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('🔄 Div cliqué - handleAddDepartment');
            handleAddDepartment();
          }}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            backgroundColor: '#3b82f6',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            padding: '0.75rem 1.5rem',
            fontSize: '0.875rem',
            fontWeight: '500',
            cursor: 'pointer',
            transition: 'background-color 0.2s'
          }}
          onMouseEnter={(e) => e.target.style.backgroundColor = '#2563eb'}
          onMouseLeave={(e) => e.target.style.backgroundColor = '#3b82f6'}
        >
          <FaPlus />
          Nouveau Département
        </div>
      </div>

      {/* Message d'erreur */}
      {error && (
        <div style={{
          backgroundColor: '#fee2e2',
          border: '1px solid #fecaca',
          color: '#dc2626',
          padding: '0.75rem',
          borderRadius: '6px',
          marginBottom: '1rem'
        }}>
          {error}
        </div>
      )}

      {/* Layout principal : deux colonnes */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: '2rem',
        minHeight: '600px'
      }}>
        {/* Colonne gauche : Départements */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e5e7eb',
          overflow: 'hidden'
        }}>
          {/* Header départements */}
          <div style={{
            padding: '1.5rem',
            borderBottom: '1px solid #e5e7eb',
            backgroundColor: '#f9fafb'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '1rem'
            }}>
              <h2 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                color: '#1f2937',
                margin: 0,
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}>
                <FaBuilding style={{ color: '#3b82f6' }} />
                Départements ({filteredDepartments.length})
              </h2>
            </div>

            {/* Barre de recherche */}
            <div style={{ position: 'relative' }}>
              <FaSearch style={{
                position: 'absolute',
                left: '0.75rem',
                top: '50%',
                transform: 'translateY(-50%)',
                color: '#9ca3af',
                fontSize: '0.875rem'
              }} />
              <input
                type="text"
                placeholder="Rechercher un département..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.75rem 0.75rem 0.75rem 2.5rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '0.875rem',
                  outline: 'none',
                  transition: 'border-color 0.2s'
                }}
                onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
              />
            </div>
          </div>

          {/* Liste des départements */}
          <div style={{
            maxHeight: '500px',
            overflowY: 'auto',
            padding: '1rem'
          }}>
            {filteredDepartments.length === 0 ? (
              <div style={{
                textAlign: 'center',
                padding: '2rem',
                color: '#6b7280'
              }}>
                Aucun département trouvé
              </div>
            ) : (
              filteredDepartments.map((department) => (
                <DepartmentCard
                  key={department._id}
                  department={department}
                  isSelected={selectedDepartment?._id === department._id}
                  onSelect={() => handleDepartmentSelect(department)}
                  onEdit={() => handleEditDepartment(department)}
                  onDelete={() => handleDeleteDepartment(department)}
                />
              ))
            )}
          </div>
        </div>

        {/* Colonne droite : Services */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          border: '1px solid #e5e7eb',
          overflow: 'hidden'
        }}>
          {/* Header services */}
          <div style={{
            padding: '1.5rem',
            borderBottom: '1px solid #e5e7eb',
            backgroundColor: '#f9fafb'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <h2 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                color: '#1f2937',
                margin: 0,
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}>
                <FaUsers style={{ color: '#10b981' }} />
                Services {selectedDepartment && `- ${selectedDepartment.nom_departement}`}
                {services.length > 0 && ` (${services.length})`}
              </h2>

              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleAddService();
                }}
                disabled={!selectedDepartment}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  backgroundColor: selectedDepartment ? '#10b981' : '#9ca3af',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  padding: '0.5rem 1rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: selectedDepartment ? 'pointer' : 'not-allowed',
                  transition: 'background-color 0.2s'
                }}
                onMouseEnter={(e) => {
                  if (selectedDepartment) e.target.style.backgroundColor = '#059669';
                }}
                onMouseLeave={(e) => {
                  if (selectedDepartment) e.target.style.backgroundColor = '#10b981';
                }}
              >
                <FaPlus />
                Ajouter Service
              </button>
            </div>
          </div>

          {/* Liste des services */}
          <div style={{
            maxHeight: '500px',
            overflowY: 'auto',
            padding: '1rem'
          }}>
            {!selectedDepartment ? (
              <div style={{
                textAlign: 'center',
                padding: '3rem',
                color: '#6b7280'
              }}>
                <FaBuilding style={{ fontSize: '3rem', marginBottom: '1rem', color: '#d1d5db' }} />
                <p style={{ margin: 0, fontSize: '1.1rem' }}>
                  Sélectionnez un département pour voir ses services
                </p>
              </div>
            ) : servicesLoading ? (
              <div style={{
                textAlign: 'center',
                padding: '2rem',
                color: '#6b7280'
              }}>
                Chargement des services...
              </div>
            ) : services.length === 0 ? (
              <div style={{
                textAlign: 'center',
                padding: '3rem',
                color: '#6b7280'
              }}>
                <FaUsers style={{ fontSize: '3rem', marginBottom: '1rem', color: '#d1d5db' }} />
                <p style={{ margin: '0 0 1rem 0', fontSize: '1.1rem' }}>
                  Aucun service dans ce département
                </p>
                <button
                  type="button"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleAddService();
                  }}
                  style={{
                    backgroundColor: '#10b981',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    padding: '0.5rem 1rem',
                    fontSize: '0.875rem',
                    cursor: 'pointer'
                  }}
                >
                  Créer le premier service
                </button>
              </div>
            ) : (
              services.map((service) => (
                <ServiceCard
                  key={service._id}
                  service={service}
                  onEdit={() => handleEditService(service)}
                  onDelete={() => handleDeleteService(service)}
                />
              ))
            )}
          </div>
        </div>
      </div>

      {/* Modals */}
      {showAddDeptModal && (
        <AddDepartmentModal onClose={handleModalClose} />
      )}

      {showEditDeptModal && selectedItem && (
        <EditDepartmentModal
          department={selectedItem}
          onClose={handleModalClose}
        />
      )}

      {showAddServiceModal && (
        <AddServiceModal
          selectedDepartment={selectedDepartment}
          onClose={handleModalClose}
        />
      )}

      {showEditServiceModal && selectedItem && (
        <EditServiceModal
          service={selectedItem}
          onClose={handleModalClose}
        />
      )}
    </div>
  );
};

// Composant pour une carte de département
const DepartmentCard = ({ department, isSelected, onSelect, onEdit, onDelete }) => {
  return (
    <div
      onClick={onSelect}
      style={{
        backgroundColor: isSelected ? '#eff6ff' : 'white',
        border: `2px solid ${isSelected ? '#3b82f6' : '#e5e7eb'}`,
        borderRadius: '8px',
        padding: '1rem',
        marginBottom: '0.75rem',
        cursor: 'pointer',
        transition: 'all 0.2s',
        position: 'relative'
      }}
      onMouseEnter={(e) => {
        if (!isSelected) {
          e.currentTarget.style.borderColor = '#d1d5db';
          e.currentTarget.style.backgroundColor = '#f9fafb';
        }
      }}
      onMouseLeave={(e) => {
        if (!isSelected) {
          e.currentTarget.style.borderColor = '#e5e7eb';
          e.currentTarget.style.backgroundColor = 'white';
        }
      }}
    >
      {/* Badge de sélection */}
      {isSelected && (
        <div style={{
          position: 'absolute',
          top: '0.5rem',
          right: '0.5rem',
          backgroundColor: '#3b82f6',
          color: 'white',
          borderRadius: '50%',
          width: '1.5rem',
          height: '1.5rem',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '0.75rem'
        }}>
          ✓
        </div>
      )}

      {/* Header avec nom et statut */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: '0.75rem'
      }}>
        <h3 style={{
          fontSize: '1rem',
          fontWeight: '600',
          color: '#1f2937',
          margin: 0,
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          <FaBuilding style={{ color: '#6b7280', fontSize: '0.875rem' }} />
          {department.nom_departement}
        </h3>

        <span style={{
          padding: '0.25rem 0.5rem',
          borderRadius: '12px',
          fontSize: '0.75rem',
          fontWeight: '500',
          backgroundColor: department.is_active ? '#dcfce7' : '#fee2e2',
          color: department.is_active ? '#166534' : '#dc2626'
        }}>
          {department.is_active ? 'Actif' : 'Inactif'}
        </span>
      </div>

      {/* Responsable */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '0.5rem',
        marginBottom: '0.5rem'
      }}>
        <FaUserTie style={{ color: '#6b7280', fontSize: '0.75rem' }} />
        <span style={{
          fontSize: '0.875rem',
          color: '#6b7280'
        }}>
          {department.responsable ?
            `${department.responsable.prenom} ${department.responsable.nom}` :
            'Aucun responsable'
          }
        </span>
      </div>

      {/* Description */}
      {department.description && (
        <p style={{
          fontSize: '0.875rem',
          color: '#6b7280',
          margin: '0 0 0.75rem 0',
          lineHeight: '1.4',
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden'
        }}>
          {department.description}
        </p>
      )}

      {/* Actions */}
      <div style={{
        display: 'flex',
        justifyContent: 'flex-end',
        gap: '0.5rem',
        paddingTop: '0.5rem',
        borderTop: '1px solid #f3f4f6'
      }}>
        <button
          type="button"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onEdit();
          }}
          style={{
            backgroundColor: '#f59e0b',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            padding: '0.375rem',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '0.75rem',
            transition: 'background-color 0.2s'
          }}
          onMouseEnter={(e) => e.target.style.backgroundColor = '#d97706'}
          onMouseLeave={(e) => e.target.style.backgroundColor = '#f59e0b'}
          title="Modifier"
        >
          <FaEdit />
        </button>

        <button
          type="button"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onDelete();
          }}
          style={{
            backgroundColor: '#ef4444',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            padding: '0.375rem',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '0.75rem',
            transition: 'background-color 0.2s'
          }}
          onMouseEnter={(e) => e.target.style.backgroundColor = '#dc2626'}
          onMouseLeave={(e) => e.target.style.backgroundColor = '#ef4444'}
          title="Supprimer"
        >
          <FaTrash />
        </button>
      </div>
    </div>
  );
};

// Composant pour une carte de service
const ServiceCard = ({ service, onEdit, onDelete }) => {
  return (
    <div style={{
      backgroundColor: 'white',
      border: '1px solid #e5e7eb',
      borderRadius: '8px',
      padding: '1rem',
      marginBottom: '0.75rem',
      transition: 'all 0.2s'
    }}
    onMouseEnter={(e) => {
      e.currentTarget.style.borderColor = '#d1d5db';
      e.currentTarget.style.backgroundColor = '#f9fafb';
    }}
    onMouseLeave={(e) => {
      e.currentTarget.style.borderColor = '#e5e7eb';
      e.currentTarget.style.backgroundColor = 'white';
    }}
    >
      {/* Header avec nom et actions */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: '0.75rem'
      }}>
        <h4 style={{
          fontSize: '1rem',
          fontWeight: '600',
          color: '#1f2937',
          margin: 0,
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          <FaUsers style={{ color: '#10b981', fontSize: '0.875rem' }} />
          {service.nom_service}
        </h4>

        <div style={{
          display: 'flex',
          gap: '0.25rem'
        }}>
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onEdit();
            }}
            style={{
              backgroundColor: '#f59e0b',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              padding: '0.25rem',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '0.75rem',
              transition: 'background-color 0.2s'
            }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#d97706'}
            onMouseLeave={(e) => e.target.style.backgroundColor = '#f59e0b'}
            title="Modifier"
          >
            <FaEdit />
          </button>

          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onDelete();
            }}
            style={{
              backgroundColor: '#ef4444',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              padding: '0.25rem',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '0.75rem',
              transition: 'background-color 0.2s'
            }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#dc2626'}
            onMouseLeave={(e) => e.target.style.backgroundColor = '#ef4444'}
            title="Supprimer"
          >
            <FaTrash />
          </button>
        </div>
      </div>

      {/* Responsable */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '0.5rem',
        marginBottom: '0.5rem'
      }}>
        <FaUser style={{ color: '#6b7280', fontSize: '0.75rem' }} />
        <span style={{
          fontSize: '0.875rem',
          color: '#6b7280'
        }}>
          Responsable: {service.responsable ?
            `${service.responsable.prenom} ${service.responsable.nom}` :
            'Non assigné'
          }
        </span>
      </div>

      {/* Description */}
      {service.description && (
        <p style={{
          fontSize: '0.875rem',
          color: '#6b7280',
          margin: '0 0 0.75rem 0',
          lineHeight: '1.4'
        }}>
          {service.description}
        </p>
      )}

      {/* Footer avec statut */}
      <div style={{
        display: 'flex',
        justifyContent: 'flex-end',
        paddingTop: '0.5rem',
        borderTop: '1px solid #f3f4f6'
      }}>
        <span style={{
          padding: '0.25rem 0.5rem',
          borderRadius: '12px',
          fontSize: '0.75rem',
          fontWeight: '500',
          backgroundColor: service.is_active ? '#dcfce7' : '#fee2e2',
          color: service.is_active ? '#166534' : '#dc2626'
        }}>
          {service.is_active ? 'Actif' : 'Inactif'}
        </span>
      </div>
    </div>
  );
};

export default DepartmentServiceDashboard;
