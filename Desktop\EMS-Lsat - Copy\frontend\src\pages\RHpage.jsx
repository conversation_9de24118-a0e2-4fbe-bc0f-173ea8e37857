import { Outlet } from "react-router-dom";
import RhSidebar from "../components/dashboard/RhSidebar.jsx";
import ModernNavbar from "../components/dashboard/ModernNavbar.jsx";
import { useAuth } from "../context/authContext.jsx";

const RHpage = () => {
  const { utilisateur, loading } = useAuth();

  return (
    <div style={{ display: "flex", flexDirection: "row" }}>
      {/* Sidebar */}
      <RhSidebar />

      {/* Main content area */}
      <div style={{
        flex: 1,
        marginLeft: "280px", // Largeur par défaut du sidebar ouvert
        transition: "margin-left 0.3s ease"
      }}>
        {/* Navbar */}
        <ModernNavbar />

        {/* Page content */}
        <div
          style={{
            padding: "2rem",
            marginTop: "4rem", // Matches the navbar height
            backgroundColor: "#f7fafc", // Light gray background for contrast
            minHeight: "calc(100vh - 4rem)", // Full height minus navbar
          }}
        >
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default RHpage;