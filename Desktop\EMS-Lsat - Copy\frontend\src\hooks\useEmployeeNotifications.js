import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../context/authContext';

/**
 * Hook personnalisé pour gérer les notifications des employés
 * Récupère les mises à jour de statut des candidatures de formations
 * Utilisé pour afficher les toast notifications et les alertes
 */
const useEmployeeNotifications = () => {
  // États pour gérer les notifications
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [lastCheck, setLastCheck] = useState(null);
  const [employeeData, setEmployeeData] = useState(null);

  // Contexte d'authentification pour récupérer l'employé connecté
  const { utilisateur } = useAuth();

  /**
   * Fonction pour récupérer les données de l'employé connecté
   * Nécessaire pour obtenir l'ID employé à partir de l'ID utilisateur
   */
  const fetchEmployeeData = useCallback(async () => {
    if (!utilisateur || utilisateur.role !== 'employee') {
      return null;
    }

    try {
      const response = await fetch('http://localhost:4000/api/employees/my-info', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setEmployeeData(data.data);
          return data.data;
        }
      }
    } catch (error) {
      console.error('❌ Erreur récupération données employé:', error);
    }
    return null;
  }, [utilisateur]);

  /**
   * Fonction pour récupérer les candidatures de l'employé connecté
   * Filtre les candidatures pour détecter les changements de statut
   */
  const fetchEmployeeCandidatures = useCallback(async () => {
    // Vérifier que l'utilisateur est un employé
    if (!utilisateur || utilisateur.role !== 'employee') {
      return;
    }

    // S'assurer qu'on a les données employé
    let currentEmployeeData = employeeData;
    if (!currentEmployeeData) {
      currentEmployeeData = await fetchEmployeeData();
      if (!currentEmployeeData) {
        console.log('❌ Impossible de récupérer les données employé');
        return;
      }
    }

    try {
      setLoading(true);
      setError('');

      // Utiliser la nouvelle route API spécifique aux notifications employé
      const response = await fetch(`http://localhost:4000/api/formations/notifications-employe/${currentEmployeeData._id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();

        console.log(`🔍 Notifications employé ID = ${currentEmployeeData._id}`);
        console.log(`📊 ${data.notifications.length} notifications trouvées pour cet employé`);

        setNotifications(data.notifications || []);
        setLastCheck(new Date());

        console.log(`🔔 ${data.notifications.length} notifications trouvées pour l'employé`);
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Erreur lors du chargement des notifications');
      }
    } catch (error) {
      console.error('❌ Erreur récupération notifications employé:', error);
      setError('Erreur de connexion');
    } finally {
      setLoading(false);
    }
  }, [utilisateur, employeeData, fetchEmployeeData]);

  /**
   * Fonction pour marquer une notification comme vue par l'employé
   * @param {string} notificationId - ID de la notification/candidature
   */
  const markAsRead = useCallback(async (notificationId) => {
    try {
      // Note: Cette route devra être ajoutée à l'API pour marquer comme vu par l'employé
      const response = await fetch(`http://localhost:4000/api/formations/candidatures/${notificationId}/marquer-vue-employe`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        // Retirer la notification de la liste locale
        setNotifications(prev =>
          prev.filter(notif => notif.candidature_id !== notificationId)
        );

        console.log(`👁️ Notification ${notificationId} marquée comme vue par l'employé`);
      } else {
        console.warn('⚠️ Impossible de marquer la notification comme vue');
      }
    } catch (error) {
      console.error('❌ Erreur marquage notification employé:', error);
    }
  }, []);

  /**
   * Fonction pour marquer toutes les notifications comme vues
   */
  const markAllAsRead = useCallback(() => {
    notifications.forEach(notification => {
      markAsRead(notification.candidature_id);
    });
  }, [notifications, markAsRead]);

  /**
   * Fonction pour rafraîchir les notifications manuellement
   */
  const refreshNotifications = useCallback(() => {
    fetchEmployeeCandidatures();
  }, [fetchEmployeeCandidatures]);

  /**
   * Fonction pour obtenir les notifications par priorité
   */
  const getNotificationsByPriority = useCallback(() => {
    const high = notifications.filter(n => n.priority === 'high');
    const medium = notifications.filter(n => n.priority === 'medium');
    const low = notifications.filter(n => n.priority === 'low');

    return { high, medium, low, all: notifications };
  }, [notifications]);

  // Charger les données employé au montage
  useEffect(() => {
    if (utilisateur && utilisateur.role === 'employee') {
      fetchEmployeeData();
    }
  }, [fetchEmployeeData, utilisateur]);

  // Charger les notifications après avoir récupéré les données employé
  useEffect(() => {
    if (utilisateur && utilisateur.role === 'employee' && employeeData) {
      fetchEmployeeCandidatures();
    }
  }, [fetchEmployeeCandidatures, utilisateur, employeeData]);

  // Polling périodique pour vérifier les nouvelles notifications (toutes les 2 minutes)
  useEffect(() => {
    if (!utilisateur || utilisateur.role !== 'employee' || !employeeData) {
      return;
    }

    const interval = setInterval(() => {
      fetchEmployeeCandidatures();
    }, 120000); // 2 minutes

    return () => clearInterval(interval);
  }, [fetchEmployeeCandidatures, utilisateur, employeeData]);

  // Retourner les données et fonctions utiles
  return {
    notifications,
    loading,
    error,
    lastCheck,
    markAsRead,
    markAllAsRead,
    refreshNotifications,
    getNotificationsByPriority,
    // Statistiques utiles
    hasNotifications: notifications.length > 0,
    approvedCount: notifications.filter(n => n.statut === 'approuvee').length,
    rejectedCount: notifications.filter(n => n.statut === 'refusee').length,
    totalCount: notifications.length,
    // Fonction utilitaire pour vérifier si l'utilisateur peut voir les notifications
    canViewNotifications: utilisateur && utilisateur.role === 'employee'
  };
};

export default useEmployeeNotifications;
