import Employe from '../models/Employe.js';
import Utilisateur from '../models/utilisateur.js';
import Departement from '../models/departement.js';
import bcrypt from 'bcrypt';

// Récupérer tous les employés
const getAllEmployees = async (req, res) => {
    try {
        const { page = 1, limit = 10, departement, status } = req.query;

        let filter = {};
        if (departement) filter.departement = departement;
        if (status) filter.statut_compte = status;

        const employees = await Employe.find(filter)
            .populate('utilisateur', 'email role actif')
            .populate('departement', 'nom_departement')
            .sort({ createdAt: -1 })
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await Employe.countDocuments(filter);

        res.status(200).json({
            success: true,
            data: {
                employees,
                pagination: {
                    current: parseInt(page),
                    pages: Math.ceil(total / limit),
                    total
                }
            }
        });
    } catch (error) {
        console.error('Erreur getAllEmployees:', error);
        res.status(500).json({
            success: false,
            message: "Erreur lors de la récupération des employés"
        });
    }
};

// Récupérer un employé par ID
const getEmployeeById = async (req, res) => {
    try {
        const { id } = req.params;

        const employee = await Employe.findById(id)
            .populate('utilisateur', 'email role actif derniere_connexion')
            .populate('departement', 'nom_departement description')
            .populate('service', 'nom_service');

        if (!employee) {
            return res.status(404).json({
                success: false,
                message: "Employé non trouvé"
            });
        }

        res.status(200).json({
            success: true,
            data: employee
        });
    } catch (error) {
        console.error('Erreur getEmployeeById:', error);
        res.status(500).json({
            success: false,
            message: "Erreur lors de la récupération de l'employé"
        });
    }
};

// Créer un nouvel employé
const createEmployee = async (req, res) => {
    try {
        const {
            // 🧍 Informations personnelles
            prenom, nom, email, password, sexe, cin, cnss, statut_matrimonial,
            telephone, mobile, adresse, ville, pays, date_naissance,

            // 💼 Informations professionnelles
            departement, fonction, type_contrat, date_entree, salaire_base,
            cout_heures_supplementaires, superviseur, conges_consommes,

            // 🏦 Informations bancaires
            nom_banque, rib, adresse_banque, ville_banque,

            // Champs optionnels et de compatibilité
            service, contacts_urgence, personnes_a_charge, poste, date_embauche
        } = req.body;

        // Validation des champs requis
        const requiredFields = {
            prenom, nom, email, password, sexe, cin, statut_matrimonial,
            mobile, adresse, ville, date_naissance, departement, fonction,
            type_contrat, salaire_base
        };

        const missingFields = Object.entries(requiredFields)
            .filter(([key, value]) => !value)
            .map(([key]) => key);

        if (missingFields.length > 0) {
            return res.status(400).json({
                success: false,
                message: `Champs obligatoires manquants: ${missingFields.join(', ')}`
            });
        }

        // Vérifier si l'email existe déjà
        const existingUser = await Utilisateur.findOne({ email });
        if (existingUser) {
            return res.status(400).json({
                success: false,
                message: "Un utilisateur avec cet email existe déjà"
            });
        }

        // Vérifier si le CIN existe déjà
        const existingCIN = await Employe.findOne({ cin });
        if (existingCIN) {
            return res.status(400).json({
                success: false,
                message: "Un employé avec ce CIN existe déjà"
            });
        }

        // Vérifier si le CNSS existe déjà (si fourni)
        if (cnss) {
            const existingCNSS = await Employe.findOne({ cnss });
            if (existingCNSS) {
                return res.status(400).json({
                    success: false,
                    message: "Un employé avec ce numéro CNSS existe déjà"
                });
            }
        }

        // Vérifier que le superviseur existe et est du même département (si fourni)
        if (superviseur) {
            const superviseurEmployee = await Employe.findById(superviseur);
            if (!superviseurEmployee) {
                return res.status(400).json({
                    success: false,
                    message: "Le superviseur spécifié n'existe pas"
                });
            }
            if (superviseurEmployee.departement.toString() !== departement) {
                return res.status(400).json({
                    success: false,
                    message: "Le superviseur doit être du même département"
                });
            }
        }

        // Créer d'abord l'utilisateur
        const hashedPassword = await bcrypt.hash(password, 10);
        const newUser = new Utilisateur({
            email,
            mot_de_passe: hashedPassword,
            role: 'employe',
            permissions: ['read_profile', 'update_profile'],
            actif: true
        });
        await newUser.save();

        // Gérer l'image uploadée
        let photoPath = null;
        if (req.file) {
            photoPath = `/uploads/employees/${req.file.filename}`;
        }

        // Créer l'employé avec toutes les nouvelles informations
        const newEmployee = new Employe({
            // Informations personnelles
            prenom,
            nom,
            email,
            sexe,
            cin,
            cnss: cnss || undefined,
            statut_matrimonial,
            telephone,
            mobile,
            adresse,
            ville,
            pays: pays || 'Maroc',
            date_naissance,
            photo: photoPath,

            // Informations professionnelles
            departement,
            service: service || undefined,
            fonction,
            type_contrat,
            date_entree: date_entree ? new Date(date_entree) : new Date(),
            salaire_base: parseFloat(salaire_base),
            cout_heures_supplementaires: parseFloat(cout_heures_supplementaires) || 0,
            superviseur: superviseur || undefined,
            conges_consommes: parseInt(conges_consommes) || 0,

            // Informations bancaires
            banque: {
                nom_banque: nom_banque || undefined,
                rib: rib || undefined,
                adresse_banque: adresse_banque || undefined,
                ville_banque: ville_banque || undefined
            },

            // Champs système
            statut_compte: 'actif',
            is_active: true,
            utilisateur: newUser._id,

            // Champs optionnels
            contacts_urgence: contacts_urgence || [],
            personnes_a_charge: personnes_a_charge || [],

            // Champs de compatibilité
            poste: fonction || poste,
            date_embauche: date_entree ? new Date(date_entree) : (date_embauche ? new Date(date_embauche) : new Date())
        });

        await newEmployee.save();

        // Récupérer l'employé créé avec les relations
        const createdEmployee = await Employe.findById(newEmployee._id)
            .populate('utilisateur', 'email role actif')
            .populate('departement', 'nom_departement')
            .populate('service', 'nom_service')
            .populate('superviseur', 'nom prenom fonction email');

        res.status(201).json({
            success: true,
            message: "Employé créé avec succès",
            data: createdEmployee
        });
    } catch (error) {
        console.error('Erreur createEmployee:', error);

        // Si erreur lors de la création de l'employé, supprimer l'utilisateur créé
        if (error.name === 'ValidationError' && req.body.email) {
            await Utilisateur.findOneAndDelete({ email: req.body.email });
        }

        res.status(500).json({
            success: false,
            message: "Erreur lors de la création de l'employé",
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
};

// Mettre à jour un employé
const updateEmployee = async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = req.body;

        const employee = await Employe.findById(id);
        if (!employee) {
            return res.status(404).json({
                success: false,
                message: "Employé non trouvé"
            });
        }

        // Gérer l'image uploadée
        if (req.file) {
            updateData.photo = `/uploads/employees/${req.file.filename}`;
        }

        // Mettre à jour les champs de l'employé
        Object.keys(updateData).forEach(key => {
            if (key !== 'utilisateur' && updateData[key] !== undefined) {
                employee[key] = updateData[key];
            }
        });

        await employee.save();

        const updatedEmployee = await Employe.findById(id)
            .populate('utilisateur', 'email role actif')
            .populate('departement', 'nom_departement');

        res.status(200).json({
            success: true,
            message: "Employé mis à jour avec succès",
            data: updatedEmployee
        });
    } catch (error) {
        console.error('Erreur updateEmployee:', error);
        res.status(500).json({
            success: false,
            message: "Erreur lors de la mise à jour de l'employé"
        });
    }
};

// Mettre à jour le statut d'un employé (activer/désactiver)
const updateEmployeeStatus = async (req, res) => {
    try {
        const { id } = req.params;
        const { statut_compte, is_active } = req.body;

        // Validation des données
        if (!statut_compte || typeof is_active !== 'boolean') {
            return res.status(400).json({
                success: false,
                message: "Statut du compte et état actif sont requis"
            });
        }

        if (!['actif', 'inactif'].includes(statut_compte)) {
            return res.status(400).json({
                success: false,
                message: "Le statut doit être 'actif' ou 'inactif'"
            });
        }

        const employee = await Employe.findById(id);
        if (!employee) {
            return res.status(404).json({
                success: false,
                message: "Employé non trouvé"
            });
        }

        // Mettre à jour le statut de l'employé
        employee.statut_compte = statut_compte;
        employee.is_active = is_active;
        await employee.save();

        // Mettre à jour aussi le statut de l'utilisateur associé si il existe
        if (employee.utilisateur) {
            await Utilisateur.findByIdAndUpdate(employee.utilisateur, {
                actif: is_active
            });
        }

        // Récupérer l'employé mis à jour avec les relations
        const updatedEmployee = await Employe.findById(id)
            .populate('utilisateur', 'email role actif')
            .populate('departement', 'nom_departement');

        res.status(200).json({
            success: true,
            message: `Compte ${statut_compte === 'actif' ? 'activé' : 'désactivé'} avec succès`,
            data: updatedEmployee
        });
    } catch (error) {
        console.error('Erreur updateEmployeeStatus:', error);
        res.status(500).json({
            success: false,
            message: "Erreur lors de la mise à jour du statut"
        });
    }
};

// Supprimer un employé (action plus restrictive)
const deleteEmployee = async (req, res) => {
    try {
        const { id } = req.params;

        const employee = await Employe.findById(id);
        if (!employee) {
            return res.status(404).json({
                success: false,
                message: "Employé non trouvé"
            });
        }

        // Vérifier que le compte est inactif avant suppression
        if (employee.statut_compte === 'actif') {
            return res.status(400).json({
                success: false,
                message: "Impossible de supprimer un employé actif. Veuillez d'abord désactiver son compte."
            });
        }

        // Supprimer aussi l'utilisateur associé
        if (employee.utilisateur) {
            await Utilisateur.findByIdAndDelete(employee.utilisateur);
        }

        await Employe.findByIdAndDelete(id);

        res.status(200).json({
            success: true,
            message: "Employé supprimé définitivement avec succès"
        });
    } catch (error) {
        console.error('Erreur deleteEmployee:', error);
        res.status(500).json({
            success: false,
            message: "Erreur lors de la suppression de l'employé"
        });
    }
};

// Statistiques des employés
const getEmployeeStats = async (req, res) => {
    try {
        const totalEmployees = await Employe.countDocuments();
        const activeEmployees = await Employe.countDocuments({ statut_compte: 'actif' });
        const inactiveEmployees = await Employe.countDocuments({ statut_compte: 'inactif' });

        const employeesByDepartment = await Employe.aggregate([
            {
                $lookup: {
                    from: 'departements',
                    localField: 'departement',
                    foreignField: '_id',
                    as: 'dept'
                }
            },
            {
                $group: {
                    _id: '$departement',
                    count: { $sum: 1 },
                    departmentName: { $first: '$dept.nom_departement' }
                }
            }
        ]);

        res.status(200).json({
            success: true,
            data: {
                total: totalEmployees,
                active: activeEmployees,
                inactive: inactiveEmployees,
                byDepartment: employeesByDepartment
            }
        });
    } catch (error) {
        console.error('Erreur getEmployeeStats:', error);
        res.status(500).json({
            success: false,
            message: "Erreur lors de la récupération des statistiques"
        });
    }
};

// Récupérer les informations de l'employé connecté
const getMyInfo = async (req, res) => {
    try {
        // L'utilisateur connecté est disponible dans req.utilisateur grâce au middleware
        const userId = req.utilisateur._id;

        // Chercher l'employé correspondant à cet utilisateur
        const employee = await Employe.findOne({ utilisateur: userId })
            .populate('utilisateur', 'email role actif')
            .populate('departement', 'nom_departement description');

        if (!employee) {
            return res.status(404).json({
                success: false,
                message: "Aucune information d'employé trouvée pour cet utilisateur"
            });
        }

        res.status(200).json({
            success: true,
            data: employee
        });
    } catch (error) {
        console.error('Erreur getMyInfo:', error);
        res.status(500).json({
            success: false,
            message: "Erreur lors de la récupération de vos informations"
        });
    }
};

export {
    getAllEmployees,
    getEmployeeById,
    createEmployee,
    updateEmployee,
    updateEmployeeStatus,
    deleteEmployee,
    getEmployeeStats,
    getMyInfo
};
