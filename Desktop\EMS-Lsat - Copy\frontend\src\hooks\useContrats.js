/**
 * Hook personnalisé pour la gestion des contrats
 */
import { useState, useEffect, useCallback } from 'react';
import { contratService } from '../services/contratService';

export const useContrats = () => {
    const [contrats, setContrats] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        current: 1,
        pages: 1,
        total: 0
    });

    const fetchContrats = useCallback(async (params = {}) => {
        setLoading(true);
        setError(null);
        
        try {
            const response = await contratService.getAllContrats(params);
            if (response.success) {
                setContrats(response.data?.contrats || []);
                setPagination(response.data?.pagination || { current: 1, pages: 1, total: 0 });
            } else {
                setError(response.message || 'Erreur lors du chargement des contrats');
            }
        } catch (error) {
            setError(error.message || 'Erreur lors du chargement des contrats');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchContrats();
    }, [fetchContrats]);

    const createContrat = async (contratData) => {
        try {
            const response = await contratService.createContrat(contratData);
            if (response.success) {
                await fetchContrats(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de la création');
            }
        } catch (error) {
            throw error;
        }
    };

    const updateContrat = async (id, contratData) => {
        try {
            const response = await contratService.updateContrat(id, contratData);
            if (response.success) {
                await fetchContrats(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de la mise à jour');
            }
        } catch (error) {
            throw error;
        }
    };

    const deleteContrat = async (id) => {
        try {
            const response = await contratService.deleteContrat(id);
            if (response.success) {
                await fetchContrats(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de la suppression');
            }
        } catch (error) {
            throw error;
        }
    };

    return {
        contrats,
        loading,
        error,
        pagination,
        fetchContrats,
        createContrat,
        updateContrat,
        deleteContrat
    };
};

/**
 * Hook pour obtenir un contrat spécifique
 */
export const useContrat = (id) => {
    const [contrat, setContrat] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchContrat = useCallback(async () => {
        if (!id) return;
        
        setLoading(true);
        setError(null);
        
        try {
            const response = await contratService.getContratById(id);
            if (response.success) {
                setContrat(response.data?.contrat);
            } else {
                setError(response.message || 'Contrat non trouvé');
            }
        } catch (error) {
            setError(error.message || 'Erreur lors du chargement du contrat');
        } finally {
            setLoading(false);
        }
    }, [id]);

    useEffect(() => {
        fetchContrat();
    }, [fetchContrat]);

    return { contrat, loading, error, refetch: fetchContrat };
};

/**
 * Hook pour les statistiques des contrats
 */
export const useContratStats = () => {
    const [stats, setStats] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchStats = useCallback(async () => {
        setLoading(true);
        setError(null);
        
        try {
            const response = await contratService.getContratStats();
            if (response.success) {
                setStats(response.data);
            } else {
                setError(response.message || 'Erreur lors du chargement des statistiques');
            }
        } catch (error) {
            setError(error.message || 'Erreur lors du chargement des statistiques');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchStats();
    }, [fetchStats]);

    return { stats, loading, error, refetch: fetchStats };
};

/**
 * Hook pour obtenir les contrats d'un employé
 */
export const useContratsByEmploye = (employeId) => {
    const [contrats, setContrats] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchContratsByEmploye = useCallback(async () => {
        if (!employeId) return;
        
        setLoading(true);
        setError(null);
        
        try {
            const response = await contratService.getContratsByEmploye(employeId);
            if (response.success) {
                setContrats(response.data?.contrats || []);
            } else {
                setError(response.message || 'Erreur lors du chargement des contrats');
            }
        } catch (error) {
            setError(error.message || 'Erreur lors du chargement des contrats');
        } finally {
            setLoading(false);
        }
    }, [employeId]);

    useEffect(() => {
        fetchContratsByEmploye();
    }, [fetchContratsByEmploye]);

    return { contrats, loading, error, refetch: fetchContratsByEmploye };
};
