import React, { useState, useEffect } from 'react';
import { 
  FaTimes, 
  FaUser, 
  FaEnvelope, 
  FaBuilding, 
  FaCalendarAlt, 
  FaCheck, 
  FaHourglassHalf,
  FaTimesCircle,
  FaUsers
} from 'react-icons/fa';

const CandidaturesViewModal = ({ formation, onClose }) => {
  const [candidatures, setCandidatures] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [processing, setProcessing] = useState(null);

  useEffect(() => {
    if (formation) {
      loadCandidatures();
    }
  }, [formation]);

  // Charger les candidatures pour cette formation
  const loadCandidatures = async () => {
    try {
      setLoading(true);
      setError('');

      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:4000/api/formations/candidatures?formation_id=${formation._id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('📋 Candidatures chargées:', data);
        setCandidatures(data.candidatures || []);
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Erreur lors du chargement');
      }
    } catch (error) {
      console.error('❌ Erreur candidatures:', error);
      setError('Erreur de connexion');
    } finally {
      setLoading(false);
    }
  };

  // Approuver une candidature
  const approuverCandidature = async (candidatureId) => {
    const message = prompt('Message d\'approbation (optionnel):');
    if (message === null) return; // Annulé

    try {
      setProcessing(candidatureId);
      const token = localStorage.getItem('token');
      
      const response = await fetch(`http://localhost:4000/api/formations/candidatures/${candidatureId}/approuver`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ message_rh: message })
      });

      if (response.ok) {
        await loadCandidatures(); // Recharger la liste
        alert('Candidature approuvée avec succès');
      } else {
        const errorData = await response.json();
        alert(errorData.message || 'Erreur lors de l\'approbation');
      }
    } catch (error) {
      console.error('❌ Erreur approbation:', error);
      alert('Erreur de connexion');
    } finally {
      setProcessing(null);
    }
  };

  // Refuser une candidature
  const refuserCandidature = async (candidatureId) => {
    const message = prompt('Raison du refus (optionnel):');
    if (message === null) return; // Annulé

    try {
      setProcessing(candidatureId);
      const token = localStorage.getItem('token');
      
      const response = await fetch(`http://localhost:4000/api/formations/candidatures/${candidatureId}/refuser`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ message_rh: message })
      });

      if (response.ok) {
        await loadCandidatures(); // Recharger la liste
        alert('Candidature refusée');
      } else {
        const errorData = await response.json();
        alert(errorData.message || 'Erreur lors du refus');
      }
    } catch (error) {
      console.error('❌ Erreur refus:', error);
      alert('Erreur de connexion');
    } finally {
      setProcessing(null);
    }
  };

  // Obtenir la couleur du statut
  const getStatutColor = (statut) => {
    switch (statut) {
      case 'en_attente': return '#f59e0b';
      case 'approuvee': return '#10b981';
      case 'refusee': return '#ef4444';
      default: return '#6b7280';
    }
  };

  // Obtenir le label du statut
  const getStatutLabel = (statut) => {
    switch (statut) {
      case 'en_attente': return 'En attente';
      case 'approuvee': return 'Approuvée';
      case 'refusee': return 'Refusée';
      default: return statut;
    }
  };

  // Obtenir l'icône du statut
  const getStatutIcon = (statut) => {
    switch (statut) {
      case 'en_attente': return <FaHourglassHalf />;
      case 'approuvee': return <FaCheck />;
      case 'refusee': return <FaTimesCircle />;
      default: return <FaHourglassHalf />;
    }
  };

  // Formater la date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '1rem'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        width: '100%',
        maxWidth: '900px',
        maxHeight: '90vh',
        overflow: 'hidden',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '1.5rem',
          borderBottom: '1px solid #e5e7eb',
          backgroundColor: '#f8fafc'
        }}>
          <div>
            <h2 style={{
              margin: '0 0 0.5rem 0',
              fontSize: '1.5rem',
              fontWeight: '600',
              color: '#1f2937',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              <FaUsers style={{ color: '#3b82f6' }} />
              Candidatures - {formation?.nom_formation}
            </h2>
            <p style={{
              margin: 0,
              color: '#6b7280',
              fontSize: '0.875rem'
            }}>
              {formation?.organisme} • {candidatures.length} candidature{candidatures.length !== 1 ? 's' : ''}
            </p>
          </div>
          
          <button
            onClick={onClose}
            style={{
              backgroundColor: 'transparent',
              border: 'none',
              fontSize: '1.5rem',
              color: '#6b7280',
              cursor: 'pointer',
              padding: '0.5rem',
              borderRadius: '6px'
            }}
          >
            <FaTimes />
          </button>
        </div>

        {/* Contenu */}
        <div style={{
          padding: '1.5rem',
          maxHeight: 'calc(90vh - 120px)',
          overflow: 'auto'
        }}>
          {/* Message d'erreur */}
          {error && (
            <div style={{
              backgroundColor: '#fef2f2',
              color: '#dc2626',
              padding: '1rem',
              borderRadius: '8px',
              marginBottom: '1rem',
              border: '1px solid #fecaca'
            }}>
              {error}
            </div>
          )}

          {/* Loading */}
          {loading ? (
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '200px',
              color: '#6b7280'
            }}>
              <FaHourglassHalf style={{ marginRight: '0.5rem' }} />
              Chargement des candidatures...
            </div>
          ) : candidatures.length === 0 ? (
            // Aucune candidature
            <div style={{
              textAlign: 'center',
              padding: '3rem',
              color: '#6b7280'
            }}>
              <FaUsers style={{ fontSize: '3rem', marginBottom: '1rem', color: '#d1d5db' }} />
              <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '1.25rem' }}>
                Aucune candidature
              </h3>
              <p style={{ margin: 0 }}>
                Aucun employé n'a encore postulé à cette formation
              </p>
            </div>
          ) : (
            // Liste des candidatures
            <div style={{
              display: 'grid',
              gap: '1rem'
            }}>
              {candidatures.map((candidature) => (
                <CandidatureCard
                  key={candidature._id}
                  candidature={candidature}
                  onApprouver={() => approuverCandidature(candidature._id)}
                  onRefuser={() => refuserCandidature(candidature._id)}
                  processing={processing === candidature._id}
                  getStatutColor={getStatutColor}
                  getStatutLabel={getStatutLabel}
                  getStatutIcon={getStatutIcon}
                  formatDate={formatDate}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Composant pour une carte de candidature
const CandidatureCard = ({ 
  candidature, 
  onApprouver, 
  onRefuser, 
  processing,
  getStatutColor,
  getStatutLabel,
  getStatutIcon,
  formatDate
}) => {
  return (
    <div style={{
      backgroundColor: 'white',
      border: '1px solid #e5e7eb',
      borderRadius: '8px',
      padding: '1.5rem',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: '1rem'
      }}>
        {/* Informations employé */}
        <div style={{ flex: 1 }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            marginBottom: '0.5rem'
          }}>
            <FaUser style={{ color: '#6b7280' }} />
            <h4 style={{
              margin: 0,
              fontSize: '1.125rem',
              fontWeight: '600',
              color: '#1f2937'
            }}>
              {candidature.employe_id?.prenom} {candidature.employe_id?.nom}
            </h4>
          </div>

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            marginBottom: '0.5rem',
            fontSize: '0.875rem',
            color: '#6b7280'
          }}>
            <FaEnvelope />
            {candidature.employe_id?.email}
          </div>

          {candidature.employe_id?.departement && (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              marginBottom: '0.5rem',
              fontSize: '0.875rem',
              color: '#6b7280'
            }}>
              <FaBuilding />
              {candidature.employe_id.departement}
            </div>
          )}

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            fontSize: '0.875rem',
            color: '#6b7280'
          }}>
            <FaCalendarAlt />
            Candidature du {formatDate(candidature.date_candidature)}
          </div>
        </div>

        {/* Badge statut */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem',
          backgroundColor: getStatutColor(candidature.statut),
          color: 'white',
          padding: '0.5rem 1rem',
          borderRadius: '20px',
          fontSize: '0.875rem',
          fontWeight: '600'
        }}>
          {getStatutIcon(candidature.statut)}
          {getStatutLabel(candidature.statut)}
        </div>
      </div>

      {/* Message de l'employé */}
      {candidature.message_employe && (
        <div style={{
          backgroundColor: '#f8fafc',
          padding: '1rem',
          borderRadius: '8px',
          marginBottom: '1rem',
          borderLeft: '4px solid #3b82f6'
        }}>
          <p style={{
            margin: '0 0 0.5rem 0',
            fontSize: '0.75rem',
            fontWeight: '600',
            color: '#3b82f6',
            textTransform: 'uppercase'
          }}>
            Message de l'employé:
          </p>
          <p style={{
            margin: 0,
            fontSize: '0.875rem',
            color: '#374151',
            fontStyle: 'italic'
          }}>
            "{candidature.message_employe}"
          </p>
        </div>
      )}

      {/* Message RH (si traité) */}
      {candidature.message_rh && (
        <div style={{
          backgroundColor: '#eff6ff',
          padding: '1rem',
          borderRadius: '8px',
          marginBottom: '1rem',
          borderLeft: '4px solid #10b981'
        }}>
          <p style={{
            margin: '0 0 0.5rem 0',
            fontSize: '0.75rem',
            fontWeight: '600',
            color: '#10b981',
            textTransform: 'uppercase'
          }}>
            Réponse RH:
          </p>
          <p style={{
            margin: 0,
            fontSize: '0.875rem',
            color: '#374151'
          }}>
            {candidature.message_rh}
          </p>
        </div>
      )}

      {/* Actions (si en attente) */}
      {candidature.statut === 'en_attente' && (
        <div style={{
          display: 'flex',
          gap: '0.5rem',
          justifyContent: 'flex-end'
        }}>
          <button
            onClick={onApprouver}
            disabled={processing}
            style={{
              backgroundColor: '#10b981',
              color: 'white',
              border: 'none',
              padding: '0.5rem 1rem',
              borderRadius: '6px',
              cursor: processing ? 'not-allowed' : 'pointer',
              fontSize: '0.875rem',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              opacity: processing ? 0.7 : 1
            }}
          >
            <FaCheck />
            {processing ? 'Traitement...' : 'Approuver'}
          </button>

          <button
            onClick={onRefuser}
            disabled={processing}
            style={{
              backgroundColor: '#ef4444',
              color: 'white',
              border: 'none',
              padding: '0.5rem 1rem',
              borderRadius: '6px',
              cursor: processing ? 'not-allowed' : 'pointer',
              fontSize: '0.875rem',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              opacity: processing ? 0.7 : 1
            }}
          >
            <FaTimesCircle />
            {processing ? 'Traitement...' : 'Refuser'}
          </button>
        </div>
      )}

      {/* Date de traitement (si traité) */}
      {candidature.date_reponse && (
        <div style={{
          marginTop: '1rem',
          paddingTop: '1rem',
          borderTop: '1px solid #e5e7eb',
          fontSize: '0.75rem',
          color: '#6b7280',
          textAlign: 'right'
        }}>
          Traité le {formatDate(candidature.date_reponse)}
        </div>
      )}
    </div>
  );
};

export default CandidaturesViewModal;
