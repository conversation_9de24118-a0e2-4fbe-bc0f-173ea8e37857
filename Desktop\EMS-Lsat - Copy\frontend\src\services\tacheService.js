/**
 * Service pour la gestion des tâches
 */
import api from './api';

export const tacheService = {
    /**
     * Obtenir toutes les tâches
     * @param {Object} params - Paramètres de pagination et filtres
     * @returns {Promise} Liste des tâches
     */
    getAllTaches: async (params = {}) => {
        try {
            const { 
                page = 1, 
                limit = 10, 
                statut, 
                priorite, 
                projet, 
                employe_assigne,
                search 
            } = params;
            
            let url = `/taches?page=${page}&limit=${limit}`;
            if (statut) url += `&statut=${statut}`;
            if (priorite) url += `&priorite=${priorite}`;
            if (projet) url += `&projet=${projet}`;
            if (employe_assigne) url += `&employe_assigne=${employe_assigne}`;
            if (search) url += `&search=${encodeURIComponent(search)}`;
            
            const response = await api.get(url);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la récupération des tâches' };
        }
    },

    /**
     * Obtenir une tâche par ID
     * @param {string} id - ID de la tâche
     * @returns {Promise} Données de la tâche
     */
    getTacheById: async (id) => {
        try {
            const response = await api.get(`/taches/${id}`);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la récupération de la tâche' };
        }
    },

    /**
     * Obtenir les tâches d'un projet
     * @param {string} projetId - ID du projet
     * @returns {Promise} Liste des tâches du projet
     */
    getTachesByProjet: async (projetId) => {
        try {
            const response = await api.get(`/taches/projet/${projetId}`);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la récupération des tâches du projet' };
        }
    },

    /**
     * Créer une nouvelle tâche
     * @param {Object} tacheData - Données de la tâche
     * @returns {Promise} Tâche créée
     */
    createTache: async (tacheData) => {
        try {
            const response = await api.post('/taches', tacheData);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la création de la tâche' };
        }
    },

    /**
     * Mettre à jour une tâche
     * @param {string} id - ID de la tâche
     * @param {Object} tacheData - Nouvelles données de la tâche
     * @returns {Promise} Tâche mise à jour
     */
    updateTache: async (id, tacheData) => {
        try {
            const response = await api.put(`/taches/${id}`, tacheData);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la mise à jour de la tâche' };
        }
    },

    /**
     * Supprimer une tâche
     * @param {string} id - ID de la tâche
     * @returns {Promise} Confirmation de suppression
     */
    deleteTache: async (id) => {
        try {
            const response = await api.delete(`/taches/${id}`);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la suppression de la tâche' };
        }
    },

    /**
     * Ajouter un commentaire à une tâche
     * @param {string} id - ID de la tâche
     * @param {string} contenu - Contenu du commentaire
     * @returns {Promise} Tâche avec nouveau commentaire
     */
    ajouterCommentaire: async (id, contenu) => {
        try {
            const response = await api.post(`/taches/${id}/commentaires`, { contenu });
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de l\'ajout du commentaire' };
        }
    },

    /**
     * Obtenir les statuts disponibles
     * @returns {Array} Liste des statuts
     */
    getStatuts: () => {
        return [
            { value: 'Non commencée', label: 'Non commencée', color: '#6b7280' },
            { value: 'En cours', label: 'En cours', color: '#3b82f6' },
            { value: 'En pause', label: 'En pause', color: '#f59e0b' },
            { value: 'Terminée', label: 'Terminée', color: '#10b981' },
            { value: 'Annulée', label: 'Annulée', color: '#ef4444' }
        ];
    },

    /**
     * Obtenir les priorités disponibles
     * @returns {Array} Liste des priorités
     */
    getPriorites: () => {
        return [
            { value: 'Faible', label: 'Faible', color: '#10b981' },
            { value: 'Moyenne', label: 'Moyenne', color: '#f59e0b' },
            { value: 'Haute', label: 'Haute', color: '#ef4444' },
            { value: 'Urgente', label: 'Urgente', color: '#dc2626' }
        ];
    },

    /**
     * Formater une date pour l'affichage
     * @param {string|Date} date - Date à formater
     * @returns {string} Date formatée
     */
    formatDate: (date) => {
        if (!date) return 'Non définie';
        return new Date(date).toLocaleDateString('fr-FR', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    },

    /**
     * Calculer la durée d'une tâche
     * @param {string|Date} dateDebut - Date de début
     * @param {string|Date} dateFin - Date de fin
     * @returns {string} Durée formatée
     */
    calculateDuree: (dateDebut, dateFin) => {
        if (!dateDebut || !dateFin) return 'Non définie';
        
        const debut = new Date(dateDebut);
        const fin = new Date(dateFin);
        const diffTime = Math.abs(fin - debut);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays === 1) return '1 jour';
        if (diffDays < 7) return `${diffDays} jours`;
        if (diffDays < 30) return `${Math.floor(diffDays / 7)} semaine${Math.floor(diffDays / 7) > 1 ? 's' : ''}`;
        return `${Math.floor(diffDays / 30)} mois`;
    },

    /**
     * Vérifier si une tâche est en retard
     * @param {Object} tache - Tâche à vérifier
     * @returns {boolean} True si la tâche est en retard
     */
    isTacheEnRetard: (tache) => {
        if (tache.statut === 'Terminée' || tache.statut === 'Annulée') return false;
        
        const now = new Date();
        const dateFin = new Date(tache.date_fin_prevue);
        
        return now > dateFin;
    },

    /**
     * Calculer les jours restants
     * @param {Object} tache - Tâche
     * @returns {number} Nombre de jours restants
     */
    getJoursRestants: (tache) => {
        if (tache.statut === 'Terminée' || tache.statut === 'Annulée') return 0;
        
        const now = new Date();
        const dateFin = tache.date_fin_reelle ? new Date(tache.date_fin_reelle) : new Date(tache.date_fin_prevue);
        const diffTime = dateFin - now;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        return Math.max(0, diffDays);
    },

    /**
     * Obtenir la couleur selon la priorité
     * @param {string} priorite - Priorité de la tâche
     * @returns {string} Couleur
     */
    getPrioriteColor: (priorite) => {
        const prioriteObj = tacheService.getPriorites().find(p => p.value === priorite);
        return prioriteObj?.color || '#6b7280';
    },

    /**
     * Obtenir la couleur selon le statut
     * @param {string} statut - Statut de la tâche
     * @returns {string} Couleur
     */
    getStatutColor: (statut) => {
        const statutObj = tacheService.getStatuts().find(s => s.value === statut);
        return statutObj?.color || '#6b7280';
    },

    /**
     * Obtenir l'icône selon la priorité
     * @param {string} priorite - Priorité de la tâche
     * @returns {string} Emoji d'icône
     */
    getPrioriteIcon: (priorite) => {
        switch (priorite) {
            case 'Faible': return '🟢';
            case 'Moyenne': return '🟡';
            case 'Haute': return '🟠';
            case 'Urgente': return '🔴';
            default: return '⚪';
        }
    },

    /**
     * Obtenir l'icône selon le statut
     * @param {string} statut - Statut de la tâche
     * @returns {string} Emoji d'icône
     */
    getStatutIcon: (statut) => {
        switch (statut) {
            case 'Non commencée': return '⏳';
            case 'En cours': return '🚀';
            case 'En pause': return '⏸️';
            case 'Terminée': return '✅';
            case 'Annulée': return '❌';
            default: return '📝';
        }
    },

    /**
     * Formater le temps en heures
     * @param {number} heures - Nombre d'heures
     * @returns {string} Temps formaté
     */
    formatTemps: (heures) => {
        if (!heures || heures === 0) return '0h';
        if (heures < 1) return `${Math.round(heures * 60)}min`;
        if (heures < 8) return `${heures}h`;
        
        const jours = Math.floor(heures / 8);
        const heuresRestantes = heures % 8;
        
        if (heuresRestantes === 0) {
            return `${jours}j`;
        }
        return `${jours}j ${heuresRestantes}h`;
    }
};
