import React, { useState } from 'react';
import { FaBuilding, FaCogs, FaChartBar } from 'react-icons/fa';
import DepartmentList from '../departement/DepartmentList';
import ServiceList from '../services/ServiceList';
import { useDepartments, useDepartmentStats } from '../../hooks/useDepartments';
import { useServices, useServiceStats } from '../../hooks/useServices';

const DepartmentServiceManagement = () => {
  const [activeTab, setActiveTab] = useState('departments');
  const { departments } = useDepartments();
  const { services } = useServices();
  const { stats: deptStats } = useDepartmentStats();
  const { stats: serviceStats } = useServiceStats();

  const tabs = [
    {
      id: 'departments',
      label: 'Départements',
      icon: FaBuilding,
      color: '#3b82f6',
      count: departments.length
    },
    {
      id: 'services',
      label: 'Services',
      icon: FaCogs,
      color: '#10b981',
      count: services.length
    },
    {
      id: 'overview',
      label: 'Vue d\'ensemble',
      icon: FaChartBar,
      color: '#8b5cf6',
      count: null
    }
  ];

  const renderOverview = () => (
    <div style={{ padding: '1rem' }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '2rem',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        marginBottom: '2rem'
      }}>
        <h2 style={{
          fontSize: '1.75rem',
          fontWeight: 'bold',
          color: '#1a202c',
          margin: '0 0 1.5rem 0',
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          <FaChartBar style={{ color: '#8b5cf6' }} />
          Vue d'ensemble - Départements & Services
        </h2>

        {/* Statistiques générales */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '1.5rem',
          marginBottom: '2rem'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
            borderRadius: '12px',
            padding: '1.5rem',
            color: 'white',
            position: 'relative',
            overflow: 'hidden'
          }}>
            <div style={{
              position: 'absolute',
              top: '-20px',
              right: '-20px',
              width: '80px',
              height: '80px',
              borderRadius: '50%',
              background: 'rgba(255, 255, 255, 0.1)',
            }} />
            <FaBuilding style={{ fontSize: '2rem', marginBottom: '0.5rem' }} />
            <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '2rem', fontWeight: 'bold' }}>
              {departments.length}
            </h3>
            <p style={{ margin: 0, opacity: 0.9 }}>Départements</p>
          </div>

          <div style={{
            background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
            borderRadius: '12px',
            padding: '1.5rem',
            color: 'white',
            position: 'relative',
            overflow: 'hidden'
          }}>
            <div style={{
              position: 'absolute',
              top: '-20px',
              right: '-20px',
              width: '80px',
              height: '80px',
              borderRadius: '50%',
              background: 'rgba(255, 255, 255, 0.1)',
            }} />
            <FaCogs style={{ fontSize: '2rem', marginBottom: '0.5rem' }} />
            <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '2rem', fontWeight: 'bold' }}>
              {services.length}
            </h3>
            <p style={{ margin: 0, opacity: 0.9 }}>Services</p>
          </div>

          <div style={{
            background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
            borderRadius: '12px',
            padding: '1.5rem',
            color: 'white',
            position: 'relative',
            overflow: 'hidden'
          }}>
            <div style={{
              position: 'absolute',
              top: '-20px',
              right: '-20px',
              width: '80px',
              height: '80px',
              borderRadius: '50%',
              background: 'rgba(255, 255, 255, 0.1)',
            }} />
            <FaChartBar style={{ fontSize: '2rem', marginBottom: '0.5rem' }} />
            <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '2rem', fontWeight: 'bold' }}>
              {serviceStats?.active || 0}
            </h3>
            <p style={{ margin: 0, opacity: 0.9 }}>Services Actifs</p>
          </div>
        </div>

        {/* Répartition par département */}
        <div style={{
          backgroundColor: '#f8fafc',
          borderRadius: '8px',
          padding: '1.5rem'
        }}>
          <h3 style={{
            fontSize: '1.25rem',
            fontWeight: '600',
            color: '#1f2937',
            margin: '0 0 1rem 0'
          }}>
            Répartition des Services par Département
          </h3>
          
          {departments.length === 0 ? (
            <p style={{ color: '#6b7280', textAlign: 'center', margin: '2rem 0' }}>
              Aucun département créé
            </p>
          ) : (
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
              gap: '1rem'
            }}>
              {departments.map((dept) => {
                const deptServices = services.filter(s => s.departement?._id === dept._id);
                return (
                  <div
                    key={dept._id}
                    style={{
                      backgroundColor: 'white',
                      borderRadius: '8px',
                      padding: '1rem',
                      border: '1px solid #e5e7eb'
                    }}
                  >
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      marginBottom: '0.75rem'
                    }}>
                      <FaBuilding style={{ color: '#3b82f6', fontSize: '1rem' }} />
                      <h4 style={{
                        margin: 0,
                        fontSize: '1rem',
                        fontWeight: '600',
                        color: '#1f2937'
                      }}>
                        {dept.nom_departement}
                      </h4>
                    </div>
                    
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between'
                    }}>
                      <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>
                        Services: {deptServices.length}
                      </span>
                      <div style={{
                        padding: '0.25rem 0.5rem',
                        borderRadius: '12px',
                        backgroundColor: deptServices.length > 0 ? '#dcfce7' : '#fee2e2',
                        color: deptServices.length > 0 ? '#166534' : '#dc2626',
                        fontSize: '0.75rem',
                        fontWeight: '500'
                      }}>
                        {deptServices.length > 0 ? 'Actif' : 'Vide'}
                      </div>
                    </div>

                    {deptServices.length > 0 && (
                      <div style={{ marginTop: '0.75rem' }}>
                        <div style={{
                          fontSize: '0.75rem',
                          color: '#6b7280',
                          marginBottom: '0.25rem'
                        }}>
                          Services:
                        </div>
                        {deptServices.slice(0, 3).map((service) => (
                          <div
                            key={service._id}
                            style={{
                              fontSize: '0.75rem',
                              color: '#374151',
                              padding: '0.125rem 0'
                            }}
                          >
                            • {service.nom_service}
                          </div>
                        ))}
                        {deptServices.length > 3 && (
                          <div style={{
                            fontSize: '0.75rem',
                            color: '#6b7280',
                            fontStyle: 'italic'
                          }}>
                            +{deptServices.length - 3} autres...
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div style={{ padding: '1rem' }}>
      {/* En-tête avec onglets */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '1.5rem',
        marginBottom: '1.5rem',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      }}>
        <h1 style={{
          fontSize: '2rem',
          fontWeight: 'bold',
          color: '#1a202c',
          margin: '0 0 1.5rem 0'
        }}>
          Gestion Départements & Services
        </h1>

        {/* Onglets */}
        <div style={{
          display: 'flex',
          gap: '0.5rem',
          borderBottom: '1px solid #e5e7eb',
          paddingBottom: '1rem'
        }}>
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;
            
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  padding: '0.75rem 1.5rem',
                  border: 'none',
                  borderRadius: '8px',
                  backgroundColor: isActive ? tab.color : 'transparent',
                  color: isActive ? 'white' : '#6b7280',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  fontSize: '0.9rem',
                  fontWeight: '500'
                }}
                onMouseOver={(e) => {
                  if (!isActive) {
                    e.target.style.backgroundColor = '#f3f4f6';
                    e.target.style.color = '#374151';
                  }
                }}
                onMouseOut={(e) => {
                  if (!isActive) {
                    e.target.style.backgroundColor = 'transparent';
                    e.target.style.color = '#6b7280';
                  }
                }}
              >
                <Icon />
                <span>{tab.label}</span>
                {tab.count !== null && (
                  <span style={{
                    backgroundColor: isActive ? 'rgba(255, 255, 255, 0.2)' : '#e5e7eb',
                    color: isActive ? 'white' : '#6b7280',
                    padding: '0.125rem 0.5rem',
                    borderRadius: '12px',
                    fontSize: '0.75rem',
                    fontWeight: '600'
                  }}>
                    {tab.count}
                  </span>
                )}
              </button>
            );
          })}
        </div>
      </div>

      {/* Contenu des onglets */}
      <div>
        {activeTab === 'departments' && <DepartmentList />}
        {activeTab === 'services' && <ServiceList />}
        {activeTab === 'overview' && renderOverview()}
      </div>
    </div>
  );
};

export default DepartmentServiceManagement;
