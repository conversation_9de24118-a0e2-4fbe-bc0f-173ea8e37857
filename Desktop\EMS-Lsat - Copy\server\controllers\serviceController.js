/**
 * Contrôleur pour la gestion des services
 */
import Service from '../models/Service.js';
import Departement from '../models/departement.js';
import { successResponse, errorResponse, notFoundResponse, serverErrorResponse } from '../utils/responseHelper.js';

/**
 * Récupérer tous les services
 */
export const getAllServices = async (req, res) => {
    try {
        const { page = 1, limit = 10, departement } = req.query;

        // Construire le filtre
        const filter = {};
        if (departement) {
            filter.departement = departement;
        }

        const services = await Service.find(filter)
            .populate('departement', 'nom_departement description')
            .populate('responsable', 'nom prenom email')
            .sort({ nom_service: 1 })
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await Service.countDocuments(filter);

        return successResponse(res, {
            services,
            pagination: {
                current: parseInt(page),
                pages: Math.ceil(total / limit),
                total
            }
        }, "Services récupérés avec succès");

    } catch (error) {
        console.error('Erreur getAllServices:', error);
        return serverErrorResponse(res, "Erreur lors de la récupération des services", error);
    }
};

/**
 * Récupérer un service par ID
 */
export const getServiceById = async (req, res) => {
    try {
        const { id } = req.params;

        const service = await Service.findById(id)
            .populate('departement', 'nom_departement description')
            .populate('responsable', 'nom prenom email poste');

        if (!service) {
            return notFoundResponse(res, "Service non trouvé");
        }

        return successResponse(res, { service }, "Service récupéré avec succès");

    } catch (error) {
        console.error('Erreur getServiceById:', error);
        return serverErrorResponse(res, "Erreur lors de la récupération du service", error);
    }
};

/**
 * Créer un nouveau service
 */
export const createService = async (req, res) => {
    try {
        const { nom_service, description, departement, responsable, is_active } = req.body;

        // Validation des champs requis
        if (!nom_service || !departement) {
            return errorResponse(res, "Le nom du service et le département sont requis", 400);
        }

        // Vérifier que le département existe
        const departementExists = await Departement.findById(departement);
        if (!departementExists) {
            return notFoundResponse(res, "Département non trouvé");
        }

        // Vérifier que le responsable existe si fourni
        if (responsable) {
            const Employe = (await import('../models/Employe.js')).default;
            const employeExists = await Employe.findById(responsable);
            if (!employeExists) {
                return errorResponse(res, "L'employé responsable spécifié n'existe pas", 400);
            }
        }

        // Vérifier si le service existe déjà dans ce département
        const existingService = await Service.findOne({
            nom_service: nom_service.trim(),
            departement
        });

        if (existingService) {
            return errorResponse(res, "Un service avec ce nom existe déjà dans ce département", 400);
        }

        // Créer le nouveau service
        const newService = new Service({
            nom_service: nom_service.trim(),
            description: description?.trim(),
            departement,
            responsable: responsable || null,
            is_active: is_active !== undefined ? is_active : true
        });

        await newService.save();

        // Récupérer le service avec les données populées
        const populatedService = await Service.findById(newService._id)
            .populate('departement', 'nom_departement description')
            .populate('responsable', 'nom prenom email');

        return successResponse(res, { service: populatedService }, "Service créé avec succès", 201);

    } catch (error) {
        console.error('Erreur createService:', error);

        // Gestion des erreurs de validation MongoDB
        if (error.code === 11000) {
            return errorResponse(res, "Un service avec ce nom existe déjà dans ce département", 400);
        }

        return serverErrorResponse(res, "Erreur lors de la création du service", error);
    }
};

/**
 * Mettre à jour un service
 */
export const updateService = async (req, res) => {
    try {
        const { id } = req.params;
        const { nom_service, description, departement, responsable, is_active } = req.body;

        const service = await Service.findById(id);
        if (!service) {
            return notFoundResponse(res, "Service non trouvé");
        }

        // Vérifier que le département existe si fourni
        if (departement && departement !== service.departement.toString()) {
            const departementExists = await Departement.findById(departement);
            if (!departementExists) {
                return notFoundResponse(res, "Département non trouvé");
            }
        }

        // Vérifier que le responsable existe si fourni
        if (responsable) {
            const Employe = (await import('../models/Employe.js')).default;
            const employeExists = await Employe.findById(responsable);
            if (!employeExists) {
                return errorResponse(res, "L'employé responsable spécifié n'existe pas", 400);
            }
        }

        // Mettre à jour les champs
        if (nom_service) service.nom_service = nom_service.trim();
        if (description !== undefined) service.description = description?.trim();
        if (departement) service.departement = departement;
        if (responsable !== undefined) service.responsable = responsable || null;
        if (is_active !== undefined) service.is_active = is_active;

        await service.save();

        // Récupérer le service mis à jour avec les données populées
        const updatedService = await Service.findById(id)
            .populate('departement', 'nom_departement description')
            .populate('responsable', 'nom prenom email');

        return successResponse(res, { service: updatedService }, "Service mis à jour avec succès");

    } catch (error) {
        console.error('Erreur updateService:', error);

        if (error.code === 11000) {
            return errorResponse(res, "Un service avec ce nom existe déjà dans ce département", 400);
        }

        return serverErrorResponse(res, "Erreur lors de la mise à jour du service", error);
    }
};

/**
 * Supprimer un service
 */
export const deleteService = async (req, res) => {
    try {
        const { id } = req.params;

        const service = await Service.findById(id);
        if (!service) {
            return notFoundResponse(res, "Service non trouvé");
        }

        // Vérifier s'il y a des employés dans ce service
        const Employe = (await import('../models/Employe.js')).default;
        const employeesCount = await Employe.countDocuments({ service: id });

        if (employeesCount > 0) {
            return errorResponse(res,
                `Impossible de supprimer le service. ${employeesCount} employé(s) y sont assigné(s).`,
                400
            );
        }

        await Service.findByIdAndDelete(id);

        return successResponse(res, null, "Service supprimé avec succès");

    } catch (error) {
        console.error('Erreur deleteService:', error);
        return serverErrorResponse(res, "Erreur lors de la suppression du service", error);
    }
};

/**
 * Obtenir les statistiques des services
 */
export const getServiceStats = async (req, res) => {
    try {
        const totalServices = await Service.countDocuments();
        const activeServices = await Service.countDocuments({ actif: true });

        // Services par département
        const servicesByDepartment = await Service.aggregate([
            {
                $lookup: {
                    from: 'departements',
                    localField: 'departement',
                    foreignField: '_id',
                    as: 'departementInfo'
                }
            },
            {
                $unwind: '$departementInfo'
            },
            {
                $group: {
                    _id: '$departement',
                    nom_departement: { $first: '$departementInfo.nom_departement' },
                    count: { $sum: 1 },
                    services: {
                        $push: {
                            _id: '$_id',
                            nom_service: '$nom_service',
                            actif: '$actif'
                        }
                    }
                }
            },
            {
                $sort: { count: -1 }
            }
        ]);

        return successResponse(res, {
            total: totalServices,
            active: activeServices,
            inactive: totalServices - activeServices,
            byDepartment: servicesByDepartment
        }, "Statistiques des services récupérées avec succès");

    } catch (error) {
        console.error('Erreur getServiceStats:', error);
        return serverErrorResponse(res, "Erreur lors de la récupération des statistiques", error);
    }
};

/**
 * Obtenir les services d'un département spécifique
 */
export const getServicesByDepartment = async (req, res) => {
    try {
        const { departmentId } = req.params;

        // Vérifier que le département existe
        const departement = await Departement.findById(departmentId);
        if (!departement) {
            return notFoundResponse(res, "Département non trouvé");
        }

        const services = await Service.find({ departement: departmentId, actif: true })
            .populate('chef_service', 'nom prenom email')
            .sort({ nom_service: 1 });

        return successResponse(res, {
            departement: {
                _id: departement._id,
                nom_departement: departement.nom_departement
            },
            services
        }, "Services du département récupérés avec succès");

    } catch (error) {
        console.error('Erreur getServicesByDepartment:', error);
        return serverErrorResponse(res, "Erreur lors de la récupération des services", error);
    }
};
