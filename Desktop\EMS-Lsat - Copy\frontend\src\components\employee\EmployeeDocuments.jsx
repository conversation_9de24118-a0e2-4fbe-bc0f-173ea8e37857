import React, { useState, useEffect } from 'react';
import { FaFileAlt, FaDownload, FaPrint, FaPlus, FaCalendarAlt, FaUser, FaBuilding } from 'react-icons/fa';

const EmployeeDocuments = ({ employeeData }) => {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showAttestationModal, setShowAttestationModal] = useState(false);

  useEffect(() => {
    loadDocuments();
  }, []);

  const loadDocuments = async () => {
    setLoading(true);
    try {
      // TODO: Implémenter l'API pour récupérer les documents de l'employé
      // const response = await documentService.getMyDocuments();
      // setDocuments(response.data);
      
      // Données de test pour l'instant
      setDocuments([
        {
          _id: '1',
          type: 'Attestation de travail',
          date_creation: '2024-05-15',
          statut: 'Généré',
          url: '/documents/attestation_2024_05_15.pdf'
        },
        {
          _id: '2',
          type: 'Certificat de salaire',
          date_creation: '2024-04-30',
          statut: 'Généré',
          url: '/documents/certificat_salaire_2024_04.pdf'
        }
      ]);
    } catch (error) {
      console.error('Erreur lors du chargement des documents:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateAttestation = async (type) => {
    try {
      // TODO: Implémenter l'API pour générer une attestation
      console.log('Génération d\'attestation:', type);
      
      // Simuler la génération
      const newDoc = {
        _id: Date.now().toString(),
        type: 'Attestation de travail',
        date_creation: new Date().toISOString().split('T')[0],
        statut: 'Généré',
        url: `/documents/attestation_${Date.now()}.pdf`
      };
      
      setDocuments(prev => [newDoc, ...prev]);
      setShowAttestationModal(false);
      
      // Simuler le téléchargement
      alert('Attestation générée avec succès ! Le téléchargement va commencer...');
    } catch (error) {
      console.error('Erreur lors de la génération:', error);
    }
  };

  const downloadDocument = (document) => {
    // TODO: Implémenter le téléchargement réel
    console.log('Téléchargement du document:', document);
    alert(`Téléchargement de ${document.type} en cours...`);
  };

  const printDocument = (document) => {
    // TODO: Implémenter l'impression
    console.log('Impression du document:', document);
    alert(`Impression de ${document.type} en cours...`);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const AttestationModal = () => {
    const [attestationType, setAttestationType] = useState('travail');

    const handleGenerate = () => {
      generateAttestation(attestationType);
    };

    return (
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000
      }}>
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '2rem',
          width: '90%',
          maxWidth: '500px',
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
        }}>
          <h2 style={{
            margin: '0 0 1.5rem 0',
            fontSize: '1.5rem',
            color: '#374151',
            fontWeight: '600'
          }}>
            Générer une attestation
          </h2>

          <div style={{ marginBottom: '1.5rem' }}>
            <label style={{
              display: 'block',
              marginBottom: '0.5rem',
              fontSize: '0.875rem',
              fontWeight: '500',
              color: '#374151'
            }}>
              Type d'attestation
            </label>
            <select
              value={attestationType}
              onChange={(e) => setAttestationType(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '1rem'
              }}
            >
              <option value="travail">Attestation de travail</option>
              <option value="salaire">Certificat de salaire</option>
              <option value="emploi">Attestation d'emploi</option>
            </select>
          </div>

          {/* Aperçu des informations */}
          <div style={{
            backgroundColor: '#f9fafb',
            border: '1px solid #e5e7eb',
            borderRadius: '8px',
            padding: '1rem',
            marginBottom: '1.5rem'
          }}>
            <h3 style={{
              margin: '0 0 1rem 0',
              fontSize: '1rem',
              color: '#374151',
              fontWeight: '600'
            }}>
              Informations qui seront incluses :
            </h3>
            <div style={{ fontSize: '0.875rem', color: '#6b7280', lineHeight: '1.6' }}>
              <p style={{ margin: '0 0 0.5rem 0' }}>
                <strong>Nom :</strong> {employeeData?.prenom} {employeeData?.nom}
              </p>
              <p style={{ margin: '0 0 0.5rem 0' }}>
                <strong>Poste :</strong> {employeeData?.poste || 'Non défini'}
              </p>
              <p style={{ margin: '0 0 0.5rem 0' }}>
                <strong>Département :</strong> {employeeData?.departement?.nom_departement || 'Non assigné'}
              </p>
              <p style={{ margin: '0 0 0.5rem 0' }}>
                <strong>Date d'embauche :</strong> {employeeData?.date_embauche ? formatDate(employeeData.date_embauche) : 'Non renseignée'}
              </p>
              <p style={{ margin: 0 }}>
                <strong>Date de génération :</strong> {formatDate(new Date().toISOString())}
              </p>
            </div>
          </div>

          <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end' }}>
            <button
              onClick={() => setShowAttestationModal(false)}
              style={{
                padding: '0.75rem 1.5rem',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                backgroundColor: 'white',
                color: '#374151',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: 'pointer'
              }}
            >
              Annuler
            </button>
            <button
              onClick={handleGenerate}
              style={{
                padding: '0.75rem 1.5rem',
                border: 'none',
                borderRadius: '8px',
                backgroundColor: '#10b981',
                color: 'white',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
            >
              <FaFileAlt />
              Générer
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div style={{ padding: '1rem' }}>
      {/* En-tête */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '1.5rem',
        marginBottom: '2rem',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        border: '1px solid #e5e7eb'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h1 style={{
            margin: 0,
            fontSize: '1.5rem',
            color: '#374151',
            fontWeight: '600',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            <FaFileAlt style={{ color: '#10b981' }} />
            Mes documents
          </h1>
          <button
            onClick={() => setShowAttestationModal(true)}
            style={{
              backgroundColor: '#10b981',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '0.75rem 1.5rem',
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
          >
            <FaPlus />
            Générer une attestation
          </button>
        </div>
      </div>

      {/* Types de documents disponibles */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '1rem',
        marginBottom: '2rem'
      }}>
        <div style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '1rem',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
          border: '1px solid #e5e7eb',
          textAlign: 'center'
        }}>
          <FaFileAlt style={{ fontSize: '2rem', color: '#10b981', marginBottom: '0.5rem' }} />
          <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '1rem', color: '#374151' }}>
            Attestation de travail
          </h3>
          <p style={{ margin: 0, fontSize: '0.875rem', color: '#6b7280' }}>
            Document officiel certifiant votre emploi
          </p>
        </div>
        <div style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '1rem',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
          border: '1px solid #e5e7eb',
          textAlign: 'center'
        }}>
          <FaFileAlt style={{ fontSize: '2rem', color: '#3b82f6', marginBottom: '0.5rem' }} />
          <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '1rem', color: '#374151' }}>
            Certificat de salaire
          </h3>
          <p style={{ margin: 0, fontSize: '0.875rem', color: '#6b7280' }}>
            Justificatif de vos revenus
          </p>
        </div>
        <div style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '1rem',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
          border: '1px solid #e5e7eb',
          textAlign: 'center'
        }}>
          <FaFileAlt style={{ fontSize: '2rem', color: '#f59e0b', marginBottom: '0.5rem' }} />
          <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '1rem', color: '#374151' }}>
            Attestation d'emploi
          </h3>
          <p style={{ margin: 0, fontSize: '0.875rem', color: '#6b7280' }}>
            Preuve de votre statut d'employé
          </p>
        </div>
      </div>

      {/* Liste des documents générés */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '1.5rem',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        border: '1px solid #e5e7eb'
      }}>
        <h2 style={{
          margin: '0 0 1.5rem 0',
          fontSize: '1.3rem',
          color: '#374151',
          fontWeight: '600'
        }}>
          Documents générés
        </h2>

        {loading ? (
          <p>Chargement des documents...</p>
        ) : documents.length > 0 ? (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            {documents.map((document) => (
              <div
                key={document._id}
                style={{
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  padding: '1rem',
                  backgroundColor: '#f9fafb',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                  <div style={{
                    width: '40px',
                    height: '40px',
                    borderRadius: '50%',
                    backgroundColor: '#10b981',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white'
                  }}>
                    <FaFileAlt />
                  </div>
                  <div>
                    <h3 style={{
                      margin: '0 0 0.25rem 0',
                      fontSize: '1rem',
                      color: '#374151',
                      fontWeight: '600'
                    }}>
                      {document.type}
                    </h3>
                    <p style={{
                      margin: 0,
                      fontSize: '0.875rem',
                      color: '#6b7280'
                    }}>
                      Généré le {formatDate(document.date_creation)}
                    </p>
                  </div>
                </div>
                <div style={{ display: 'flex', gap: '0.5rem' }}>
                  <button
                    onClick={() => downloadDocument(document)}
                    style={{
                      backgroundColor: '#3b82f6',
                      color: 'white',
                      border: 'none',
                      borderRadius: '6px',
                      padding: '0.5rem',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                    title="Télécharger"
                  >
                    <FaDownload />
                  </button>
                  <button
                    onClick={() => printDocument(document)}
                    style={{
                      backgroundColor: '#6b7280',
                      color: 'white',
                      border: 'none',
                      borderRadius: '6px',
                      padding: '0.5rem',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                    title="Imprimer"
                  >
                    <FaPrint />
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '2rem', color: '#6b7280' }}>
            <FaFileAlt style={{ fontSize: '3rem', marginBottom: '1rem', opacity: 0.5 }} />
            <p>Aucun document généré</p>
            <p style={{ fontSize: '0.875rem' }}>Cliquez sur "Générer une attestation" pour commencer</p>
          </div>
        )}
      </div>

      {/* Modal de génération d'attestation */}
      {showAttestationModal && <AttestationModal />}
    </div>
  );
};

export default EmployeeDocuments;
