import mongoose from "mongoose";

const contratSchema = new mongoose.Schema({
  type_contrat: {
    type: String,
    required: true,
    enum: ['CDI', 'CDD', 'Stage', 'Freelance', 'Apprentissage', 'Interim'],
    index: true
  },
  date_debut: {
    type: Date,
    required: true,
    index: true
  },
  date_fin: {
    type: Date,
    validate: {
      validator: function(value) {
        // date_fin optionnelle pour CDI, obligatoire pour CDD, Stage, etc.
        if (this.type_contrat === 'CDI') return true;
        return value && value > this.date_debut;
      },
      message: 'La date de fin doit être postérieure à la date de début'
    }
  },
  salaire_base: {
    type: Number,
    required: true,
    min: 0,
    comment: "Salaire de base en Dirhams"
  },
  cout_heures_supplementaires: {
    type: Number,
    default: 0,
    min: 0,
    comment: "Coût par heure supplémentaire en Dirhams"
  },
  employe: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Employe",
    required: true,
    index: true
  },
  superviseur: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Employe",
    validate: {
      validator: async function(value) {
        if (!value) return true; // Superviseur optionnel

        // Vérifier que le superviseur est dans le même département que l'employé
        const employe = await mongoose.model('Employe').findById(this.employe);
        const superviseur = await mongoose.model('Employe').findById(value);

        return employe && superviseur &&
               employe.departement.toString() === superviseur.departement.toString();
      },
      message: 'Le superviseur doit être du même département que l\'employé'
    }
  },
  statut: {
    type: String,
    enum: ['Actif', 'Terminé', 'Suspendu', 'Annulé'],
    default: 'Actif',
    index: true
  },
  description: {
    type: String,
    trim: true
  },
  conditions_particulieres: {
    type: String,
    trim: true
  },
  // Champ de compatibilité avec l'ancien modèle
  salaire: {
    type: Number,
    default: function() { return this.salaire_base; }
  }
}, {
  timestamps: true
});

// Index composé pour éviter les doublons employe + type_contrat actif
contratSchema.index({ employe: 1, statut: 1 });

// Méthode pour vérifier si le contrat est actif
contratSchema.methods.isActif = function() {
  const now = new Date();
  return this.statut === 'Actif' &&
         this.date_debut <= now &&
         (!this.date_fin || this.date_fin >= now);
};

// Méthode pour calculer la durée du contrat
contratSchema.methods.getDuree = function() {
  if (!this.date_fin) return null; // CDI
  const diffTime = Math.abs(this.date_fin - this.date_debut);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

const Contrat = mongoose.model("Contrat", contratSchema);
export default Contrat;
