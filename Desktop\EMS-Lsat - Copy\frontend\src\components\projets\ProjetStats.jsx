import React from 'react';
import { FaProjectDiagram, FaPlay, FaCheckCircle, FaExclamationTriangle, FaEuroSign } from 'react-icons/fa';
import { useProjetStats } from '../../hooks/useProjets';
import { projetService } from '../../services/projetService';

const ProjetStats = () => {
  const { stats, loading, error } = useProjetStats();

  if (loading) {
    return (
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '1rem',
        marginBottom: '2rem'
      }}>
        {[...Array(4)].map((_, index) => (
          <div
            key={index}
            style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              padding: '1.5rem',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              display: 'flex',
              alignItems: 'center',
              gap: '1rem'
            }}
          >
            <div style={{
              width: '60px',
              height: '60px',
              borderRadius: '12px',
              backgroundColor: '#f3f4f6',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <div style={{
                width: '24px',
                height: '24px',
                backgroundColor: '#e5e7eb',
                borderRadius: '4px'
              }} />
            </div>
            <div style={{ flex: 1 }}>
              <div style={{
                height: '20px',
                backgroundColor: '#e5e7eb',
                borderRadius: '4px',
                marginBottom: '0.5rem'
              }} />
              <div style={{
                height: '16px',
                backgroundColor: '#f3f4f6',
                borderRadius: '4px',
                width: '60%'
              }} />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        backgroundColor: '#fef2f2',
        border: '1px solid #fecaca',
        borderRadius: '8px',
        padding: '1rem',
        marginBottom: '2rem',
        color: '#dc2626'
      }}>
        <strong>Erreur:</strong> {error}
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  const statsCards = [
    {
      title: 'Total Projets',
      value: stats.total || 0,
      icon: FaProjectDiagram,
      color: '#8b5cf6',
      bgColor: '#f3e8ff'
    },
    {
      title: 'Projets Actifs',
      value: stats.actifs || 0,
      icon: FaPlay,
      color: '#3b82f6',
      bgColor: '#dbeafe'
    },
    {
      title: 'Projets Terminés',
      value: stats.termines || 0,
      icon: FaCheckCircle,
      color: '#10b981',
      bgColor: '#d1fae5'
    },
    {
      title: 'Projets en Retard',
      value: stats.enRetard || 0,
      icon: FaExclamationTriangle,
      color: '#ef4444',
      bgColor: '#fee2e2'
    }
  ];

  return (
    <div style={{ marginBottom: '2rem' }}>
      {/* Statistiques principales */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '1rem',
        marginBottom: '1.5rem'
      }}>
        {statsCards.map((stat, index) => (
          <div
            key={index}
            style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              padding: '1.5rem',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              display: 'flex',
              alignItems: 'center',
              gap: '1rem',
              transition: 'transform 0.2s, box-shadow 0.2s'
            }}
            onMouseOver={(e) => {
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
            }}
          >
            <div style={{
              width: '60px',
              height: '60px',
              borderRadius: '12px',
              backgroundColor: stat.bgColor,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: stat.color
            }}>
              <stat.icon size={24} />
            </div>
            <div style={{ flex: 1 }}>
              <h3 style={{
                margin: 0,
                fontSize: '2rem',
                fontWeight: 'bold',
                color: '#1f2937'
              }}>
                {stat.value}
              </h3>
              <p style={{
                margin: 0,
                fontSize: '0.875rem',
                color: '#6b7280'
              }}>
                {stat.title}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* Statistiques détaillées */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '1rem'
      }}>
        {/* Répartition par statut */}
        {stats.parStatut && stats.parStatut.length > 0 && (
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '1.5rem',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
          }}>
            <h4 style={{
              margin: '0 0 1rem 0',
              fontSize: '1.125rem',
              fontWeight: '600',
              color: '#1f2937'
            }}>
              Répartition par statut
            </h4>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
              {stats.parStatut.map((item, index) => (
                <div key={index} style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '0.75rem',
                  backgroundColor: '#f9fafb',
                  borderRadius: '8px'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    <span style={{
                      padding: '0.25rem 0.5rem',
                      borderRadius: '12px',
                      backgroundColor: `${projetService.getStatutColor(item._id)}20`,
                      color: projetService.getStatutColor(item._id),
                      fontSize: '0.75rem',
                      fontWeight: '500'
                    }}>
                      {projetService.getStatutIcon(item._id)} {item._id}
                    </span>
                  </div>
                  <span style={{
                    fontSize: '1rem',
                    fontWeight: '600',
                    color: '#1f2937'
                  }}>
                    {item.count}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Répartition par priorité */}
        {stats.parPriorite && stats.parPriorite.length > 0 && (
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '1.5rem',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
          }}>
            <h4 style={{
              margin: '0 0 1rem 0',
              fontSize: '1.125rem',
              fontWeight: '600',
              color: '#1f2937'
            }}>
              Répartition par priorité
            </h4>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
              {stats.parPriorite.map((item, index) => (
                <div key={index} style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '0.75rem',
                  backgroundColor: '#f9fafb',
                  borderRadius: '8px'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    <span style={{
                      padding: '0.25rem 0.5rem',
                      borderRadius: '12px',
                      backgroundColor: `${projetService.getPrioriteColor(item._id)}20`,
                      color: projetService.getPrioriteColor(item._id),
                      fontSize: '0.75rem',
                      fontWeight: '500'
                    }}>
                      {projetService.getPrioriteIcon(item._id)} {item._id}
                    </span>
                  </div>
                  <span style={{
                    fontSize: '1rem',
                    fontWeight: '600',
                    color: '#1f2937'
                  }}>
                    {item.count}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Budget global */}
        {stats.budget && (
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '1.5rem',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
          }}>
            <h4 style={{
              margin: '0 0 1rem 0',
              fontSize: '1.125rem',
              fontWeight: '600',
              color: '#1f2937',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              <FaEuroSign style={{ color: '#f59e0b' }} />
              Budget global
            </h4>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>
                  Budget total alloué
                </span>
                <span style={{
                  fontSize: '1.25rem',
                  fontWeight: '600',
                  color: '#1f2937'
                }}>
                  {projetService.formatBudget(stats.budget.budgetTotalAlloue)}
                </span>
              </div>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>
                  Budget utilisé
                </span>
                <span style={{
                  fontSize: '1.25rem',
                  fontWeight: '600',
                  color: '#ef4444'
                }}>
                  {projetService.formatBudget(stats.budget.budgetTotalUtilise)}
                </span>
              </div>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingTop: '1rem',
                borderTop: '1px solid #e5e7eb'
              }}>
                <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>
                  Budget restant
                </span>
                <span style={{
                  fontSize: '1.25rem',
                  fontWeight: '600',
                  color: '#10b981'
                }}>
                  {projetService.formatBudget(stats.budget.budgetTotalAlloue - stats.budget.budgetTotalUtilise)}
                </span>
              </div>
              
              {/* Barre de progression du budget */}
              <div style={{ marginTop: '0.5rem' }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '0.5rem'
                }}>
                  <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                    Utilisation du budget
                  </span>
                  <span style={{ fontSize: '0.875rem', fontWeight: '600', color: '#1f2937' }}>
                    {stats.budget.budgetTotalAlloue > 0 
                      ? Math.round((stats.budget.budgetTotalUtilise / stats.budget.budgetTotalAlloue) * 100)
                      : 0
                    }%
                  </span>
                </div>
                <div style={{
                  width: '100%',
                  height: '8px',
                  backgroundColor: '#e5e7eb',
                  borderRadius: '4px',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    width: `${stats.budget.budgetTotalAlloue > 0 
                      ? Math.min(100, (stats.budget.budgetTotalUtilise / stats.budget.budgetTotalAlloue) * 100)
                      : 0
                    }%`,
                    height: '100%',
                    backgroundColor: stats.budget.budgetTotalAlloue > 0 && (stats.budget.budgetTotalUtilise / stats.budget.budgetTotalAlloue) > 0.8
                      ? '#ef4444'
                      : stats.budget.budgetTotalAlloue > 0 && (stats.budget.budgetTotalUtilise / stats.budget.budgetTotalAlloue) > 0.6
                      ? '#f59e0b'
                      : '#10b981',
                    transition: 'width 0.3s ease'
                  }} />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjetStats;
