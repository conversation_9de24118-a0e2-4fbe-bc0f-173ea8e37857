import { useState, useEffect } from 'react';
import { FaUser, FaSignOutAlt, FaIdCard, FaBuilding, FaEnvelope, FaHome, FaCalendarAlt, FaTasks, FaFileAlt, FaBars, FaTimes, FaGraduationCap } from 'react-icons/fa';
import { employeeService } from '../services/employeeService';
import MyInfoModal from '../components/employes/MyInfoModal';
import EmployeeDashboard from '../components/employee/EmployeeDashboard';
import EmployeeProfile from '../components/employee/EmployeeProfile';
import EmployeeLeaves from '../components/employee/EmployeeLeaves';
import EmployeeProjects from '../components/employee/EmployeeProjects';
import EmployeeDocuments from '../components/employee/EmployeeDocuments';
import EmployeeFormations from '../components/employee/EmployeeFormations';
import EmployeeNotificationManager from '../components/notifications/EmployeeNotificationManager';

const Employeepage = () => {
  const [currentUser, setCurrentUser] = useState(null);
  const [currentEmployeeData, setCurrentEmployeeData] = useState(null);
  const [activeTab, setActiveTab] = useState('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Récupérer les informations de l'utilisateur connecté
  useEffect(() => {
    getCurrentUser();
    loadEmployeeData();
  }, []);

  const getCurrentUser = () => {
    const token = localStorage.getItem('token');
    if (token) {
      try {
        // Décoder le token pour récupérer les informations utilisateur
        const payload = JSON.parse(atob(token.split('.')[1]));
        setCurrentUser(payload);
      } catch (error) {
        console.error('Erreur lors du décodage du token:', error);
        setError('Erreur lors de la récupération des informations utilisateur');
      }
    }
  };

  // Charger les données de l'employé connecté
  const loadEmployeeData = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await employeeService.getMyInfo();
      if (response.success) {
        setCurrentEmployeeData(response.data);
      } else {
        setError(response.message || 'Aucune information d\'employé trouvée');
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des informations:', error);
      if (error.message?.includes('404')) {
        setError('Aucune information d\'employé trouvée pour votre compte');
      } else {
        setError('Erreur lors de la récupération de vos informations');
      }
    } finally {
      setLoading(false);
    }
  };

  // Déconnexion
  const handleLogout = () => {
    localStorage.removeItem('token');
    window.location.href = '/Login';
  };

  // Navigation items
  const navigationItems = [
    { id: 'dashboard', label: 'Accueil', icon: FaHome },
    { id: 'formations', label: 'Formations', icon: FaGraduationCap },
    { id: 'leaves', label: 'Congés', icon: FaCalendarAlt },
    { id: 'projects', label: 'Projets', icon: FaTasks },
    { id: 'documents', label: 'Documents', icon: FaFileAlt }
  ];

  // Rendu du contenu selon l'onglet actif
  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <EmployeeDashboard employeeData={currentEmployeeData} />;
      case 'profile':
        return <EmployeeProfile employeeData={currentEmployeeData} />;
      case 'formations':
        return <EmployeeFormations employeeData={currentEmployeeData} />;
      case 'leaves':
        return <EmployeeLeaves employeeData={currentEmployeeData} />;
      case 'projects':
        return <EmployeeProjects employeeData={currentEmployeeData} />;
      case 'documents':
        return <EmployeeDocuments employeeData={currentEmployeeData} />;
      default:
        return <EmployeeDashboard employeeData={currentEmployeeData} />;
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#f5f7fa',
      fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
      display: 'flex'
    }}>
      {/* Gestionnaire de notifications employé (invisible) */}
      <EmployeeNotificationManager />
      {/* Sidebar */}
      <div style={{
        width: sidebarOpen ? '280px' : '80px',
        backgroundColor: 'white',
        boxShadow: '2px 0 10px rgba(0,0,0,0.1)',
        transition: 'width 0.3s ease',
        position: 'fixed',
        height: '100vh',
        zIndex: 1000,
        overflow: 'hidden'
      }}>
        {/* Header du sidebar */}
        <div style={{
          padding: '1.5rem',
          borderBottom: '1px solid #e5e7eb',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          {sidebarOpen && (
            <div>
              <h2 style={{
                margin: '0 0 0.25rem 0',
                fontSize: '1.2rem',
                color: '#374151',
                fontWeight: '700'
              }}>
                Espace Employé
              </h2>
              <p style={{
                margin: 0,
                fontSize: '0.875rem',
                color: '#6b7280'
              }}>
                {currentEmployeeData ? `${currentEmployeeData.prenom} ${currentEmployeeData.nom}` : 'Chargement...'}
              </p>
            </div>
          )}
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            style={{
              backgroundColor: 'transparent',
              border: 'none',
              color: '#6b7280',
              cursor: 'pointer',
              padding: '0.5rem',
              borderRadius: '4px',
              fontSize: '1.2rem'
            }}
          >
            {sidebarOpen ? <FaTimes /> : <FaBars />}
          </button>
        </div>

        {/* Navigation */}
        <nav style={{ padding: '1rem 0' }}>
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeTab === item.id;
            return (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                style={{
                  width: '100%',
                  padding: sidebarOpen ? '0.75rem 1.5rem' : '0.75rem',
                  border: 'none',
                  backgroundColor: isActive ? '#667eea' : 'transparent',
                  color: isActive ? 'white' : '#6b7280',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: sidebarOpen ? '0.75rem' : '0',
                  justifyContent: sidebarOpen ? 'flex-start' : 'center',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  transition: 'all 0.2s ease',
                  borderLeft: isActive ? '4px solid #667eea' : '4px solid transparent'
                }}
                onMouseEnter={(e) => {
                  if (!isActive) {
                    e.target.style.backgroundColor = '#f3f4f6';
                    e.target.style.color = '#374151';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isActive) {
                    e.target.style.backgroundColor = 'transparent';
                    e.target.style.color = '#6b7280';
                  }
                }}
              >
                <Icon style={{ fontSize: '1.1rem' }} />
                {sidebarOpen && item.label}
              </button>
            );
          })}
        </nav>

        {/* Bouton de déconnexion */}
        <div style={{
          position: 'absolute',
          bottom: '1rem',
          left: '1rem',
          right: '1rem'
        }}>
          <button
            onClick={handleLogout}
            style={{
              width: '100%',
              padding: sidebarOpen ? '0.75rem 1rem' : '0.75rem',
              border: '1px solid #dc2626',
              backgroundColor: 'transparent',
              color: '#dc2626',
              cursor: 'pointer',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              gap: sidebarOpen ? '0.5rem' : '0',
              justifyContent: sidebarOpen ? 'flex-start' : 'center',
              fontSize: '0.875rem',
              fontWeight: '500',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = '#dc2626';
              e.target.style.color = 'white';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = 'transparent';
              e.target.style.color = '#dc2626';
            }}
          >
            <FaSignOutAlt />
            {sidebarOpen && 'Déconnexion'}
          </button>
        </div>
      </div>

      {/* Contenu principal */}
      <div style={{
        marginLeft: sidebarOpen ? '280px' : '80px',
        flex: 1,
        transition: 'margin-left 0.3s ease',
        minHeight: '100vh'
      }}>
        {/* Navbar Employee */}
        <nav style={{
          background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
          borderBottom: '1px solid rgba(0, 0, 0, 0.08)',
          height: '4.5rem',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '0 2rem',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
          backdropFilter: 'blur(10px)',
          fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
        }}>
          {/* Left side - Titre et sous-titre */}
          <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
              {/* Icône de la page */}
              <div style={{
                width: '40px',
                height: '40px',
                borderRadius: '10px',
                backgroundColor: '#667eea',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
              }}>
                {activeTab === 'dashboard' && <FaHome style={{ fontSize: '1.2rem' }} />}
                {activeTab === 'profile' && <FaUser style={{ fontSize: '1.2rem' }} />}
                {activeTab === 'formations' && <FaGraduationCap style={{ fontSize: '1.2rem' }} />}
                {activeTab === 'leaves' && <FaCalendarAlt style={{ fontSize: '1.2rem' }} />}
                {activeTab === 'projects' && <FaTasks style={{ fontSize: '1.2rem' }} />}
                {activeTab === 'documents' && <FaFileAlt style={{ fontSize: '1.2rem' }} />}
              </div>

              {/* Titre et sous-titre */}
              <div>
                <h1 style={{
                  margin: 0,
                  fontSize: '1.5rem',
                  fontWeight: '700',
                  color: '#1f2937',
                  lineHeight: '1.2'
                }}>
                  {activeTab === 'dashboard' && 'Mon Espace'}
                  {activeTab === 'profile' && 'Mon Profil'}
                  {activeTab === 'formations' && 'Formations'}
                  {activeTab === 'leaves' && 'Mes Congés'}
                  {activeTab === 'projects' && 'Mes Projets'}
                  {activeTab === 'documents' && 'Mes Documents'}
                </h1>
                <p style={{
                  margin: 0,
                  fontSize: '0.875rem',
                  color: '#6b7280',
                  fontWeight: '500'
                }}>
                  {activeTab === 'dashboard' && 'Tableau de bord personnel'}
                  {activeTab === 'profile' && 'Informations personnelles'}
                  {activeTab === 'formations' && 'Formations disponibles'}
                  {activeTab === 'leaves' && 'Demandes de congés'}
                  {activeTab === 'projects' && 'Projets et tâches'}
                  {activeTab === 'documents' && 'Attestations et documents'}
                </p>
              </div>
            </div>
          </div>

          {/* Right side - Profil uniquement pour Employee */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
            {/* Profil utilisateur */}
            <button
              onClick={() => setActiveTab('profile')}
              style={{
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem',
                padding: '0.5rem',
                borderRadius: '10px',
                transition: 'background-color 0.2s'
              }}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = '#f3f4f6';
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = 'transparent';
              }}
            >
              <div style={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontWeight: '700',
                fontSize: '1rem',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                border: '2px solid rgba(255, 255, 255, 0.2)'
              }}>
                {currentEmployeeData?.prenom?.charAt(0).toUpperCase() || 'E'}
              </div>
              <div style={{ textAlign: 'left' }}>
                <div style={{
                  fontSize: '0.95rem',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.125rem'
                }}>
                  {currentEmployeeData ? `${currentEmployeeData.prenom} ${currentEmployeeData.nom}` : 'Employé'}
                </div>
                <div style={{
                  fontSize: '0.75rem',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px',
                  fontWeight: '500'
                }}>
                  👤 Mo Profil
                </div>
              </div>
            </button>
          </div>
        </nav>

        {/* Message d'erreur */}
        {error && (
          <div style={{
            backgroundColor: '#fef2f2',
            color: '#dc2626',
            padding: '1rem',
            margin: '1rem',
            borderRadius: '8px',
            border: '1px solid #fecaca'
          }}>
            {error}
          </div>
        )}

        {/* Contenu dynamique */}
        <div style={{ marginTop: '0rem' }}>
          {loading ? (
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '50vh',
              fontSize: '1.2rem',
              color: '#6b7280'
            }}>
              Chargement...
            </div>
          ) : (
            renderContent()
          )}
        </div>
      </div>
    </div>
  );
};

export default Employeepage;