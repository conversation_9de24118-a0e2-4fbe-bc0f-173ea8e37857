const mongoose = require('mongoose');

const archiveSchema = new mongoose.Schema({
    // Référence vers le projet archivé
    projet: {
        type: Object,
        required: true
    },
    
    // Informations d'archivage
    raison_archivage: {
        type: String,
        required: true,
        enum: [
            'Projet terminé',
            'Projet annulé', 
            'Projet suspendu',
            'Fin de contrat',
            'Changement de priorités',
            'Budget épuisé',
            'Autre'
        ]
    },
    
    description_archivage: {
        type: String,
        default: ''
    },
    
    date_archivage: {
        type: Date,
        default: Date.now
    },
    
    // Utilisateur qui a archivé
    archive_par: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Utilisateur',
        required: true
    },
    
    // Durée de conservation (en années)
    duree_conservation: {
        type: Number,
        default: 5
    },
    
    // Date d'expiration de l'archive
    date_expiration: {
        type: Date,
        default: function() {
            const now = new Date();
            now.setFullYear(now.getFullYear() + this.duree_conservation);
            return now;
        }
    },
    
    // Métadonnées
    taille_donnees: {
        type: Number, // en bytes
        default: 0
    },
    
    nombre_taches: {
        type: Number,
        default: 0
    },
    
    // Statut de l'archive
    statut_archive: {
        type: String,
        enum: ['Active', 'Restaurée', 'Supprimée'],
        default: 'Active'
    },
    
    // Date de restauration si applicable
    date_restauration: {
        type: Date
    },
    
    // Utilisateur qui a restauré
    restaure_par: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Utilisateur'
    }
}, {
    timestamps: true
});

// Index pour optimiser les recherches
archiveSchema.index({ date_archivage: -1 });
archiveSchema.index({ raison_archivage: 1 });
archiveSchema.index({ statut_archive: 1 });
archiveSchema.index({ 'projet.nom_projet': 'text', 'projet.description': 'text' });

// Méthode pour calculer l'âge de l'archive
archiveSchema.methods.getAge = function() {
    const now = new Date();
    const diffTime = Math.abs(now - this.date_archivage);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
};

// Méthode pour vérifier si l'archive a expiré
archiveSchema.methods.isExpired = function() {
    return new Date() > this.date_expiration;
};

// Méthode pour calculer le temps restant avant expiration
archiveSchema.methods.getTimeUntilExpiration = function() {
    const now = new Date();
    const diffTime = this.date_expiration - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
};

// Middleware pre-save pour calculer les métadonnées
archiveSchema.pre('save', function(next) {
    if (this.projet) {
        // Calculer le nombre de tâches
        this.nombre_taches = this.projet.taches ? this.projet.taches.length : 0;
        
        // Estimer la taille des données (approximation)
        this.taille_donnees = JSON.stringify(this.projet).length;
    }
    next();
});

// Méthode statique pour nettoyer les archives expirées
archiveSchema.statics.cleanupExpiredArchives = async function() {
    const expiredArchives = await this.find({
        date_expiration: { $lt: new Date() },
        statut_archive: 'Active'
    });
    
    // Marquer comme supprimées au lieu de supprimer définitivement
    await this.updateMany(
        { 
            date_expiration: { $lt: new Date() },
            statut_archive: 'Active'
        },
        { 
            statut_archive: 'Supprimée',
            date_suppression: new Date()
        }
    );
    
    return expiredArchives.length;
};

// Méthode statique pour obtenir les statistiques des archives
archiveSchema.statics.getStats = async function() {
    const stats = await this.aggregate([
        {
            $match: { statut_archive: 'Active' }
        },
        {
            $group: {
                _id: null,
                total: { $sum: 1 },
                totalTaches: { $sum: '$nombre_taches' },
                tailleTotale: { $sum: '$taille_donnees' },
                raisonsArchivage: {
                    $push: '$raison_archivage'
                }
            }
        },
        {
            $project: {
                _id: 0,
                total: 1,
                totalTaches: 1,
                tailleTotale: 1,
                raisonsArchivage: 1
            }
        }
    ]);
    
    if (stats.length === 0) {
        return {
            total: 0,
            totalTaches: 0,
            tailleTotale: 0,
            raisonsArchivage: []
        };
    }
    
    const result = stats[0];
    
    // Compter les raisons d'archivage
    const raisonsCount = {};
    result.raisonsArchivage.forEach(raison => {
        raisonsCount[raison] = (raisonsCount[raison] || 0) + 1;
    });
    
    result.raisonsCount = raisonsCount;
    delete result.raisonsArchivage;
    
    return result;
};

module.exports = mongoose.model('Archive', archiveSchema);
