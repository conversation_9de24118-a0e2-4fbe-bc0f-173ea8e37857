import React,{ useState } from 'react';
import axios from 'axios';
import { useAuth } from '../context/authContext.jsx';
import { useNavigate } from 'react-router-dom';


const Login = () => {
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [error, setError] = useState(null);
    const {login} = useAuth();
    const navigate = useNavigate();

    const handleSubmit = async(e) => {
        e.preventDefault();
        // handle login logic here
        //call api
       try{
            const response = await axios.post('http://localhost:4000/api/auth/login', { email, password });
            console.log(response.data.success);
            if(response.data.success)
            {
               login(response.data.utilisateur);
               localStorage.setItem('token', response.data.token);
                    if(response.data.utilisateur.role === 'admin'){
                        navigate('/AdminPage');
                    }else if(response.data.utilisateur.role === 'rh'){
                        navigate('/RHpage');
                    }else {
                        navigate('/employee-page');
                    }
            }

    }   catch(error){
                if(error.response && !error.response.data.success){
                setError(error.response.data.message);
           }
            else{
                setError("server error");
            }
        }
    };

    return (
        <div style={{
            minHeight: '100vh',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: 'linear-gradient(135deg, #ece9ff 0%, #b6b6f7 100%)'
        }}>

            <form
                onSubmit={handleSubmit}
                style={{
                    background: '#fff',
                    padding: '2.5rem 2rem',
                    borderRadius: '12px',
                    boxShadow: '0 4px 24px rgba(0,0,0,0.08)',
                    minWidth: '320px',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '1.5rem'
                }}
            >
                <h2 style={{ margin: 0, textAlign: 'center', color: '#4b4b8f' }}>Connexion</h2>
                {error && <p style={{ color: 'red', fontWeight: 'bold', textAlign: 'center' }}>{error}</p>}
                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                    <label htmlFor="email" style={{ fontWeight: 500, color: '#333' }}>Email</label>
                    <input
                        id="email"
                        type="email"
                        value={email}
                        onChange={e => setEmail(e.target.value)}
                        required
                        style={{
                            padding: '0.75rem',
                            borderRadius: '6px',
                            border: '1px solid #d1d5db',
                            fontSize: '1rem'
                        }}
                        placeholder="Entrez votre email"
                    />
                </div>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                    <label htmlFor="password" style={{ fontWeight: 500, color: '#333' }}>Mot de passe</label>
                    <input
                        id="password"
                        type="password"
                        value={password}
                        onChange={e => setPassword(e.target.value)}
                        required
                        style={{
                            padding: '0.75rem',
                            borderRadius: '6px',
                            border: '1px solid #d1d5db',
                            fontSize: '1rem'
                        }}
                        placeholder="Entrez votre mot de passe"
                    />
                </div>
                <button
                    type="submit"
                    style={{
                        background: 'linear-gradient(90deg, #6a82fb 0%, #fc5c7d 100%)',
                        color: '#fff',
                        padding: '0.75rem',
                        border: 'none',
                        borderRadius: '6px',
                        fontWeight: 600,
                        fontSize: '1rem',
                        cursor: 'pointer',
                        transition: 'background 0.2s'
                    }}
                >
                    Se connecter
                </button>
            </form>
        </div>
    );
}

export default Login