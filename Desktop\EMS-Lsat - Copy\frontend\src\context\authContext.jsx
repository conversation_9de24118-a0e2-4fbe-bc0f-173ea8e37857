import axios from 'axios';
import  { createContext, useState, useContext ,useEffect } from 'react';


const utilisateurContext = createContext();

const AuthContext = ({ children }) => { // Renommé en AuthContext
    const [utilisateur, setUser] = useState(null);
    const [loading, setLoading] = useState(true);

    
    
    useEffect(() => {
       const verifierUtilisateur = async () => {
        try{
            const token = localStorage.getItem('token');
            
            if(token){
            const response = await axios.get('http://localhost:4000/api/auth/verify',{
                headers: {
                    Authorization : `Bearer ${token}`
            },
            });
            // console.log("hna")
            // console.log(response.data.success);
            if (response.data.success){
                setUser(response.data.utilisateur);
            }else{
                setUser(null);
            }
        }else {
            setUser(null);
            // setLoading(false);
        }
       
        }
        catch(error){
            console.error('Erreur lors de la vérification de l\'utilisateur :', error);
            if(error.response && !error.response.data.success)
                {
                   setUser(null);
                }    
        }finally{
            setLoading(false);
        }
       }
        verifierUtilisateur();

    }, []); 




    const login = (utilisateur) => {
        setUser(utilisateur);
    };

    const logout = () => {
        setUser(null);
        localStorage.removeItem("token");
    };

    return (
        <utilisateurContext.Provider value={{ utilisateur, login, logout, loading }}>
            {!loading && children}
        </utilisateurContext.Provider>
    );
};

export const useAuth = () => useContext(utilisateurContext);
export default AuthContext; // Renommé en AuthContext