import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../context/authContext';

/**
 * Hook personnalisé pour gérer les notifications de candidatures de formations
 * Utilisé par les utilisateurs RH pour recevoir les notifications en temps réel
 * des candidatures d'employés aux formations
 */
const useFormationNotifications = () => {
  // États pour gérer les notifications
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [unreadCount, setUnreadCount] = useState(0);

  // Contexte d'authentification pour vérifier le rôle
  const { utilisateur } = useAuth();

  /**
   * Fonction pour récupérer les notifications depuis l'API
   * Utilise l'API formations sur le port 4000
   */
  const fetchNotifications = useCallback(async () => {
    // Vérifier que l'utilisateur est RH ou admin
    if (!utilisateur || !['rh', 'admin'].includes(utilisateur.role)) {
      return;
    }

    try {
      setLoading(true);
      setError('');

      // Appel API pour récupérer les notifications RH
      const response = await fetch('http://localhost:4000/api/formations/notifications', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();

        // Mettre à jour les notifications
        setNotifications(data.notifications || []);

        // Calculer le nombre de notifications non lues (toutes les notifications retournées sont non vues)
        const unread = data.count || 0;
        setUnreadCount(unread);

        console.log(`🔔 ${data.notifications.length} notifications récupérées, ${unread} non lues`);
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Erreur lors du chargement des notifications');
      }
    } catch (error) {
      console.error('❌ Erreur récupération notifications:', error);
      setError('Erreur de connexion');
    } finally {
      setLoading(false);
    }
  }, [utilisateur]);

  /**
   * Fonction pour marquer une notification comme vue
   * @param {string} notificationId - ID de la notification à marquer
   */
  const markAsRead = useCallback(async (notificationId) => {
    try {
      const response = await fetch('http://localhost:4000/api/formations/notifications/marquer-vues', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          candidature_ids: [notificationId]
        })
      });

      if (response.ok) {
        // Mettre à jour localement la notification
        setNotifications(prev =>
          prev.map(notif =>
            notif._id === notificationId
              ? { ...notif, vue_par_rh: true }
              : notif
          )
        );

        // Décrémenter le compteur de non lues
        setUnreadCount(prev => Math.max(0, prev - 1));

        console.log(`👁️ Notification ${notificationId} marquée comme vue`);
      } else {
        console.error('❌ Erreur marquage notification');
      }
    } catch (error) {
      console.error('❌ Erreur marquage notification:', error);
    }
  }, []);

  /**
   * Fonction pour marquer toutes les notifications comme vues
   */
  const markAllAsRead = useCallback(async () => {
    try {
      const response = await fetch('http://localhost:4000/api/formations/notifications/marquer-vues', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
      });

      if (response.ok) {
        // Vider les notifications localement car elles sont maintenant vues
        setNotifications([]);
        setUnreadCount(0);

        console.log('👁️ Toutes les notifications marquées comme vues');
      } else {
        console.error('❌ Erreur marquage toutes notifications');
      }
    } catch (error) {
      console.error('❌ Erreur marquage toutes notifications:', error);
    }
  }, []);

  /**
   * Fonction pour rafraîchir les notifications
   * Utile après une action ou pour un rafraîchissement manuel
   */
  const refreshNotifications = useCallback(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  // Charger les notifications au montage du composant
  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  // Rafraîchir les notifications toutes les 30 secondes (polling)
  useEffect(() => {
    // Seulement si l'utilisateur est RH ou admin
    if (!utilisateur || !['rh', 'admin'].includes(utilisateur.role)) {
      return;
    }

    const interval = setInterval(() => {
      fetchNotifications();
    }, 30000); // 30 secondes

    return () => clearInterval(interval);
  }, [fetchNotifications, utilisateur]);

  // Retourner les données et fonctions utiles
  return {
    notifications,
    loading,
    error,
    unreadCount,
    markAsRead,
    markAllAsRead,
    refreshNotifications,
    // Fonction utilitaire pour vérifier si l'utilisateur peut voir les notifications
    canViewNotifications: utilisateur && ['rh', 'admin'].includes(utilisateur.role)
  };
};

export default useFormationNotifications;
