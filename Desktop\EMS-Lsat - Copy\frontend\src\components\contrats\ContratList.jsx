import React, { useState } from 'react';
import { FaPlus, FaEdit, FaTrash, FaFileContract, Fa<PERSON>ser, <PERSON><PERSON><PERSON><PERSON>ch, Fa<PERSON><PERSON>er, FaCalendarAlt, FaEuroSign } from 'react-icons/fa';
import { useContrats } from '../../hooks/useContrats';
import { useEmployees } from '../../hooks/useEmployees';
import { contratService } from '../../services/contratService';
import AddContratModal from './AddContratModal';
import EditContratModal from './EditContratModal';
import DeleteContratModal from './DeleteContratModal';

const ContratList = () => {
  const { contrats, loading, error, fetchContrats } = useContrats();
  const { employees } = useEmployees();
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedContrat, setSelectedContrat] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [selectedStatut, setSelectedStatut] = useState('');

  const typesContrat = contratService.getTypesContrat();
  const statutsContrat = contratService.getStatutsContrat();

  // Filtrer les contrats selon les critères
  const filteredContrats = contrats.filter(contrat => {
    const matchesSearch = contrat.employe?.nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         contrat.employe?.prenom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         contrat.type_contrat?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         contrat.description?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = !selectedType || contrat.type_contrat === selectedType;
    const matchesStatut = !selectedStatut || contrat.statut === selectedStatut;
    
    return matchesSearch && matchesType && matchesStatut;
  });

  const handleEdit = (contrat) => {
    setSelectedContrat(contrat);
    setShowEditModal(true);
  };

  const handleDelete = (contrat) => {
    setSelectedContrat(contrat);
    setShowDeleteModal(true);
  };

  const handleModalClose = () => {
    setShowAddModal(false);
    setShowEditModal(false);
    setShowDeleteModal(false);
    setSelectedContrat(null);
    fetchContrats(); // Recharger la liste
  };

  const getStatutColor = (statut) => {
    const statutObj = statutsContrat.find(s => s.value === statut);
    return statutObj?.color || '#6b7280';
  };

  const getContratIcon = (type) => {
    switch (type) {
      case 'CDI': return '📄';
      case 'CDD': return '📋';
      case 'Stage': return '🎓';
      case 'Freelance': return '💼';
      case 'Apprentissage': return '🔧';
      case 'Interim': return '⏰';
      default: return '📄';
    }
  };

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px',
        fontSize: '1.2rem',
        color: '#6b7280'
      }}>
        Chargement des contrats...
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        backgroundColor: '#fef2f2',
        border: '1px solid #fecaca',
        borderRadius: '8px',
        padding: '1rem',
        color: '#dc2626'
      }}>
        <strong>Erreur:</strong> {error}
      </div>
    );
  }

  return (
    <div style={{ padding: '1rem' }}>
      {/* En-tête */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        padding: '1.5rem',
        marginBottom: '1.5rem',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '1rem'
        }}>
          <div>
            <h1 style={{
              fontSize: '2rem',
              fontWeight: 'bold',
              color: '#1a202c',
              margin: 0,
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              <FaFileContract style={{ color: '#8b5cf6' }} />
              Gestion des Contrats
            </h1>
            <p style={{ color: '#6b7280', margin: '0.5rem 0 0 0' }}>
              Gérez les contrats de vos employés
            </p>
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            style={{
              backgroundColor: '#8b5cf6',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '0.75rem 1.5rem',
              fontSize: '1rem',
              fontWeight: '500',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              transition: 'background-color 0.2s'
            }}
            onMouseOver={(e) => e.target.style.backgroundColor = '#7c3aed'}
            onMouseOut={(e) => e.target.style.backgroundColor = '#8b5cf6'}
          >
            <FaPlus />
            Ajouter un contrat
          </button>
        </div>

        {/* Filtres */}
        <div style={{ 
          display: 'flex', 
          gap: '1rem', 
          alignItems: 'center',
          flexWrap: 'wrap'
        }}>
          {/* Barre de recherche */}
          <div style={{ position: 'relative', flex: '1', minWidth: '300px' }}>
            <FaSearch style={{
              position: 'absolute',
              left: '12px',
              top: '50%',
              transform: 'translateY(-50%)',
              color: '#9ca3af',
              fontSize: '1rem'
            }} />
            <input
              type="text"
              placeholder="Rechercher un contrat ou employé..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem 0.75rem 0.75rem 2.5rem',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '1rem',
                outline: 'none',
                transition: 'border-color 0.2s'
              }}
              onFocus={(e) => e.target.style.borderColor = '#8b5cf6'}
              onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
            />
          </div>

          {/* Filtre par type */}
          <div style={{ position: 'relative', minWidth: '180px' }}>
            <FaFilter style={{
              position: 'absolute',
              left: '12px',
              top: '50%',
              transform: 'translateY(-50%)',
              color: '#9ca3af',
              fontSize: '0.9rem',
              zIndex: 1
            }} />
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem 0.75rem 0.75rem 2.5rem',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '1rem',
                outline: 'none',
                backgroundColor: 'white',
                cursor: 'pointer'
              }}
            >
              <option value="">Tous les types</option>
              {typesContrat.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          {/* Filtre par statut */}
          <div style={{ position: 'relative', minWidth: '150px' }}>
            <select
              value={selectedStatut}
              onChange={(e) => setSelectedStatut(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '1rem',
                outline: 'none',
                backgroundColor: 'white',
                cursor: 'pointer'
              }}
            >
              <option value="">Tous les statuts</option>
              {statutsContrat.map((statut) => (
                <option key={statut.value} value={statut.value}>
                  {statut.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Liste des contrats */}
      {filteredContrats.length === 0 ? (
        <div style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '3rem',
          textAlign: 'center',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <FaFileContract style={{ fontSize: '3rem', color: '#d1d5db', marginBottom: '1rem' }} />
          <h3 style={{ color: '#6b7280', margin: '0 0 0.5rem 0' }}>
            {searchTerm || selectedType || selectedStatut ? 'Aucun contrat trouvé' : 'Aucun contrat'}
          </h3>
          <p style={{ color: '#9ca3af', margin: 0 }}>
            {searchTerm || selectedType || selectedStatut
              ? 'Essayez de modifier vos filtres'
              : 'Commencez par ajouter votre premier contrat'
            }
          </p>
        </div>
      ) : (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(450px, 1fr))',
          gap: '1.5rem'
        }}>
          {filteredContrats.map((contrat) => (
            <div
              key={contrat._id}
              style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                padding: '1.5rem',
                boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                border: '1px solid #e5e7eb',
                transition: 'transform 0.2s, box-shadow 0.2s'
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 4px 20px rgba(0,0,0,0.15)';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
              }}
            >
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'flex-start',
                marginBottom: '1rem'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem'
                }}>
                  <div style={{
                    width: '50px',
                    height: '50px',
                    borderRadius: '12px',
                    backgroundColor: '#8b5cf6',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '1.5rem'
                  }}>
                    {getContratIcon(contrat.type_contrat)}
                  </div>
                  <div>
                    <h3 style={{
                      margin: 0,
                      fontSize: '1.25rem',
                      fontWeight: '600',
                      color: '#1f2937'
                    }}>
                      {contrat.type_contrat}
                    </h3>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      marginTop: '0.25rem'
                    }}>
                      <FaUser style={{ color: '#6b7280', fontSize: '0.8rem' }} />
                      <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>
                        {contrat.employe?.prenom} {contrat.employe?.nom}
                      </span>
                    </div>
                  </div>
                </div>
                <div style={{ display: 'flex', gap: '0.5rem' }}>
                  <button
                    onClick={() => handleEdit(contrat)}
                    style={{
                      backgroundColor: '#f59e0b',
                      color: 'white',
                      border: 'none',
                      borderRadius: '6px',
                      padding: '0.5rem',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      transition: 'background-color 0.2s'
                    }}
                    onMouseOver={(e) => e.target.style.backgroundColor = '#d97706'}
                    onMouseOut={(e) => e.target.style.backgroundColor = '#f59e0b'}
                    title="Modifier"
                  >
                    <FaEdit />
                  </button>
                  <button
                    onClick={() => handleDelete(contrat)}
                    style={{
                      backgroundColor: '#ef4444',
                      color: 'white',
                      border: 'none',
                      borderRadius: '6px',
                      padding: '0.5rem',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      transition: 'background-color 0.2s'
                    }}
                    onMouseOver={(e) => e.target.style.backgroundColor = '#dc2626'}
                    onMouseOut={(e) => e.target.style.backgroundColor = '#ef4444'}
                    title="Supprimer"
                  >
                    <FaTrash />
                  </button>
                </div>
              </div>

              {/* Informations du contrat */}
              <div style={{ marginBottom: '1rem' }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  marginBottom: '0.5rem'
                }}>
                  <FaCalendarAlt style={{ color: '#6b7280', fontSize: '0.8rem' }} />
                  <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>
                    Du {contratService.formatDate(contrat.date_debut)} 
                    {contrat.date_fin ? ` au ${contratService.formatDate(contrat.date_fin)}` : ' (CDI)'}
                  </span>
                </div>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  marginBottom: '0.5rem'
                }}>
                  <FaEuroSign style={{ color: '#6b7280', fontSize: '0.8rem' }} />
                  <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>
                    {contratService.formatSalaire(contrat.salaire)}
                  </span>
                </div>
                <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                  Durée: {contratService.calculateDuree(contrat.date_debut, contrat.date_fin)}
                </div>
              </div>

              {contrat.description && (
                <p style={{
                  margin: '0 0 1rem 0',
                  color: '#6b7280',
                  lineHeight: '1.5',
                  fontSize: '0.875rem'
                }}>
                  {contrat.description}
                </p>
              )}

              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <span style={{
                  padding: '0.25rem 0.75rem',
                  borderRadius: '12px',
                  backgroundColor: `${getStatutColor(contrat.statut)}20`,
                  color: getStatutColor(contrat.statut),
                  fontSize: '0.75rem',
                  fontWeight: '500'
                }}>
                  {contrat.statut}
                </span>
                
                {contratService.isContratExpirantBientot(contrat) && (
                  <span style={{
                    padding: '0.25rem 0.5rem',
                    borderRadius: '12px',
                    backgroundColor: '#fef3c7',
                    color: '#d97706',
                    fontSize: '0.75rem',
                    fontWeight: '500'
                  }}>
                    ⚠️ Expire bientôt
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Modals */}
      {showAddModal && (
        <AddContratModal
          onClose={handleModalClose}
        />
      )}

      {showEditModal && selectedContrat && (
        <EditContratModal
          contrat={selectedContrat}
          onClose={handleModalClose}
        />
      )}

      {showDeleteModal && selectedContrat && (
        <DeleteContratModal
          contrat={selectedContrat}
          onClose={handleModalClose}
        />
      )}
    </div>
  );
};

export default ContratList;
