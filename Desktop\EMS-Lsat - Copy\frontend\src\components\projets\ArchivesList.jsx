import React, { useState, useEffect } from 'react';
import { FaArchive, FaSearch, FaEye, FaUndo, FaTrash, FaCalendarAlt, FaUser, FaProjectDiagram } from 'react-icons/fa';
import { projetService } from '../../services/projetService';

const ArchivesList = () => {
  const [archives, setArchives] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRaison, setSelectedRaison] = useState('');

  const raisonsArchivage = projetService.getRaisonsArchivage();

  // Charger les archives
  const fetchArchives = async () => {
    setLoading(true);
    setError('');
    
    try {
      const response = await projetService.getArchives();
      if (response.success) {
        setArchives(response.data?.archives || []);
      } else {
        setError(response.message || 'Erreur lors du chargement des archives');
      }
    } catch (error) {
      setError(error.message || 'Erreur lors du chargement des archives');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchArchives();
  }, []);

  // Restaurer un projet archivé
  const handleRestaurer = async (archive) => {
    if (window.confirm(`Êtes-vous sûr de vouloir restaurer le projet "${archive.projet?.nom_projet}" ?`)) {
      try {
        const response = await projetService.restaurerProjet(archive._id);
        if (response.success) {
          await fetchArchives(); // Recharger la liste
          alert('Projet restauré avec succès !');
        } else {
          alert('Erreur lors de la restauration : ' + response.message);
        }
      } catch (error) {
        alert('Erreur lors de la restauration : ' + error.message);
      }
    }
  };

  // Supprimer définitivement une archive
  const handleSupprimerDefinitivement = async (archive) => {
    if (window.confirm(`⚠️ ATTENTION ⚠️\n\nÊtes-vous sûr de vouloir supprimer DÉFINITIVEMENT l'archive du projet "${archive.projet?.nom_projet}" ?\n\nCette action est IRRÉVERSIBLE et supprimera toutes les données du projet.`)) {
      try {
        const response = await projetService.supprimerArchiveDefinitivement(archive._id);
        if (response.success) {
          await fetchArchives(); // Recharger la liste
          alert('Archive supprimée définitivement !');
        } else {
          alert('Erreur lors de la suppression : ' + response.message);
        }
      } catch (error) {
        alert('Erreur lors de la suppression : ' + error.message);
      }
    }
  };

  // Filtrer les archives
  const filteredArchives = archives.filter(archive => {
    const matchesSearch = archive.projet?.nom_projet?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         archive.projet?.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         archive.projet?.client?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         archive.description_archivage?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesRaison = !selectedRaison || archive.raison_archivage === selectedRaison;
    
    return matchesSearch && matchesRaison;
  });

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px',
        fontSize: '1.2rem',
        color: '#6b7280'
      }}>
        Chargement des archives...
      </div>
    );
  }

  return (
    <div style={{ padding: '1rem' }}>
      {/* En-tête */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        padding: '1.5rem',
        marginBottom: '1.5rem',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '1rem'
        }}>
          <div>
            <h1 style={{
              fontSize: '2rem',
              fontWeight: 'bold',
              color: '#1a202c',
              margin: 0,
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              <FaArchive style={{ color: '#6b7280' }} />
              Archives des Projets
            </h1>
            <p style={{ color: '#6b7280', margin: '0.5rem 0 0 0' }}>
              Consultez et gérez les projets archivés
            </p>
          </div>
        </div>

        {/* Filtres */}
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: '2fr 1fr',
          gap: '1rem', 
          alignItems: 'end'
        }}>
          {/* Barre de recherche */}
          <div style={{ position: 'relative' }}>
            <FaSearch style={{
              position: 'absolute',
              left: '12px',
              top: '50%',
              transform: 'translateY(-50%)',
              color: '#9ca3af',
              fontSize: '1rem'
            }} />
            <input
              type="text"
              placeholder="Rechercher dans les archives..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem 0.75rem 0.75rem 2.5rem',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '1rem',
                outline: 'none',
                transition: 'border-color 0.2s'
              }}
              onFocus={(e) => e.target.style.borderColor = '#6b7280'}
              onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
            />
          </div>

          {/* Filtre par raison d'archivage */}
          <div>
            <select
              value={selectedRaison}
              onChange={(e) => setSelectedRaison(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '1rem',
                outline: 'none',
                backgroundColor: 'white',
                cursor: 'pointer'
              }}
            >
              <option value="">Toutes les raisons</option>
              {raisonsArchivage.map((raison) => (
                <option key={raison.value} value={raison.value}>
                  {raison.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Message d'erreur */}
      {error && (
        <div style={{
          backgroundColor: '#fef2f2',
          border: '1px solid #fecaca',
          borderRadius: '8px',
          padding: '1rem',
          marginBottom: '1.5rem',
          color: '#dc2626'
        }}>
          <strong>Erreur:</strong> {error}
        </div>
      )}

      {/* Liste des archives */}
      {filteredArchives.length === 0 ? (
        <div style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '3rem',
          textAlign: 'center',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <FaArchive style={{ fontSize: '3rem', color: '#d1d5db', marginBottom: '1rem' }} />
          <h3 style={{ color: '#6b7280', margin: '0 0 0.5rem 0' }}>
            {searchTerm || selectedRaison ? 'Aucune archive trouvée' : 'Aucune archive'}
          </h3>
          <p style={{ color: '#9ca3af', margin: 0 }}>
            {searchTerm || selectedRaison
              ? 'Essayez de modifier vos filtres'
              : 'Aucun projet n\'a encore été archivé'
            }
          </p>
        </div>
      ) : (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(450px, 1fr))',
          gap: '1.5rem'
        }}>
          {filteredArchives.map((archive) => (
            <div
              key={archive._id}
              style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                padding: '1.5rem',
                boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                border: '1px solid #e5e7eb',
                transition: 'transform 0.2s, box-shadow 0.2s'
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 4px 20px rgba(0,0,0,0.15)';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
              }}
            >
              {/* En-tête de l'archive */}
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'flex-start',
                marginBottom: '1rem'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem'
                }}>
                  <div style={{
                    width: '50px',
                    height: '50px',
                    borderRadius: '12px',
                    backgroundColor: '#6b7280',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '1.5rem'
                  }}>
                    <FaArchive />
                  </div>
                  <div>
                    <h3 style={{
                      margin: 0,
                      fontSize: '1.25rem',
                      fontWeight: '600',
                      color: '#1f2937'
                    }}>
                      {archive.projet?.nom_projet}
                    </h3>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      marginTop: '0.25rem'
                    }}>
                      <FaUser style={{ color: '#6b7280', fontSize: '0.8rem' }} />
                      <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>
                        {archive.projet?.chef_projet?.prenom} {archive.projet?.chef_projet?.nom}
                      </span>
                    </div>
                  </div>
                </div>
                <div style={{ display: 'flex', gap: '0.5rem' }}>
                  <button
                    onClick={() => handleRestaurer(archive)}
                    style={{
                      backgroundColor: '#10b981',
                      color: 'white',
                      border: 'none',
                      borderRadius: '6px',
                      padding: '0.5rem',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      transition: 'background-color 0.2s'
                    }}
                    onMouseOver={(e) => e.target.style.backgroundColor = '#059669'}
                    onMouseOut={(e) => e.target.style.backgroundColor = '#10b981'}
                    title="Restaurer"
                  >
                    <FaUndo />
                  </button>
                  <button
                    onClick={() => handleSupprimerDefinitivement(archive)}
                    style={{
                      backgroundColor: '#ef4444',
                      color: 'white',
                      border: 'none',
                      borderRadius: '6px',
                      padding: '0.5rem',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      transition: 'background-color 0.2s'
                    }}
                    onMouseOver={(e) => e.target.style.backgroundColor = '#dc2626'}
                    onMouseOut={(e) => e.target.style.backgroundColor = '#ef4444'}
                    title="Supprimer définitivement"
                  >
                    <FaTrash />
                  </button>
                </div>
              </div>

              {/* Description du projet */}
              <p style={{
                margin: '0 0 1rem 0',
                color: '#6b7280',
                lineHeight: '1.5',
                fontSize: '0.875rem'
              }}>
                {archive.projet?.description}
              </p>

              {/* Informations d'archivage */}
              <div style={{
                backgroundColor: '#f9fafb',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                padding: '1rem',
                marginBottom: '1rem'
              }}>
                <h4 style={{
                  margin: '0 0 0.5rem 0',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  color: '#374151'
                }}>
                  📦 Informations d'archivage
                </h4>
                <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                  <div style={{ marginBottom: '0.25rem' }}>
                    <strong>Raison:</strong> {archive.raison_archivage}
                  </div>
                  <div style={{ marginBottom: '0.25rem' }}>
                    <strong>Date:</strong> {new Date(archive.date_archivage).toLocaleDateString('fr-FR')}
                  </div>
                  <div style={{ marginBottom: '0.25rem' }}>
                    <strong>Archivé par:</strong> {archive.archive_par?.prenom} {archive.archive_par?.nom}
                  </div>
                  {archive.description_archivage && (
                    <div style={{ marginTop: '0.5rem', fontStyle: 'italic' }}>
                      "{archive.description_archivage}"
                    </div>
                  )}
                </div>
              </div>

              {/* Informations du projet original */}
              <div style={{ marginBottom: '1rem' }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  marginBottom: '0.5rem'
                }}>
                  <FaCalendarAlt style={{ color: '#6b7280', fontSize: '0.8rem' }} />
                  <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>
                    Du {projetService.formatDate(archive.projet?.date_debut)} au {projetService.formatDate(archive.projet?.date_fin_prevue)}
                  </span>
                </div>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  marginBottom: '0.5rem'
                }}>
                  <FaProjectDiagram style={{ color: '#6b7280', fontSize: '0.8rem' }} />
                  <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>
                    Budget: {projetService.formatBudget(archive.projet?.budget_alloue)}
                  </span>
                </div>
                {archive.projet?.client && (
                  <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                    Client: {archive.projet?.client}
                  </div>
                )}
              </div>

              {/* Statut et priorité originaux */}
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <div style={{ display: 'flex', gap: '0.5rem' }}>
                  <span style={{
                    padding: '0.25rem 0.75rem',
                    borderRadius: '12px',
                    backgroundColor: `${projetService.getStatutColor(archive.projet?.statut)}20`,
                    color: projetService.getStatutColor(archive.projet?.statut),
                    fontSize: '0.75rem',
                    fontWeight: '500'
                  }}>
                    {archive.projet?.statut}
                  </span>
                  <span style={{
                    padding: '0.25rem 0.75rem',
                    borderRadius: '12px',
                    backgroundColor: `${projetService.getPrioriteColor(archive.projet?.priorite)}20`,
                    color: projetService.getPrioriteColor(archive.projet?.priorite),
                    fontSize: '0.75rem',
                    fontWeight: '500'
                  }}>
                    {archive.projet?.priorite}
                  </span>
                </div>
                
                <span style={{
                  padding: '0.25rem 0.5rem',
                  borderRadius: '12px',
                  backgroundColor: '#f3f4f6',
                  color: '#6b7280',
                  fontSize: '0.75rem',
                  fontWeight: '500'
                }}>
                  📦 Archivé
                </span>
              </div>

              {/* Informations supplémentaires */}
              <div style={{
                marginTop: '1rem',
                paddingTop: '1rem',
                borderTop: '1px solid #e5e7eb',
                display: 'flex',
                justifyContent: 'space-between',
                fontSize: '0.75rem',
                color: '#6b7280'
              }}>
                <span>
                  {archive.projet?.taches?.length || 0} tâche{(archive.projet?.taches?.length || 0) > 1 ? 's' : ''} archivée{(archive.projet?.taches?.length || 0) > 1 ? 's' : ''}
                </span>
                <span>
                  Progression finale: {archive.projet?.progression || 0}%
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ArchivesList;
