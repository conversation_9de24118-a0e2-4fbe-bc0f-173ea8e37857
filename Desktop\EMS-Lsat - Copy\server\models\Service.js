import mongoose from "mongoose";

const serviceSchema = new mongoose.Schema({
  nom_service: { type: String, required: true },
  description: { type: String },
  responsable: { type: mongoose.Schema.Types.ObjectId, ref: "Employe" },
  departement: { type: mongoose.Schema.Types.ObjectId, ref: "Departement", required: true },
  is_active: { type: Boolean, default: true }
}, { timestamps: true });

// Index composé pour éviter les doublons nom_service + departement
serviceSchema.index({ nom_service: 1, departement: 1 }, { unique: true });

const Service = mongoose.model("Service", serviceSchema);
export default Service;
